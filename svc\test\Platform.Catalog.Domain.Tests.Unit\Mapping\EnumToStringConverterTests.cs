﻿using Aveva.Platform.Catalog.Domain.Mapping;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Mapping
{
    [Trait("Category", "Unit")]
    [Trait("Category", "Domain.Unit")]
    public class EnumToStringConverterTests
    {
        private enum EnumConverterTest
        {
            One,
            Two,
            Three,
        }

        [Fact]
        public void TestEnumToStringConverter()
        {
            // Arrange
            var converter = new EnumToStringConverter<EnumConverterTest>();

            // Act
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            var result = converter.Convert(EnumConverterTest.One, string.Empty, null);
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.

            // Assert
            result.ShouldBe("One");
            result.GetType().ShouldBe(typeof(string));
        }
    }
}