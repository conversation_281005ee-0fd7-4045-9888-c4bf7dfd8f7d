{"version": 1, "isRoot": true, "tools": {"aveva.platform.gateway.sdk.cli": {"version": "2.1.6", "commands": ["aveva-gateway"], "rollForward": false}, "aveva.platform.gateway.tools.openapi": {"version": "1.2.0", "commands": ["generatecrd"], "rollForward": false}, "aveva.platform.gateway.tools.pathtransform": {"version": "1.2.0", "commands": ["generatepublicswagger"], "rollForward": false}, "microsoft.openapi.kiota": {"version": "1.24.3", "commands": ["kiota"], "rollForward": false}, "swashbuckle.aspnetcore.cli": {"version": "8.1.1", "commands": ["swagger"], "rollForward": false}}}