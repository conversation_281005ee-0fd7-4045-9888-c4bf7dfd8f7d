<Project>
  <!-- 
        Shared build properties file - reference: https://docs.microsoft.com/en-us/visualstudio/msbuild/customize-your-build
        Hierarchical if you add this to child Directory.Build.props files:
        <Import Project="$([MSBuild]::GetPathOfFileAbove($(MSBuildThisFile), $(MSBuildThisFileDirectory)..))" />
  -->
  <!-- General Metadata Properties -->
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <TargetLatestRuntimePatch>true</TargetLatestRuntimePatch>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
    <NeutralLanguage>en-US</NeutralLanguage>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <Import Project="$([MSBuild]::GetPathOfFileAbove($(MSBuildThisFile), $(MSBuildThisFileDirectory)..))" />
</Project>