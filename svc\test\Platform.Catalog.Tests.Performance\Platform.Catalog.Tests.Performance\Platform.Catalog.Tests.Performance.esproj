<Project Sdk="Microsoft.VisualStudio.JavaScript.Sdk/0.5.128-alpha">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|AnyCPU">
      <Configuration>Debug</Configuration>
      <Platform>AnyCPU</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleasePolaris|AnyCPU">
      <Configuration>ReleasePolaris</Configuration>
      <Platform>AnyCPU</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|AnyCPU">
      <Configuration>Release</Configuration>
      <Platform>AnyCPU</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup>
    <!--The AssemblyName and OutDir properties are set so that the copyright file is copied to the correct location with the correct name.-->
    <AssemblyName>Platform.Catalog.Tests.Performance</AssemblyName>
    <OutDir>.\</OutDir>
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>
  <PropertyGroup>
    <BuildCommand>npm run start</BuildCommand>
    <PublishAssetsDirectory>dist\</PublishAssetsDirectory>
    <CleanCommand>npm run clean</CleanCommand>
  </PropertyGroup>
  <ItemGroup>
    <Folder Include="src\utils\models\" />
  </ItemGroup>
</Project>