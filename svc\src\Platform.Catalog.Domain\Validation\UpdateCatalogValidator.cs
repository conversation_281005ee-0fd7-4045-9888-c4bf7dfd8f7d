﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Models;
using FluentValidation;
using FluentValidation.Results;

namespace Aveva.Platform.Catalog.Domain.Validation
{
    /// <summary>
    /// Class to validate the ServiceEntry data.
    /// </summary>
    public class UpdateCatalogValidator : BaseCatalogValidator<V1ServiceEntry>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateCatalogValidator"/> class.
        /// T is of type ServiceEntry.
        /// </summary>
        /// <param name="existingEntries">Existing Catalog API entries to validate against.</param>
        /// <param name="skipExistingEntriesValidation">Should existing entries be considered when performing validation.</param>
        public UpdateCatalogValidator(ServiceCollectionResponse? existingEntries, bool skipExistingEntriesValidation)
        {
            if (existingEntries != null)
            {
                ExistingEntries = existingEntries;
            }

            SkipExistingEntriesValidation = skipExistingEntriesValidation;

            ValidationRuleBuilder();
        }

        /// <summary>
        /// Method to validate the newly created or updated ServiceEntry.
        /// </summary>
        /// <param name="context">Fluents ValidationContext.</param>
        /// <param name="cancellation">Cancellation token.</param>
        /// <returns>FluentValidationResult.</returns>
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
        public override async Task<ValidationResult> ValidateAsync(ValidationContext<V1ServiceEntry> context, CancellationToken cancellation = default)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
        {
            var result = Enumerable.Empty<ValidationFailure>();

            if (context == null)
            {
                return new ValidationResult(result);
            }

            if (context.InstanceToValidate == null)
            {
                return new ValidationResult(result);
            }

            result = result.Concat(Validate(context).Errors)
                           .Concat(ValidateLifecycle(context.InstanceToValidate))
                           .Concat(ValidateInstanceMode(context.InstanceToValidate))
                           .Concat(ValidateGeographies(context.InstanceToValidate))
                           .Concat(ValidateApplications(context.InstanceToValidate))
                           .Concat(ValidateDependencies(context.InstanceToValidate))
                           .Concat(ValidateDependenciesExist(context.InstanceToValidate))
                           .Concat(ValidateLegacyProtocol(context.InstanceToValidate))
                           .Concat(ValidateExternalIdentities(context.InstanceToValidate))
                           .Concat(ValidateWebhookProtocol(context.InstanceToValidate));
            return new ValidationResult(result);
        }
    }
#pragma warning restore CS8602 // Dereference of a possibly null reference.
}