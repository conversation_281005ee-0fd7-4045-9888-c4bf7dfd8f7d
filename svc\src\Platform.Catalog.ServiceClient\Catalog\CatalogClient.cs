﻿using System.Net.Http.Json;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Serialization;
using Microsoft.Extensions.Configuration;

namespace Aveva.Platform.Catalog.ServiceClient.Catalog
{
    /// <summary>
    /// Client class to communicate with Catalog API service.
    /// </summary>
    public class CatalogClient : ICatalogClient
    {
        private readonly IConfiguration _config;
        private readonly HttpClient _client;

        /// <summary>
        /// Initializes a new instance of the <see cref="CatalogClient"/> class.
        /// </summary>
        /// <param name="client">Http client.</param>
        /// <param name="configuration">App configuration object.</param>
        public CatalogClient(IConfiguration configuration, HttpClient client)
        {
            ArgumentNullException.ThrowIfNull(client);
            ArgumentNullException.ThrowIfNull(configuration);

            _config = configuration;
            GetEndPoint = _config.GetValue<string>(key: "Apis:CatalogService:GetAllServiceEntries");
            _client = client;
        }

        private string? GetEndPoint { get; set; }

        /// <summary>
        /// Gets all service entries from Catalog API service.
        /// </summary>
        /// <returns>List of ServiceEntries.</returns>
        public async Task<ServiceCollectionResponse?> GetAllAsync()
        {
            using var activity = CatalogTraceSource.OperatorTrace.StartActivity("catalog.serviceentry.get", System.Diagnostics.ActivityKind.Client);
            {
                using var httpRequest = new HttpRequestMessage(HttpMethod.Get, GetEndPoint);
                HttpResponseMessage? response = await _client.SendAsync(httpRequest).ConfigureAwait(false);

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null;
                }

                response.EnsureSuccessStatusCode();
                var catalogsOpsResult = await response.Content.ReadFromJsonAsync<ServiceCollectionResponse>(JsonSerializationOptions.Options).ConfigureAwait(false);
                if (catalogsOpsResult == null)
                {
                    return null;
                }

                return catalogsOpsResult;
            }
        }
    }
}