﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;

namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Api.v2
{
    /// <summary>
    /// Represents a catalog service entry with detailed information about the service and its capabilities.
    /// </summary>
    public record ServiceResponse
    {
        #region Public Properties

        /// <summary>
        /// The unique identifier for this service. This ID is used to reference the service in other operations.
        /// </summary>
        public string? Id { get; init; }

        /// <summary>
        /// The user-friendly name of the service displayed in user interfaces. This is the primary label used to identify the service to end users.
        /// </summary>
        public string? DisplayName { get; init; }

        /// <summary>
        /// A URL pointing to the service's icon image stored in the AVEVA CDN. When using this URL, implement proper cache control to ensure users see updated icons when they change. Browsers should be configured to periodically check for updates rather than caching indefinitely.
        /// </summary>
        public Uri? IconUrl { get; init; }

        /// <summary>
        /// A detailed description of the service explaining its purpose, capabilities, and key features. This text helps users understand what the service does and when to use it.
        /// </summary>
        public string? Description { get; init; }

        /// <summary>
        /// Other services that this service depends on to function properly. Keys represent the dependency identifier, and values contain details about the dependency relationship including cardinality and configuration requirements.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public Dictionary<string, Dependency>? Dependencies { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// Applications associated with this service. Each application represents a discrete component or interface that provides specific functionality within the service.
        /// </summary>
        public List<Application>? Applications { get; init; }

        /// <summary>
        /// Geographic regions where this service can be provisioned. A service may be available in multiple geographies, each representing a distinct region where the service can be deployed.
        /// </summary>
        public List<Geography>? Geographies { get; init; }

        /// <summary>
        /// The classification category for this service. Categories help organize services into logical groups based on their purpose or domain.
        /// </summary>
        public Category? Category { get; init; }

        /// <summary>
        /// The deployment scope of the service. Valid values include:
        /// `Environment` (available in all regions and geographies),
        /// `Geography` (available in all regions within a specific geography),
        /// `Regional` (available in a specific region within a specific geography),
        /// `External` (hosted outside the platform but accessible through platform integration).
        /// </summary>
        public string? HostingType { get; init; }

        /// <summary>
        /// Terms used to categorize and filter the service. Tags provide additional classification beyond the primary category and help users discover related services.
        /// </summary>
        public List<string>? Tags { get; init; }

        /// <summary>
        /// Configuration that determines how service instances are created, managed, and terminated. This includes information about provisioning approach, resource allocation, and integration patterns.
        /// </summary>
        public Lifecycle? Lifecycle { get; init; }

        /// <summary>
        /// Provisioning constraints and availability information for this service. This includes details about service visibility in the catalog, provisioning limits, and approval requirements. The `Enabled` property indicates whether the service is visible in catalog listings, and the `Limit` property specifies the maximum number of instances an account can have (with default value 10 when null).
        /// </summary>
        public ServiceAvailability? Availability { get; init; }

        #endregion Public Properties
    }
}