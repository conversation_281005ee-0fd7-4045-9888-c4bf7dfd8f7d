﻿using System.Diagnostics.CodeAnalysis;
using Aveva.Platform.Authentication.Sdk.KiotaClient.Extensions;
using Aveva.Platform.Authentication.Sdk.S2SCommon.Constants;
using Aveva.Platform.Authentication.Service.Handler.Extensions;
using Aveva.Platform.Catalog.Domain;
using Aveva.Platform.Catalog.ServiceClient.AccountMgmt;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.Common.Framework.Abstractions.Configuration;
using Dapr.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Aveva.Platform.Catalog.ServiceClient.Configuration;

/// <summary>
/// ServiceRegistrationModule class to DI the required service to use this library.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="ServiceRegistrationModule"/> class.
/// </remarks>
/// <param name="configuration">The configuration.</param>
/// <exception cref="ArgumentNullException">configuration is <c>null</c>.</exception>
public sealed class ServiceRegistrationModule(IConfiguration configuration) : IServiceRegistrationModule
{
    #region Private Fields

    private readonly IConfiguration _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    private string? _accountMgmtIdentityId;
    private string? _catalogSvcBaseUrl;
    private string? _catalogEventSvcBaseUrl;
    private string? _catalogIdentityId;
    private string? _catalogEventIdentityId;
    private string? _instanceMgmtIdentityId;
    private bool _overrideAuthenticationForLocalTesting;
    private bool _authenticationTypeDebugDisabled;

    #endregion Private Fields
    #region Public Constructors

    #endregion Public Constructors

    #region Public Methods

    /// <inheritdoc/>
    [SuppressMessage("StyleCop.CSharp.SpacingRules", "SA1010:Opening square brackets should be spaced correctly", Justification = "Conflicts with space should be followed by comma.", Scope = "member", Target = "~M:Aveva.Platform.Catalog.ServiceClient.Configuration.ServiceRegistrationModule.AddServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)")]
    public void AddServices(IServiceCollection services)
    {
        // Load config settings
        _accountMgmtIdentityId = _configuration.GetValue<string>(key: "Apis:AccountMgmtService:serviceIdentityId") ?? throw new ArgumentException("Configuration not found = Apis:AccountMgmtService:serviceIdentityId");
        _catalogIdentityId = _configuration.GetValue<string>(key: "Apis:CatalogService:serviceIdentityId") ?? CatalogConstants.ServiceId;
        _catalogEventIdentityId = _configuration.GetValue<string>(key: "Apis:CatalogEventsService:serviceIdentityId") ?? CatalogConstants.ServiceId;
        _catalogSvcBaseUrl = _configuration.GetValue<string>(key: "Apis:CatalogService:BaseServiceUrl") ?? throw new ArgumentException("Configuration not found = Apis:CatalogService:BaseServiceUrl");
        _catalogEventSvcBaseUrl = _configuration.GetValue<string>(key: "Apis:CatalogEventsService:BaseServiceUrl") ?? throw new ArgumentException("Configuration not found = Apis:CatalogEventsService:BaseServiceUrl");
        _instanceMgmtIdentityId = _configuration.GetValue<string>(key: "Apis:InstanceMgmtService:serviceIdentityId") ?? throw new ArgumentException("Configuration not found = Apis:InstanceMgmtService:serviceIdentityId");
        _overrideAuthenticationForLocalTesting = _configuration.GetValue<bool>(key: "Authentication:OverrideAuthenticationForLocalTesting");
        _authenticationTypeDebugDisabled = _configuration.GetValue<bool>(key: "Authentication:DebugDisabled");

        // Add services for S2S communication.
        if (_overrideAuthenticationForLocalTesting)
        {
            services.AddTransient<InvocationHandler>();
            services.AddHttpClient<ICatalogClient, CatalogClient>((client) =>
            {
                client.BaseAddress = new Uri(_catalogSvcBaseUrl);
            }).AddHttpMessageHandler<InvocationHandler>();

            services.AddHttpClient<ICatalogEventClient, CatalogEventClient>((client) =>
            {
                client.BaseAddress = new Uri(_catalogEventSvcBaseUrl);
            }).AddHttpMessageHandler<InvocationHandler>();
        }
        else
        {
            services.AddHttpClientUsingServiceAuthentication<ICatalogEventClient, CatalogEventClient>(options =>
            {
                options.ServiceId = CatalogConstants.ServiceId;
                options.Authentication.Scopes = [Scope.Ops];
                options.Authentication.Resources = [_catalogEventIdentityId];
                options.Routing.BaseUrl = new Uri(_catalogEventSvcBaseUrl);
                options.Routing.UseDaprCommunication = true;
            });

            services.AddHttpClientUsingServiceAuthentication<ICatalogClient, CatalogClient>(options =>
            {
                options.ServiceId = CatalogConstants.ServiceId;
                options.Authentication.Scopes = [Scope.Ops];
                options.Authentication.Resources = [_catalogIdentityId];
                options.Routing.BaseUrl = new Uri(_catalogSvcBaseUrl);
                options.Routing.UseDaprCommunication = true;
            });
        }

        services.AddClientWithAuthentication<Platform.AccountMgmt.Client.Ops.Client>(options =>
        {
            options.Authentication.Scopes = [Scope.Ops];
            options.Authentication.Resources = [_accountMgmtIdentityId];
            options.ServiceId = CatalogConstants.ServiceId;
            options.Authentication.Type = _authenticationTypeDebugDisabled ? AuthenticationType.DebugDisabled : AuthenticationType.ServiceToService;
        });
        services.AddSingleton<IAccountMgmtClient, AccountMgmtClient>();

        services.AddClientWithAuthentication<InstanceMgmt.Client.Ops.Client>(options =>
        {
            options.Authentication.Scopes = [Scope.Ops];
            options.Authentication.Resources = [_instanceMgmtIdentityId];
            options.ServiceId = CatalogConstants.ServiceId;
            options.Authentication.Type = _authenticationTypeDebugDisabled ? AuthenticationType.DebugDisabled : AuthenticationType.ServiceToService;
        });
        services.AddSingleton<IInstanceMgmtClient, InstanceMgmtClient>();
    }
    #endregion Public Methods
}