﻿using System.Text.Json;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.Serialization;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Serialization;

/// <summary>
/// <see cref="LifeCycleJsonConvertorTests"/> unit tests.
/// </summary>
[Trait("Category", "Unit")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Unit")]
public static class LifeCycleJsonConvertorTests
{
    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeLegacyProtocolOptions()
    {
        // Arrange
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "Legacy",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "SolutionDefinition": "test-solution",
                "Mappings": {
                    "Applications": [
                        {
                            "Name": "test-app",
                            "CapabilityDefinition": "test-cap"
                        }
                    ]
                }
            }
        }
        """;

        // Act
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        // Assert
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Legacy");
        result.ProtocolOptions.ShouldNotBeNull();
        result.ProtocolOptions.ShouldBeOfType<LegacyProtocolOptions>();
        var legacyOptions = (LegacyProtocolOptions)result.ProtocolOptions;
        legacyOptions.SolutionDefinition.ShouldBe("test-solution");
        legacyOptions.Mappings.ShouldNotBeNull();
        legacyOptions.Mappings.Applications.ShouldNotBeNull();
        legacyOptions.Mappings.Applications[0].Name.ShouldBe("test-app");
        legacyOptions.Mappings.Applications[0].CapabilityDefinition.ShouldBe("test-cap");
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeWebhookProtocolOptions()
    {
        // Arrange
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "Webhook",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "WebhookUri": "https://example.com/webhook"
            }
        }
        """;

        // Act
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        // Assert
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Webhook");
        result.ProtocolOptions.ShouldNotBeNull();
        result.ProtocolOptions.ShouldBeOfType<WebhookProtocolOptions>();
        var webhookOptions = (WebhookProtocolOptions)result.ProtocolOptions;
        webhookOptions.WebhookUri.ShouldNotBeNull();
        webhookOptions.WebhookUri!.ToString().ShouldBe("https://example.com/webhook");
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeWithInvalidProtocol()
    {
        // Arrange
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "InvalidProtocol",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "solutionDefinition": "test-solution"
            }
        }
        """;

        // Act
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        // Assert
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("InvalidProtocol");
        result.ProtocolOptions.ShouldBeNull();
    }

    [Fact]
    public static void LifeCycleJsonConvertor_SerializeLegacyProtocolOptions()
    {
        // Arrange
        var lifecycle = new Lifecycle(
            "Catalog",
            "Legacy",
            "test-provider",
            "Shared",
            new LegacyProtocolOptions
            {
                SolutionDefinition = "test-solution",
                Mappings = new LegacyProtocolMappings
                {
                    Applications = new List<LegacyProtocolApplicationMapping>
                    {
                        new LegacyProtocolApplicationMapping
                        {
                            Name = "test-app",
                            CapabilityDefinition = "test-cap",
                        },
                    },
                },
            });

        // Act
        var json = JsonSerializer.Serialize(lifecycle, JsonSerializationOptions.Options);

        // Assert
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Legacy");
        result.ProtocolOptions.ShouldNotBeNull();
        result.ProtocolOptions.ShouldBeOfType<LegacyProtocolOptions>();
        var legacyOptions = (LegacyProtocolOptions)result.ProtocolOptions;
        legacyOptions.SolutionDefinition.ShouldBe("test-solution");
        legacyOptions.Mappings.ShouldNotBeNull();
        legacyOptions.Mappings.Applications.ShouldNotBeNull();
        legacyOptions.Mappings.Applications[0].Name.ShouldBe("test-app");
        legacyOptions.Mappings.Applications[0].CapabilityDefinition.ShouldBe("test-cap");
    }

    [Fact]
    public static void LifeCycleJsonConvertor_SerializeWebhookProtocolOptions()
    {
        // Arrange
        var lifecycle = new Lifecycle(
            "Catalog",
            "Webhook",
            "test-provider",
            "Shared",
            new WebhookProtocolOptions
            {
                WebhookUri = new Uri("https://example.com/webhook"),
            });

        // Act
        var json = JsonSerializer.Serialize(lifecycle, JsonSerializationOptions.Options);

        // Assert
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Webhook");
        result.ProtocolOptions.ShouldNotBeNull();
        result.ProtocolOptions.ShouldBeOfType<WebhookProtocolOptions>();
        var webhookOptions = (WebhookProtocolOptions)result.ProtocolOptions;
        webhookOptions.WebhookUri.ShouldNotBeNull();
        webhookOptions.WebhookUri!.ToString().ShouldBe("https://example.com/webhook");
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeWithNullProtocolOptions()
    {
        // Arrange
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "Webhook",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": null
        }
        """;

        // Act
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        // Assert
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Webhook");
        result.ProtocolOptions.ShouldBeNull();
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeWithMissingProtocolOptions()
    {
        // Arrange
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "Webhook",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared"
        }
        """;

        // Act
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        // Assert
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Webhook");
        result.ProtocolOptions.ShouldBeNull();
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeWithEmptyProtocolOptions()
    {
        // Arrange
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "Webhook",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {}
        }
        """;

        // Act
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        // Assert
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Webhook");
        result.ProtocolOptions.ShouldNotBeNull();
        result.ProtocolOptions.ShouldBeOfType<WebhookProtocolOptions>();
        var webhookOptions = (WebhookProtocolOptions)result.ProtocolOptions;
        webhookOptions.WebhookUri.ShouldBeNull();
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeWithCaseInsensitivePropertyNames()
    {
        // Arrange
        var json = """
        {
            "trigger": "Catalog",
            "protocol": "Webhook",
            "providerId": "test-provider",
            "instanceMode": "Shared",
            "protocolOptions": {
                "webhookUri": "https://example.com/webhook"
            }
        }
        """;

        // Act
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        // Assert
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Webhook");
        result.ProtocolOptions.ShouldNotBeNull();
        result.ProtocolOptions.ShouldBeOfType<WebhookProtocolOptions>();
        var webhookOptions = (WebhookProtocolOptions)result.ProtocolOptions;
        webhookOptions.WebhookUri.ShouldNotBeNull();
        webhookOptions.WebhookUri!.ToString().ShouldBe("https://example.com/webhook");
    }

    [Fact]
    public static void LifeCycleJsonConvertor_SerializeWithNullProtocolOptions()
    {
        // Arrange
        var lifecycle = new Lifecycle(
            "Catalog",
            "Webhook",
            "test-provider",
            "Shared",
            null);

        // Act
        var json = JsonSerializer.Serialize(lifecycle, JsonSerializationOptions.Options);

        // Assert
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Webhook");
        result.ProtocolOptions.ShouldBeNull();
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeWithInvalidWebhookUri()
    {
        // Arrange
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "Webhook",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "WebhookUri": "not-a-valid-uri"
            }
        }
        """;

        // Act
        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        // Assert
        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Webhook");
        result.ProtocolOptions.ShouldNotBeNull();
        result.ProtocolOptions.ShouldBeOfType<WebhookProtocolOptions>();
        var webhookOptions = (WebhookProtocolOptions)result.ProtocolOptions;
        webhookOptions.WebhookUri.ShouldNotBeNull();
        webhookOptions.WebhookUri!.IsAbsoluteUri.ShouldBeFalse();
        webhookOptions.WebhookUri!.ToString().ShouldBe("not-a-valid-uri");
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeWithInvalidUriFormat()
    {
        // Arrange
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "Webhook",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "WebhookUri": "http://[invalid"
            }
        }
        """;

        // Act & Assert
        Should.Throw<JsonException>(() => JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options))
            .Message.ShouldContain("could not be converted to System.Uri");
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeWithMissingProtocol()
    {
        var json = """
    {
        "Trigger": "Catalog",
        "ProviderId": "test-provider",
        "InstanceMode": "Shared",
        "ProtocolOptions": {
            "WebhookUri": "https://example.com/webhook"
        }
    }
    """;

        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        result.ShouldNotBeNull();
        result.Protocol.ShouldBeNull(); // Or default
        result.ProtocolOptions.ShouldBeNull(); // Should skip deserialization
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeWithMismatchedProtocolOptions()
    {
        var json = """
    {
        "Trigger": "Catalog",
        "Protocol": "Webhook",
        "ProviderId": "test-provider",
        "InstanceMode": "Shared",
        "ProtocolOptions": {
            "SolutionDefinition": "test-solution"
        }
    }
    """;

        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Webhook");
        result.ProtocolOptions.ShouldNotBeNull();
        result.ProtocolOptions.ShouldBeOfType<WebhookProtocolOptions>(); // Should not fail, but values will be null/default
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializeUnknownProtocolWithOptions()
    {
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "SomeFutureProtocol",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "someField": "someValue"
            }
        }
        """;

        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("SomeFutureProtocol");
        result.ProtocolOptions.ShouldBeNull(); // Type not in map, so skip
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    public static void LifeCycleJsonConvertor_DeserializeWithEmptyProtocol(string protocolValue)
    {
        var json = $$"""
        {
            "Trigger": "Catalog",
            "Protocol": "{{protocolValue}}",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "WebhookUri": "https://example.com/webhook"
            }
        }
        """;

        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        result.ShouldNotBeNull();
        result.Protocol.ShouldBe(protocolValue);
        result.ProtocolOptions.ShouldBeNull(); // Skip deserialization
    }

    [Theory]
    [InlineData("legacy")]
    [InlineData("LEGACY")]
    [InlineData("Legacy")]
    [InlineData("LeGaCy")]
    public static void LifeCycleJsonConvertor_DeserializeWithDifferentProtocolCasings(string protocolValue)
    {
        var json = $$"""
        {
            "Trigger": "Catalog",
            "Protocol": "{{protocolValue}}",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "SolutionDefinition": "test-solution"
            }
        }
        """;

        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        result.ShouldNotBeNull();
        result.Protocol.ShouldBe(protocolValue);
        result.ProtocolOptions.ShouldBeOfType<LegacyProtocolOptions>();
    }

    [Theory]
    [InlineData("webhook")]
    [InlineData("WEBHOOK")]
    [InlineData("Webhook")]
    [InlineData("WebHook")]
    [InlineData("WeBhOoK")]
    public static void LifeCycleJsonConvertor_DeserializeWebhookWithDifferentProtocolCasings(string protocolValue)
    {
        var json = $$"""
        {
            "Trigger": "Catalog",
            "Protocol": "{{protocolValue}}",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "WebhookUri": "https://example.com/webhook"
            }
        }
        """;

        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        result.ShouldNotBeNull();
        result.Protocol.ShouldBe(protocolValue);
        result.ProtocolOptions.ShouldBeOfType<WebhookProtocolOptions>();
        var webhookOptions = (WebhookProtocolOptions)result.ProtocolOptions;
        webhookOptions.WebhookUri.ShouldNotBeNull();
        webhookOptions.WebhookUri!.ToString().ShouldBe("https://example.com/webhook");
    }

    [Theory]
    [InlineData("Protocol")]
    [InlineData("protocol")]
    [InlineData("PROTOCOL")]
    [InlineData("PrOtOcOl")]
    public static void LifeCycleJsonConvertor_DeserializeWithDifferentProtocolPropertyCasings(string protocolPropertyName)
    {
        var json = $$"""
        {
            "Trigger": "Catalog",
            "{{protocolPropertyName}}": "Legacy",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "SolutionDefinition": "test-solution"
            }
        }
        """;

        var result = JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options);

        result.ShouldNotBeNull();
        result.Protocol.ShouldBe("Legacy");
        result.ProtocolOptions.ShouldBeOfType<LegacyProtocolOptions>();
    }

    [Fact]
    public static void LifeCycleJsonConvertor_SerializesLifecyleAsCamelCase()
    {
        // Arrange
        var lifecycle = new Lifecycle(
            "Catalog",
            "Legacy",
            "test-provider",
            "Shared",
            new LegacyProtocolOptions
            {
                SolutionDefinition = "test-solution",
            });

        // Act
        var json = JsonSerializer.Serialize(lifecycle, JsonSerializationOptions.Options);

        // Assert
        var expectedJson = """{"trigger":"Catalog","protocol":"Legacy","providerId":"test-provider","instanceMode":"Shared","protocolOptions":{"solutionDefinition":"test-solution","mappings":{"geographies":{},"dependencies":{},"applications":[]}},"fulfillmentRequired":false}""";
        json.ShouldBe(expectedJson);
    }

    [Fact]
    public static void LifeCycleJsonConvertor_DeserializesLifecycleFromCamelCase()
    {
        // Arrange - Use the exact same JSON structure as the serialization test
        var expectedJson = """{"trigger":"Catalog","protocol":"Legacy","providerId":"test-provider","instanceMode":"Shared","protocolOptions":{"solutionDefinition":"test-solution","mappings":{"geographies":{},"dependencies":{},"applications":[]}},"fulfillmentRequired":false}""";

        // Act
        var result = JsonSerializer.Deserialize<Lifecycle>(expectedJson, JsonSerializationOptions.Options);

        // Assert - Verify complete Lifecycle object is correctly deserialized
        result.ShouldNotBeNull();
        result.Trigger.ShouldBe("Catalog");
        result.Protocol.ShouldBe("Legacy");
        result.ProviderId.ShouldBe("test-provider");
        result.InstanceMode.ShouldBe("Shared");
        result.FulfillmentRequired.ShouldBe(false);
        result.ProtocolOptions.ShouldBeOfType<LegacyProtocolOptions>();

        var legacyOptions = (LegacyProtocolOptions)result.ProtocolOptions;
        legacyOptions.SolutionDefinition.ShouldBe("test-solution");
        legacyOptions.Mappings.ShouldNotBeNull();
        legacyOptions.Mappings.Applications.ShouldNotBeNull();
        legacyOptions.Mappings.Applications.ShouldBeEmpty();
        legacyOptions.Mappings.Geographies.ShouldNotBeNull();
        legacyOptions.Mappings.Dependencies.ShouldNotBeNull();
    }

    [Fact]
    public static void LifeCycleJsonConvertor_ThrowsOnMalformedJson()
    {
#pragma warning disable JSON001 // Invalid JSON pattern
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "Webhook",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": {
                "WebhookUri": "https://example.com/webhook"
        """; // Missing closing braces
#pragma warning restore JSON001 // Invalid JSON pattern

        Should.Throw<JsonException>(() =>
            JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options));
    }

    [Fact]
    public static void LifeCycleJsonConvertor_FailsIfProtocolOptionsIsUnexpectedType()
    {
        var json = """
        {
            "Trigger": "Catalog",
            "Protocol": "Webhook",
            "ProviderId": "test-provider",
            "InstanceMode": "Shared",
            "ProtocolOptions": 42
        }
        """;

        Should.Throw<JsonException>(() =>
            JsonSerializer.Deserialize<Lifecycle>(json, JsonSerializationOptions.Options));
    }
}