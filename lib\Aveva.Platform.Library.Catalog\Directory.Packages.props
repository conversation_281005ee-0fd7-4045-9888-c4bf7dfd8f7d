<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="Aveva.Platform.Common.Testing.ApiContracts" Version="5.0.39" />
    <PackageVersion Include="Aveva.Ruleset" Version="*******" />
    <PackageVersion Include="Microsoft.Kiota.Abstractions" Version="1.17.3" />
    <PackageVersion Include="Microsoft.Kiota.Http.HttpClientLibrary" Version="1.17.3" />
    <PackageVersion Include="Microsoft.Kiota.Serialization.Form" Version="1.17.3" />
    <PackageVersion Include="Microsoft.Kiota.Serialization.Json" Version="1.17.3" />
    <PackageVersion Include="Microsoft.Kiota.Serialization.Multipart" Version="1.17.3" />
    <PackageVersion Include="Microsoft.Kiota.Serialization.Text" Version="1.17.3" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.0" />
    <PackageVersion Include="PactNet" Version="5.0.0-beta.2" />
    <PackageVersion Include="PactNet.Abstractions" Version="5.0.0-beta.2" />
    <PackageVersion Include="PactNet.Output.Xunit" Version="1.0.0" />
    <PackageVersion Include="VogueDeputy" Version="3.2.0" />
    <PackageVersion Include="xunit" Version="2.9.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageVersion Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>
</Project>