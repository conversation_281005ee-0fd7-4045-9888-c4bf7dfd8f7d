{"name": "connectv2-performance", "description": "Performance-testing framework for Connect V2 clusters, using K6", "keywords": ["k6", "performance", "test", "webpack"], "homepage": "https://dev.azure.com/AVEVA-VSTS/Cloud%20Platform/", "repository": {"type": "git", "url": "https://dev.azure.com/AVEVA-VSTS/Cloud%20Platform/_git/platform-performance"}, "author": {"name": "Industrial Platform SystemTest", "email": "<EMAIL>"}, "dependencies": {"@babel/core": "7.23.7", "@babel/plugin-proposal-class-properties": "7.13.0", "@babel/plugin-proposal-object-rest-spread": "7.13.8", "@babel/preset-env": "7.23.8", "@babel/preset-typescript": "7.23.3", "@platform/aveva.platform.accessmgmt.tsclient": "0.253.5598145", "@platform/aveva.platform.accountmgmt.tsclient": "0.422.5565208", "@platform/aveva.platform.catalog.tsclient": "0.1403.5733382", "@platform/aveva.platform.identity.tsclient": "0.2005.5601604", "@platform/performance-libs": "^1.0.39", "@types/k6": "^0.43.2", "babel-loader": "9.1.3", "clean-webpack-plugin": "4.0.0", "copy-webpack-plugin": "^12.0.2", "copyfiles": "^2.4.1", "ejs": "^3.1.10", "memory-cache": "^0.2.0", "multiple-bundles-webpack-plugin": "^0.2.0", "papaparse": "^5.4.1", "@types/papaparse": "^5.3.14", "rimraf": "^4.4.0", "ts-node": "^10.9.1", "@types/node": "^20.12.7", "ttypescript": "^1.5.13", "typescript": "4.9.5", "typescript-transform-paths": "^3.4.4", "url": "^0.11.4", "webpack": "^5.89.0", "webpack-cli": "5.1.4", "webpack-glob-entries": "^1.0.1"}, "devDependencies": {"@types/memory-cache": "^0.2.5", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.43.0", "eslint": "^8.27.0", "eslint-plugin-check-file": "^2.0.0", "eslint-plugin-import": "^2.27.5", "http-server": "13.0.1", "husky": "^8.0.0", "lint-staged": "^13.0.3", "prettier": "^2.7.1"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "clean-perflib": "rimraf ./node_modules/@platform/performance-libs/node_modules", "build": "npm run transpile", "transform-webpack": "npm run bundle", "post-webpack": "npm run delete-ttsc-output && npm run copy-webpack-tests && rimraf dist/transformed && npm run copy-files && npm run identify-k6-test-files", "start": "npm run clean && npm run clean-perflib && npm run build && npm run transform-webpack && npm run post-webpack", "style-check-all": "prettier --check .", "style-fix-all": "prettier --write --loglevel=warn .", "lint-check-all": "eslint src/", "lint-fix-all": "eslint src/ --fix", "check-all": "npm run lint-check-all && npm run style-check-all", "fix-all": "npm run lint-fix-all && npm run style-fix-all", "check": "lint-staged", "fix": "lint-staged --config .lintstagedrc-fix.json", "tscCompiler": "tsc", "transpile": "ttsc", "copy-files": "npm run copy-json-files && npm run copy-csv-files && npm run copy-graphql-files && npm run copy-order-files && npm run copy-setup-files", "delete-ttsc-output": "rimraf dist/tests", "copy-webpack-tests": "copyfiles -u 2 dist/transformed/tests/**/* dist", "copy-json-files": "copyfiles -u 1 src/tests/**/*.json dist", "copy-csv-files": "copyfiles -u 1 src/tests/**/*.csv dist", "copy-graphql-files": "copyfiles -u 1 src/tests/**/*.graphql dist", "copy-order-files": "copyfiles -u 1 src/tests/**/.order dist", "copy-setup-files": "copyfiles -f ../*PerformanceSetup/performance_setup.csv dist", "identify-k6-test-files": "pwsh identifyK6TestFiles.ps1", "bundle": "webpack"}}