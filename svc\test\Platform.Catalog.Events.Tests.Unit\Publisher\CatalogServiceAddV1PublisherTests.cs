﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Events.Publisher;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.Common.Messaging.EventBus.Abstractions;
using Aveva.Platform.Common.Messaging.EventBus.Events.Catalog;
using Microsoft.Extensions.Logging;
using Moq;
using Polly;
using Polly.Retry;
using Shouldly;

namespace Aveva.Platform.Catalog.Events.Tests.Unit.Publisher
{
    [Trait("Category", "App")]
    [Trait("Category", "Unit")]
    [Trait("Category", "App.Unit")]
    [Trait("Tag", "Publisher")]
    public class CatalogServiceAddV1PublisherTests
    {
#pragma warning disable CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
        #region Test Cases
        private static readonly RetryStrategyOptions _retryStrategyOptions = new RetryStrategyOptions()
        {
            BackoffType = DelayBackoffType.Exponential,
            MaxRetryAttempts = 1,
            Delay = TimeSpan.FromMilliseconds(100),
        };

        private static readonly ResiliencePipeline _retryPipeline = new ResiliencePipelineBuilder()
            .AddRetry(_retryStrategyOptions)
            .Build();
        private Mock<IEventBus> _eventBus = new Mock<IEventBus>();
        private Mock<ICatalogClient> _catalogClient = new Mock<ICatalogClient>();
        private Mock<ILogger> _logger = new Mock<ILogger>();
        private Mock<EventMetrics> _metrics = new Mock<EventMetrics>();

        [Fact]
        public void CatalogServiceAddV1PublisherTests_Initialization_NullException()
        {
            // Act & Assert
            Should.Throw<ArgumentNullException>(() =>
            {
                var eventPublisher = new CatalogServiceAddV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "Us", region: "us", retryPipeline: null, _metrics.Object, _logger.Object);
            });

            Should.Throw<ArgumentNullException>(() =>
            {
                var eventPublisher = new CatalogServiceAddV1Publisher(null, _catalogClient.Object, geography: "Us", region: "us", retryPipeline: _retryPipeline, _metrics.Object, _logger.Object);
            });

            Should.Throw<ArgumentNullException>(() =>
            {
                var eventPublisher = new CatalogServiceAddV1Publisher(_eventBus.Object, null, geography: "Us", region: "us", retryPipeline: _retryPipeline, _metrics.Object, _logger.Object);
            });

            Should.Throw<ArgumentException>(() =>
            {
                var eventPublisher = new CatalogServiceAddV1Publisher(_eventBus.Object, _catalogClient.Object, geography: string.Empty, region: "us", retryPipeline: _retryPipeline, _metrics.Object, _logger.Object);
            });

            Should.Throw<ArgumentException>(() =>
            {
                var eventPublisher = new CatalogServiceAddV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "us", region: string.Empty, retryPipeline: _retryPipeline, _metrics.Object, _logger.Object);
            });

            Should.NotThrow(() =>
            {
                var eventPublisher = new CatalogServiceAddV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "us", region: "us", retryPipeline: _retryPipeline, _metrics.Object, null);
            });
        }

        [Fact]
        public async Task CatalogServiceAddV1PublisherTests_PublishEventAsync_ServiceNotAdded()
        {
            // Arrange
            CatalogServiceAddV1Publisher? serviceEntryCreatePublisher = null;

            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = new List<ServiceResponse>() { new ServiceResponse() { Id = "newlyAddedServiceId" } },
            };
            _catalogClient.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(catalogResponse));
            var serviceEntry = new V1ServiceEntry()
            {
                Id = "unknownId",
            };

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryCreatePublisher = new CatalogServiceAddV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "us", region: "us", retryPipeline: _retryPipeline, _metrics.Object);
                await serviceEntryCreatePublisher!.PublishEventAsync(serviceEntry).ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _catalogClient.Invocations.Count.ShouldBeGreaterThan(0);
            _eventBus.Verify(x => x.PublishAsync(It.IsAny<CatalogServiceAddV1>()), Times.Never);
        }

        [Fact]
        public async Task CatalogServiceAddV1PublisherTests_PublishEventAsync_ServiceAlreadyAdded()
        {
            // Arrange
            CatalogServiceAddV1Publisher? serviceEntryCreatePublisher = null;
            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = new List<ServiceResponse>() { new ServiceResponse() { Id = "newlyAddedServiceId" } },
            };

            _catalogClient.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(catalogResponse));
            var serviceEntry = new V1ServiceEntry()
            {
                Id = "newlyAddedServiceId",
            };

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryCreatePublisher = new CatalogServiceAddV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "us", region: "us", retryPipeline: _retryPipeline, _metrics.Object);
                await serviceEntryCreatePublisher!.PublishEventAsync(serviceEntry).ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _catalogClient.Invocations.Count.ShouldBeGreaterThan(0);
            _eventBus.Verify(x => x.PublishAsync(It.IsAny<CatalogServiceAddV1>()), Times.Once);
        }

        [Fact]
        public async Task CatalogServiceAddV1PublisherTests_PublishEventAsync_WaitForAddingService()
        {
            // Arrange
            CatalogServiceAddV1Publisher? serviceEntryCreatePublisher = null;
            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = new List<ServiceResponse>() { new ServiceResponse() { Id = "existingServiceId" } },
            };

            ServiceCollectionResponse? updatedResponse = new ServiceCollectionResponse()
            {
                Items = new List<ServiceResponse>() { new ServiceResponse() { Id = "newlyAddedServiceId" } },
            };

            var serviceEntry = new V1ServiceEntry()
            {
                Id = "newlyAddedServiceId",
            };

            _catalogClient.SetupSequence(x => x.GetAllAsync())
                .Returns(Task.FromResult(catalogResponse))
                .Returns(Task.FromResult(catalogResponse))
                .Returns(Task.FromResult(updatedResponse));

            RetryStrategyOptions retryStrategyOptions = new RetryStrategyOptions()
            {
                BackoffType = DelayBackoffType.Exponential,
                MaxRetryAttempts = 3,
                Delay = TimeSpan.FromMilliseconds(100),
            };

            ResiliencePipeline retryPipeline = new ResiliencePipelineBuilder()
            .AddRetry(retryStrategyOptions)
            .Build();

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryCreatePublisher = new CatalogServiceAddV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "us", region: "us", retryPipeline: retryPipeline, _metrics.Object);
                await serviceEntryCreatePublisher!.PublishEventAsync(serviceEntry).ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _catalogClient.Invocations.Count.ShouldBe(3);
            _eventBus.Verify(x => x.PublishAsync(It.IsAny<CatalogServiceAddV1>()), Times.Once);
        }
        #endregion
#pragma warning restore CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
    }
}