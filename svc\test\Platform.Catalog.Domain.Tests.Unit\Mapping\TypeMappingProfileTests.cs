﻿using AutoMapper;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Mapping;
using Aveva.Platform.Catalog.Domain.Models;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Mapping
{
    [Trait("Category", "Unit")]
    [Trait("Category", "Domain.Unit")]
    public class TypeMappingProfileTests
    {
        [Fact]
        public void TypeMappingProfile_Maps_Valid()
        {
            // Arrange
            var configuration = new MapperConfiguration(config =>
                config.AddProfile<TypeMappingProfile>());

            // Assert
            configuration.AssertConfigurationIsValid();
        }

        [Fact]
        public void TypeMappingProfile_Maps_V1ServiceEntry_WithLegacyProtocol()
        {
            // Arrange
            var configuration = new MapperConfiguration(config =>
                config.AddProfile<TypeMappingProfile>());

            var mapper = new Mapper(configuration);

            var serviceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
                Category = V1Category.Data,
                HostingType = V1HostingType.Environment,
                Description = "Description",
                Availability = new V1ServiceAvailability()
                {
                    Enabled = true,
                    Limit = 10,
                },
                Lifecycle = new V1Lifecycle()
                {
                    InstanceMode = V1InstanceMode.Isolated,
                    ProviderId = "1",
                    Trigger = V1Trigger.None,
                    Protocol = V1IntegrationProtocol.Legacy,
                    ProtocolOptions = new V1ProtocolOptions
                    {
                        SolutionDefinition = "solutionDefinition1",
                        Mappings = new V1LegacyProtocolMappings
                        {
                            Applications = new List<V1LegacyProtocolMappingsApplication>
                            {
                                new V1LegacyProtocolMappingsApplication
                                {
                                    Name = "app1",
                                    CapabilityDefinition = "capdef1",
                                },
                            },
                            Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>
                            {
                                {
                                    "dep1", new V1LegacyProtocolDependencyMapping
                                    {
                                        IntegrationDefinition = "int1",
                                        SourceContextConfig = "sourcecontext",
                                        TargetContextConfig = "targetcontext",
                                    }
                                },
                            },
                            Geographies = new Dictionary<string, string>
                            {
                                { "default", "geo1" },
                            },
                        },
                    },
                },
                Dependencies = new Dictionary<string, V1CatalogDataDependency>
                {
                    {
                        "test",
                        new V1CatalogDataDependency
                        {
                            Cardinality = V1CatalogDataDependencyCardinality.One,
                            Type = V1CatalogDataDependencyType.Optional,
                            Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                            {
                                {
                                    "config1", new V1CatalogDataDependencyConfig
                                    {
                                        Label = "Label1",
                                        Help = "Help1",
                                        Required = true,
                                        Min = 1,
                                        Max = 255,
                                    }
                                },
                            },
                        }
                    },
                },
            };
            var response = mapper.Map<V1ServiceEntry, ServiceResponse>(serviceEntry);

            // Assert
            response.Id.ShouldBe(serviceEntry.Id);
            response.HostingType.ShouldBe(serviceEntry.HostingType.ToString());
            response.Description.ShouldBe(serviceEntry.Description);
            response.Dependencies!["test"].Cardinality.ToString().ShouldBe(serviceEntry.Dependencies["test"].Cardinality.ToString());
            response.Dependencies!["test"].Type.ToString().ShouldBe(serviceEntry.Dependencies["test"].Type.ToString());
            var responseConfig = response.Dependencies!["test"].Config!["config1"];
            var serviceEntryConfig = serviceEntry.Dependencies["test"].Config!["config1"];
            responseConfig.Label.ShouldBe(serviceEntryConfig.Label);
            responseConfig.Help.ShouldBe(serviceEntryConfig.Help);
            responseConfig.Required.ShouldBe(serviceEntryConfig.Required);
            responseConfig.Min.ShouldBe(serviceEntryConfig.Min);
            responseConfig.Max.ShouldBe(serviceEntryConfig.Max);
            response.Availability?.Enabled.ShouldBe(serviceEntry.Availability.Enabled);
            response.Availability?.Limit.ShouldBe(serviceEntry.Availability.Limit);
            response.Lifecycle.ShouldNotBeNull();
            response.Lifecycle.InstanceMode.ShouldBe(serviceEntry.Lifecycle.InstanceMode.ToString());
            response.Lifecycle.ProviderId.ShouldBe(serviceEntry.Lifecycle.ProviderId);
            response.Lifecycle.Trigger.ShouldBe(serviceEntry.Lifecycle.Trigger.ToString());
            response.Lifecycle.Protocol.ShouldBe(V1IntegrationProtocol.Legacy.ToString());
            response.Lifecycle.ProtocolOptions.ShouldNotBeNull();
            var legacyProtocolOptions = response.Lifecycle.ProtocolOptions as LegacyProtocolOptions;
            var serviceEntryLegacyProtocolOptions = serviceEntry.Lifecycle.ProtocolOptions;
            serviceEntryLegacyProtocolOptions.ShouldNotBeNull();
            serviceEntryLegacyProtocolOptions.Mappings.ShouldNotBeNull();
            serviceEntryLegacyProtocolOptions.Mappings.Applications.ShouldNotBeNull();
            serviceEntryLegacyProtocolOptions.Mappings.Geographies.ShouldNotBeNull();
            serviceEntryLegacyProtocolOptions.Mappings.Dependencies.ShouldNotBeNull();
            legacyProtocolOptions.ShouldNotBeNull();
            legacyProtocolOptions.SolutionDefinition.ShouldBe(serviceEntryLegacyProtocolOptions.SolutionDefinition);
            legacyProtocolOptions.Mappings.ShouldNotBeNull();
            legacyProtocolOptions.Mappings.Applications.ShouldNotBeNull();
            legacyProtocolOptions.ShouldNotBeNull();
            legacyProtocolOptions.SolutionDefinition.ShouldBe(serviceEntryLegacyProtocolOptions!.SolutionDefinition);
            legacyProtocolOptions.Mappings.Applications[0].Name.ShouldBe(serviceEntryLegacyProtocolOptions.Mappings.Applications[0].Name);
            legacyProtocolOptions.Mappings.Applications[0].CapabilityDefinition.ShouldBe(serviceEntryLegacyProtocolOptions.Mappings.Applications[0].CapabilityDefinition);
            legacyProtocolOptions.Mappings.Geographies.ShouldNotBeNull();
            legacyProtocolOptions.Mappings.Geographies.First().Key.ShouldBe(serviceEntryLegacyProtocolOptions.Mappings.Geographies.First().Key);
            legacyProtocolOptions.Mappings.Geographies.First().Value.ShouldBe(serviceEntryLegacyProtocolOptions.Mappings.Geographies.First().Value);
            legacyProtocolOptions.Mappings.Dependencies.ShouldNotBeNull();
            legacyProtocolOptions.Mappings.Dependencies.First().Value.IntegrationDefinition.ShouldBe(serviceEntryLegacyProtocolOptions.Mappings.Dependencies.First().Value.IntegrationDefinition);
            legacyProtocolOptions.Mappings.Dependencies.First().Value.SourceContextConfig.ShouldBe(serviceEntryLegacyProtocolOptions.Mappings.Dependencies.First().Value.SourceContextConfig);
            legacyProtocolOptions.Mappings.Dependencies.First().Value.TargetContextConfig.ShouldBe(serviceEntryLegacyProtocolOptions.Mappings.Dependencies.First().Value.TargetContextConfig);
            legacyProtocolOptions.Mappings.Dependencies.First().Key.ShouldBe(serviceEntryLegacyProtocolOptions.Mappings.Dependencies.First().Key);
        }

        [Fact]
        public void TypeMappingProfile_Maps_V1ServiceEntry_WithWebhookProtocol()
        {
            // Arrange
            var configuration = new MapperConfiguration(config =>
                config.AddProfile<TypeMappingProfile>());

            var mapper = new Mapper(configuration);

            var serviceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
                Category = V1Category.Data,
                HostingType = V1HostingType.Environment,
                Description = "Description",
                Lifecycle = new V1Lifecycle()
                {
                    InstanceMode = V1InstanceMode.Isolated,
                    ProviderId = "1",
                    Trigger = V1Trigger.None,
                    Protocol = V1IntegrationProtocol.Webhook,
                    ProtocolOptions = new V1ProtocolOptions
                    {
                        WebhookUri = new Uri("https://example.com/webhook"),
                    },
                },
                Dependencies = new Dictionary<string, V1CatalogDataDependency>
                {
                    {
                        "test",
                        new V1CatalogDataDependency
                        {
                            Cardinality = V1CatalogDataDependencyCardinality.One,
                            Type = V1CatalogDataDependencyType.Optional,
                            Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                            {
                                {
                                    "config1", new V1CatalogDataDependencyConfig
                                    {
                                        Label = "Label1",
                                        Help = "Help1",
                                        Required = true,
                                        Min = 1,
                                        Max = 255,
                                    }
                                },
                            },
                        }
                    },
                },
            };
            var response = mapper.Map<V1ServiceEntry, ServiceResponse>(serviceEntry);

            // Assert
            response.Id.ShouldBe(serviceEntry.Id);
            response.HostingType.ShouldBe(serviceEntry.HostingType.ToString());
            response.Description.ShouldBe(serviceEntry.Description);
            response.Dependencies!["test"].Cardinality.ToString().ShouldBe(serviceEntry.Dependencies["test"].Cardinality.ToString());
            response.Dependencies!["test"].Type.ToString().ShouldBe(serviceEntry.Dependencies["test"].Type.ToString());
            var responseConfig = response.Dependencies!["test"].Config!["config1"];
            var serviceEntryConfig = serviceEntry.Dependencies["test"].Config!["config1"];
            responseConfig.Label.ShouldBe(serviceEntryConfig.Label);
            responseConfig.Help.ShouldBe(serviceEntryConfig.Help);
            responseConfig.Required.ShouldBe(serviceEntryConfig.Required);
            responseConfig.Min.ShouldBe(serviceEntryConfig.Min);
            responseConfig.Max.ShouldBe(serviceEntryConfig.Max);
            response.Lifecycle.ShouldNotBeNull();
            response.Lifecycle.InstanceMode.ShouldBe(serviceEntry.Lifecycle.InstanceMode.ToString());
            response.Lifecycle.ProviderId.ShouldBe(serviceEntry.Lifecycle.ProviderId);
            response.Lifecycle.Trigger.ShouldBe(serviceEntry.Lifecycle.Trigger.ToString());
            response.Lifecycle.Protocol.ShouldBe(V1IntegrationProtocol.Webhook.ToString());
            response.Lifecycle.ProtocolOptions.ShouldNotBeNull();
            var webhookProtocolOptions = response.Lifecycle.ProtocolOptions as WebhookProtocolOptions;
            var serviceEntryWebhookProtocolOptions = serviceEntry.Lifecycle.ProtocolOptions as V1ProtocolOptions;
            serviceEntryWebhookProtocolOptions.ShouldNotBeNull();
            webhookProtocolOptions.ShouldNotBeNull();
            webhookProtocolOptions.WebhookUri.ShouldBe(serviceEntryWebhookProtocolOptions.WebhookUri);
        }

        [Fact]
        public void TypeMappingProfile_Maps_V1ServiceEntry_WithIntegrationEventProtocol()
        {
            // Arrange
            var configuration = new MapperConfiguration(config =>
                config.AddProfile<TypeMappingProfile>());

            var mapper = new Mapper(configuration);

            var serviceEntry = new V1ServiceEntry()
            {
                Id = "testServiceId",
                Category = V1Category.Data,
                HostingType = V1HostingType.Environment,
                Description = "Test service with IntegrationEvent protocol",
                Lifecycle = new V1Lifecycle()
                {
                    InstanceMode = V1InstanceMode.Isolated,
                    ProviderId = "1",
                    Trigger = V1Trigger.None,
                    Protocol = V1IntegrationProtocol.IntegrationEvent,
                    ProtocolOptions = null, // IntegrationEvent doesn't have protocol options
                },
            };

            // Act - IntegrationEvent protocol should work fine
            var response = mapper.Map<V1ServiceEntry, ServiceResponse>(serviceEntry);

            // Assert
            response.ShouldNotBeNull();
            response.Id.ShouldBe(serviceEntry.Id);
            response.Lifecycle.ShouldNotBeNull();
            response.Lifecycle.Protocol.ShouldBe(V1IntegrationProtocol.IntegrationEvent.ToString());
            response.Lifecycle.ProtocolOptions.ShouldBeNull(); // Should be null for IntegrationEvent
        }

        [Fact]
        public void TypeMappingProfile_ReverseMapping_LegacyProtocolOptions_To_V1ProtocolOptions()
        {
            // Arrange
            var configuration = new MapperConfiguration(config =>
                config.AddProfile<TypeMappingProfile>());

            var mapper = new Mapper(configuration);

            var legacyProtocolOptions = new LegacyProtocolOptions
            {
                SolutionDefinition = "test-solution",
                Mappings = new LegacyProtocolMappings
                {
                    Applications = new List<LegacyProtocolApplicationMapping>
                    {
                        new LegacyProtocolApplicationMapping
                        {
                            Name = "test-app",
                            CapabilityDefinition = "test-cap",
                        },
                    },
                    Geographies = new Dictionary<string, string>
                    {
                        { "default", "test-geo" },
                    },
                    Dependencies = new Dictionary<string, LegacyProtocolDependencyMapping>
                    {
                        {
                            "test-dep", new LegacyProtocolDependencyMapping
                            {
                                IntegrationDefinition = "test-int",
                                SourceContextConfig = "source-config",
                                TargetContextConfig = "target-config",
                            }
                        },
                    },
                },
            };

            // Act - This should work with proper reverse mapping
            var result = mapper.Map<LegacyProtocolOptions, V1ProtocolOptions>(legacyProtocolOptions);

            // Assert
            result.ShouldNotBeNull();
            result.SolutionDefinition.ShouldBe("test-solution");
            result.Mappings.ShouldNotBeNull();
            result.Mappings.Applications.ShouldNotBeNull();
            result.Mappings.Applications[0].Name.ShouldBe("test-app");
            result.Mappings.Applications[0].CapabilityDefinition.ShouldBe("test-cap");
            result.Mappings.Geographies.ShouldNotBeNull();
            result.Mappings.Geographies["default"].ShouldBe("test-geo");
            result.Mappings.Dependencies.ShouldNotBeNull();
            result.Mappings.Dependencies["test-dep"].IntegrationDefinition.ShouldBe("test-int");
            result.WebhookUri.ShouldBeNull(); // Should be ignored
        }

        [Fact]
        public void TypeMappingProfile_ReverseMapping_WebhookProtocolOptions_To_V1ProtocolOptions()
        {
            // Arrange
            var configuration = new MapperConfiguration(config =>
                config.AddProfile<TypeMappingProfile>());

            var mapper = new Mapper(configuration);

            var webhookProtocolOptions = new WebhookProtocolOptions
            {
                WebhookUri = new Uri("https://example.com/webhook"),
            };

            // Act - This should work with proper reverse mapping
            var result = mapper.Map<WebhookProtocolOptions, V1ProtocolOptions>(webhookProtocolOptions);

            // Assert
            result.ShouldNotBeNull();
            result.WebhookUri.ShouldBe(new Uri("https://example.com/webhook"));
            result.SolutionDefinition.ShouldBeNull(); // Should be ignored
            result.Mappings.ShouldBeNull(); // Should be ignored
        }
    }
}