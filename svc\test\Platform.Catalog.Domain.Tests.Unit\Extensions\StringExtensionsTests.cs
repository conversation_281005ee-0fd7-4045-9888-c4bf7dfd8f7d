﻿using Aveva.Platform.Catalog.Domain.Extensions;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Extensions;

[Trait("Category", "Unit")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Unit")]
public static class StringExtensionsTests
{
    #region Test Cases

    [Fact]
    public static void StringExtensionsTests_ReturnsNullOrEmpty()
    {
        // Arrange
        string? nullvalue = null;
        string emptyValue = string.Empty;

        // Assert
#pragma warning disable
        nullvalue.Shorten(1).ShouldBeNull();
#pragma warning restore
        emptyValue.Shorten(1).ShouldBe(string.Empty);
    }

    [Fact]
    public static void StringExtensionsTests_TruncatesToMaxLength()
    {
        // Arrange
        string somevalue = "12345678901234567890";

        // Assert
        somevalue.Shorten(15).ShouldBe("123456789012345");
    }

    [Fact]
    public static void StringExtensionsTests_DonotTruncatesForShorterString()
    {
        // Arrange
        string somevalue = "12345678901234567890";

        // Assert
        somevalue.Shorten(35).ShouldBe(somevalue);
    }
    #endregion Test Cases
}