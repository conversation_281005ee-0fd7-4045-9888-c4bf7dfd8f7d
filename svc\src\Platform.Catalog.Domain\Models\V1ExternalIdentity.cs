﻿using System.ComponentModel.DataAnnotations;

namespace Aveva.Platform.Catalog.Domain.Models;

/// <summary>
/// Geography.
/// </summary>
public class V1ExternalIdentity
{
#pragma warning disable CS8618
    /// <summary>
    /// Gets or sets id.
    /// </summary>
    [Required]
    public string Id { get; set; }

    /// <summary>
    /// Gets or sets type.
    /// </summary>
    [Required]
    public V1ExternalIdentityType Type { get; set; }

    /// <summary>
    /// Gets or sets scopes.
    /// </summary>
    [Required]
    #pragma warning disable CA2227 // Collection properties should be read only
    public List<V1Scope> Scopes { get; set; }
    #pragma warning restore CA2227 // Collection properties should be read only
#pragma warning restore CS8618

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        if (obj is not V1ExternalIdentity item)
        {
            return false;
        }

        var result = string.Equals(item.Id, Id, StringComparison.InvariantCultureIgnoreCase);
        result &= V1ExternalIdentityType.Equals(item.Type, Type);

        if (Scopes == null && item.Scopes != null)
        {
            if (item.Scopes.Count > 0)
            {
                return false;
            }
        }

        if (Scopes != null && item.Scopes == null)
        {
            if (Scopes.Count > 0)
            {
                return false;
            }
        }

        if (Scopes != null && item.Scopes != null)
        {
            result = result
                     && (Scopes.Count == item.Scopes.Count)
                     && !(from scope in Scopes
                          let match = item.Scopes.Contains(scope)
                          where !match
                          select scope).Any();
        }

        return result;
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
}