{{- define "deployments.api" }}
- deployment:
    name: "catalog-api"
    automountServiceAccountToken: true 
    dapr:
      enabled: true
      idSuffix: "api"
      appPort: "8080"
      {{ if ne .Values.deploymentMode "pipeline" }}
      includeTracing: true
      {{ end }}
    scaling:
      targetCPUUtilizationPercentage: {{ .Values.api.scaling.cpuUtilizationPercentage }}
      targetMemoryUtilizationPercentage : {{ .Values.api.scaling.memoryUtilizationPercentage }}
      {{- if eq (include "helpers.isStandardCoreOrPerformanceEnv" .) "true" }}
      minReplicas: 3
      {{- else }}
      minReplicas: 1
      {{- end }}
      maxReplicas: {{ .Values.api.scaling.maxReplicas }}
    {{- if .Values.image.pullSecret }}
    imagePullSecrets: {{ .Values.image.pullSecret }}
    {{- end }}
    {{- if eq (include "helpers.isStandardCoreOrPerformanceEnv" .) "true" }}
    disruptionBudget:
      minAvailable: 1
    topologySpreadConstraints:
      - maxSkew: 1
    {{- end }}
    containers:
      - container:
          runAsNonRoot: {{ .Values.api.runAsNonRoot }}
          imageName: {{ .Values.api.image.name | quote }}
          repository: {{ .Values.environment.containerRegistryServer | quote }}
          imagePullPolicy: {{ .Values.api.image.pullPolicy | quote }}
          tag: {{ .Values.buildVariables.tag | quote }}
          port: 8080
          environmentVariables:
            "ASPNETCORE_ENVIRONMENT": {{ eq .Values.deploymentMode "pipeline" | ternary "Production" "Development"  | quote}}
            "ASPNETCORE_URLS": "http://+:8080"
            "Logging__LogLevel__Default": "Information"
            "ContainerName": "{{ .Values.api.image.name }}"
            "Authentication__IdentityDnsZoneName": "{{ .Values.environment.identityDnsZoneName }}"
            "Instrumentation__DebugEnabled": {{ .Values.api.env.Instrumentation__DebugEnabled | quote }}
          {{ if ne .Values.deploymentMode "pipeline" }}
            "Authentication__SkipAuthenticationIfNotSupplied": true
            "Authentication__OverrideAuthenticationForLocalTesting": true
          {{ end }}
          {{ if .Values.api.env.Instrumentation__OtelExporterEndpoint }}
            "Instrumentation__OtelExporterEndpoint": {{ .Values.api.env.Instrumentation__OtelExporterEndpoint | quote }}
          {{ end}}
          livenessProbe:
            httpGet:
              path: "/liveness"
              port: {{ .Values.api.port }}
          {{ if eq .Values.deploymentMode "pipeline" }}
            initialDelaySeconds: 5
          {{ if or (eq .Values.environment.environmentType "development") (eq .Values.environment.environmentType "production") }}
            periodSeconds: 10
          {{ else }}
            periodSeconds: {{ .Values.api.livenessProbe.periodSeconds }}
          {{ end }}
          {{ else }}
            periodSeconds: {{ .Values.api.livenessProbe.periodSeconds }}
          {{ end }}
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: "/readiness"
              port: {{ .Values.api.port }}
          {{ if eq .Values.deploymentMode "pipeline" }}
            initialDelaySeconds: 5
          {{ if or (eq .Values.environment.environmentType "development") (eq .Values.environment.environmentType "production") }}
            periodSeconds: 30
          {{ else }}
            periodSeconds: {{ .Values.api.readinessProbe.periodSeconds }}
          {{ end }}
          {{ else }}
            periodSeconds: {{ .Values.api.readinessProbe.periodSeconds }}
          {{ end }}
            timeoutSeconds: 10
          startupProbe:
            httpGet:
              path: "/startup"
              port: {{ .Values.api.port }}
            initialDelaySeconds: {{ .Values.api.startupProbe.initialDelaySeconds }}
            timeoutSeconds: {{ .Values.api.startupProbe.initialDelaySeconds }}
          cpuRequested: {{ .Values.scaling.cpuRequested }}
          cpuLimit: {{ .Values.scaling.cpuLimit }}
          memoryRequested: {{ .Values.scaling.memoryRequested }}
          memoryLimit: {{ .Values.scaling.memoryLimit }}
{{- end -}}