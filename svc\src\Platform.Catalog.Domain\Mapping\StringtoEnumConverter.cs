﻿using AutoMapper;

namespace Aveva.Platform.Catalog.Domain.Mapping
{
    /// <summary>
    /// Converter.
    /// </summary>
    /// <typeparam name="T">The enum type.</typeparam>
    public class StringToEnumConverter<T> : IValueConverter<string, T> where T : System.Enum
    {
        /// <summary>
        /// Convert string to enum.
        /// </summary>
        /// <returns>Enum value.</returns>
        public T Convert(string sourceMember, ResolutionContext context)
        {
            return (T)Enum.Parse(typeof(T), sourceMember, true);
        }
    }
}