import { Ops as v2CatalogOpsModels } from "@platform/aveva.platform.catalog.tsclient";
import { Api as v2CatalogApiModels } from "@platform/aveva.platform.catalog.tsclient";
import { OpsClient, ApiClient, HttpVerb, RootRequest } from "@platform/performance-libs";
import { opsGetTypedResponse, apiGetTypedResponse } from "@utils/common/clientHelper";

export function v2OpsGetAllServices(client: OpsClient, checkName: string): v2CatalogOpsModels.ServiceCollectionResponse | undefined {
    const request = new RootRequest(HttpVerb.GET, `/catalog/v2/services`);
    return opsGetTypedResponse<v2CatalogOpsModels.ServiceCollectionResponse>(client, request, 200, checkName);
}

export function v2OpsGetServiceWithServiceID(client: OpsClient, checkName: string): v2CatalogOpsModels.ServiceResponse | undefined {
    const request = new RootRequest(HttpVerb.GET, `/catalog/v2/services/catalog`);
    return opsGetTypedResponse<v2CatalogOpsModels.ServiceResponse>(client, request, 200, checkName);
}

export function v2ApiGetAllServices(client: ApiClient, accountId: string, checkName: string): v2CatalogApiModels.ServiceCollectionResponse | undefined {
    const request = new RootRequest(HttpVerb.GET, `/account/${accountId}/catalog/v2/services`);
    return apiGetTypedResponse<v2CatalogApiModels.ServiceCollectionResponse>(client, request, 200, checkName);
}

export function v2ApiGetServicesWithServiceID(client: ApiClient, accountId: string, checkName: string): v2CatalogApiModels.ServiceResponse | undefined {
    const request = new RootRequest(HttpVerb.GET, `/account/${accountId}/catalog/v2/services/catalog`);
    return apiGetTypedResponse<v2CatalogApiModels.ServiceResponse>(client, request, 200, checkName);
}