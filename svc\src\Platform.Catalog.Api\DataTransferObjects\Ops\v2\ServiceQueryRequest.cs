﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Microsoft.AspNetCore.Mvc;

namespace Aveva.Platform.Catalog.Api.DataTransferObjects.Ops.v2;

/// <summary>
/// ServiceQueryRequest for Ops.
/// </summary>
public record ServiceQueryRequest
{
    /// <summary>
    /// Gets accountId.
    /// </summary>
    [FromQuery(Name = "accountId")]
    public string? AccountId { get; init; }

    /// <summary>
    /// Gets category.
    /// </summary>
    [FromQuery(Name = "category")]
    public Category? Category { get; init; }
}