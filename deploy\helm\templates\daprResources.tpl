{{- define "daprResources" }}
daprResources:
- component:
    apiVersion: dapr.io/v1alpha1
    name: environment-pubsub
    spec:
      type:  {{ if eq .Values.deploymentMode "pipeline" }}pubsub.azure.servicebus{{ else }}pubsub.rabbitmq{{ end }}
      version: v1
      metadata:
    {{- if eq .Values.deploymentMode "pipeline" }}
      - name: "namespaceName"
        value: "{{ .Values.environment.globalServiceBusName }}.servicebus.windows.net"
    {{- else }}
      - name: "connectionString"
        secretKeyRef:
          name: catalog-env-secrets
          key: environmentPubSubConnectionString
    {{- end }}
      - name: "consumerID"
        value: "catalog-{{ .Values.geography.name }}"
{{- end -}}