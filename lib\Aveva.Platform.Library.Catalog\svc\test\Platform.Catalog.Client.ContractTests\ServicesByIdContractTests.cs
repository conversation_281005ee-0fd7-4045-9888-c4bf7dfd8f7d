﻿using System.Net;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;
using Microsoft.Kiota.Http.HttpClientLibrary;
using Microsoft.Kiota.Serialization.Json;
using Shouldly;
using Xunit;
using Xunit.Abstractions;
using Pactflow = PactNet.Matchers;

namespace Aveva.Platform.Catalog.ContractTests;

/// <summary>
/// Contract test for Get service by Id endpoint.
/// </summary>
[Trait("Category", "Contract")]
public class ServicesByIdContractTests : IClassFixture<CatalogContractTestFixture>
{
    private readonly CatalogContractTestFixture _fixture;

    public ServicesByIdContractTests(CatalogContractTestFixture fixture, ITestOutputHelper testOutput)
    {
        ArgumentNullException.ThrowIfNull(fixture);

        _fixture = fixture.SetupPact(testOutput) as CatalogContractTestFixture ?? throw new InvalidOperationException();
    }

    [Fact]
    [Trait("Tag", "Pactflow")]
    public async Task GetAsync_ServiceId_NotExists_InCatalog()
    {
        // Arrange
        // Creates an interaction (for Pact contract) and setup the expected mock data
        _fixture.PactBuilder.UponReceiving("A request to get service not exists")
            .WithRequest(HttpMethod.Get, "/ops/catalog/v2/services/testServiceId")
            .WillRespond()
            .WithStatus(HttpStatusCode.NotFound);

        // Pact Interaction verification step
        await _fixture.PactBuilder.VerifyAsync(async ctx =>
        {
            var authProvider = new AnonymousAuthenticationProvider();
            var parseNodeFactory = new JsonParseNodeFactory();
            var serializationWriterFactory = new JsonSerializationWriterFactory();
            var requestAdapter = new HttpClientRequestAdapter(authProvider, parseNodeFactory, serializationWriterFactory, new HttpClient { BaseAddress = ctx.MockServerUri });
            var client = new Client.V2.Ops.Client(requestAdapter);

            // Act
            try
            {
                var result = await client.Ops.Catalog.V2.Services["testServiceId"].GetAsync().ConfigureAwait(true);
            }
            catch (ApiException ex)
            {
                // Assert
                ex.ResponseStatusCode.ShouldBe(404);
            }
        }).ConfigureAwait(true);
    }

    [Fact]
    [Trait("Tag", "Pactflow")]
    public async Task GetAsync_ServiceId_Exists_InCatalog()
    {
        // Arrange
        // Setup the expected response as dynamic type.
        // InstMgmt changes or reformats the response received from Catalog. But Pact test fails if the properties do not match with the Catalog.
        // So we have to use dynamic type to set the expected response and also verify that the CatalogClient is producing the formatted output.
        dynamic catalogService = new
        {
            id = "testServiceId",
            displayName = "Test",
            hostingType = "External",
            lifecycle = new
            {
                trigger = "None",
                protocol = "IntegrationEvent",
                providerId = "testServiceId",
            },
        };
        _fixture.PactBuilder.UponReceiving("A request to get service not exists")
            .WithRequest(HttpMethod.Get, "/ops/catalog/v2/services/testServiceId")
            .WillRespond()
            .WithStatus(HttpStatusCode.OK)
                   .WithJsonBody(new
                   {
                       id = Pactflow.Match.Type(catalogService.id),
                       displayName = Pactflow.Match.Type(catalogService.displayName),
                       hostingType = Pactflow.Match.Type(catalogService.hostingType),
                       lifecycle = Pactflow.Match.Type(catalogService.lifecycle),
                   });

        // Pact Interaction verification step
        await _fixture.PactBuilder.VerifyAsync(async ctx =>
        {
            var authProvider = new AnonymousAuthenticationProvider();
            var parseNodeFactory = new JsonParseNodeFactory();
            var serializationWriterFactory = new JsonSerializationWriterFactory();
            var requestAdapter = new HttpClientRequestAdapter(authProvider, parseNodeFactory, serializationWriterFactory, new HttpClient { BaseAddress = ctx.MockServerUri });
            var client = new Client.V2.Ops.Client(requestAdapter);

            // Act
            var result = await client.Ops.Catalog.V2.Services["testServiceId"].GetAsync().ConfigureAwait(true);

            // Assert
            result.ShouldNotBe(null);
            result?.Id.ShouldBe("testServiceId");
            result?.DisplayName.ShouldBe("Test");
            result?.HostingType.ShouldBe("External");
            result?.Lifecycle?.Trigger.ShouldBe("None");
            result?.Lifecycle?.Protocol.ShouldBe("IntegrationEvent");
            result?.Lifecycle?.ProviderId.ShouldBe("testServiceId");
        }).ConfigureAwait(true);
    }
}