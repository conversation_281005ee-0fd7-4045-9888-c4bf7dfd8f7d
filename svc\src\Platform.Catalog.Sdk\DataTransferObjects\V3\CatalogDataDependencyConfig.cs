﻿namespace Aveva.Platform.Catalog.Sdk.DataTransferObjects.V3;

/// <summary>
/// Model for defining configuration between the dependent service applications.
/// </summary>
public class CatalogDataDependencyConfig
{
    /// <summary>
    /// The label for the config value.
    /// </summary>
    public string? Label { get; init; }

    /// <summary>
    /// The help text for the config value.
    /// </summary>
    public string? Help { get; init; }

    /// <summary>
    /// Whether the config value is required for integration between two services.
    /// </summary>
    public bool Required { get; init; }

    /// <summary>
    /// The minimum length of config value.
    /// </summary>
    public int? Min { get; init; }

    /// <summary>
    /// The maximum length of config value.
    /// </summary>
    public int? Max { get; init; }
}