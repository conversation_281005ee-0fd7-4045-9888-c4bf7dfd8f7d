{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "lib": ["ES2015"], "moduleResolution": "nodenext", "rootDir": "./", "baseUrl": "./", "outDir": "./dist", "declaration": true, "declarationMap": false, "removeComments": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowJs": true, "esModuleInterop": true, "strict": true, "noImplicitAny": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "typeRoots": ["./node_modules/@types"], "types": ["node"]}, "include": ["./**/*.ts", "./**/*.js"], "tsc-alias": {"resolveFullPaths": true, "verbose": false}}