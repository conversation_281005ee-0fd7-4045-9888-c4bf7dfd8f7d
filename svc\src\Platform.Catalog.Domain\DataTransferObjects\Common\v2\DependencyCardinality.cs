﻿using System.Text.Json.Serialization;

namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2
{
    /// <summary>
    /// Specifies whether a catalog service can connect to one (`One`) or many (`Many`) instances of a dependent service.
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum DependencyCardinality
    {
        /// <summary>
        /// The catalog service connects to exactly one instance of the dependent service. This represents a one-to-one relationship between the catalog service and the dependent service. Use this when your service requires a single dedicated instance of the dependent service.
        /// </summary>
        One,

        /// <summary>
        /// The catalog service can connect to multiple instances of the dependent service. This represents a one-to-many relationship between the catalog service and the dependent service. Use this when your service needs to integrate with multiple instances of the same dependent service simultaneously.
        /// </summary>
        Many,
    }
}