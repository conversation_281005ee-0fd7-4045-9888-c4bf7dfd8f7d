﻿using Aveva.Platform.Catalog.Domain.Models;

namespace Aveva.Platform.Catalog.Domain.Contracts
{
    /// <summary>
    /// Interface to implement ICachedCatalog Repository.
    /// </summary>
    public interface IEventPublisher
    {
        /// <summary>
        /// Publish Catalog service generated events.
        /// </summary>
        /// <returns>List of catalog entires.</returns>
        public Task PublishEventAsync(V1ServiceEntry newServiceEntry, V1ServiceEntry? oldServiceEntry = null);
    }
}