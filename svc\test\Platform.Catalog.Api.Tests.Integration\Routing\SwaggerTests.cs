﻿using System.Net;
using System.Text;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.OpenApi.Exceptions;
using Microsoft.OpenApi.Models;
using Microsoft.OpenApi.Readers;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Api.Tests.Integration.Routing;

/// <summary>
/// Swagger endpoint integration test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// <item><see href="https://docs.microsoft.com/en-us/aspnet/core/test/integration-tests">Integration tests in ASP.NET Core</see></item>
/// </list>
/// </remarks>
[Trait("Category", "Integration")]
[Trait("Category", "Api")]
[Trait("Category", "Api.Integration")]
[Trait("Tag", "Swagger")]
public class SwaggerTests : IClassFixture<WebApplicationFactory<Program>>
{
    #region Private Fields

    private readonly HttpClient _httpClient;
    private readonly Uri _swaggerUIUri;

    #endregion Private Fields

    #region Public Constructors

    /// <summary>
    /// Initializes a new instance of the <see cref="SwaggerTests"/> class.
    /// </summary>
    public SwaggerTests(WebApplicationFactory<Program> factory)
    {
        Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Development");
        _httpClient = factory.CreateClient();

        // localhost set up for testing.
        _swaggerUIUri = new Uri($"/swagger/index.html", UriKind.Relative);
    }

    #endregion Public Constructors

    #region Test Cases

    /// <summary>
    /// Tests that Get on Swagger/Open API endpoint returns a valid Open API specification.
    /// </summary>
    /// <returns>An awaitable task.</returns>
    [Theory]
    [InlineData("/swagger/api-v2/swagger.json")]
    [InlineData("/swagger/operations-v2/swagger.json")]
    public async Task Swagger_GetSwaggerJson_Returns_Content(string endpoint)
    {
        // Arrange & Act
        HttpResponseMessage response = await _httpClient.GetAsync(new Uri(endpoint, UriKind.Relative), cancellationToken: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        response.ShouldSatisfyAllConditions(
            () => response.ShouldNotBeNull(),
            () => response.StatusCode.ShouldBe(HttpStatusCode.OK),
            () => response.Content.ShouldNotBeNull(),
            () => response.Content.Headers.ContentType.ShouldNotBeNull(),
            () => response.Content.Headers.ContentType?.MediaType.ShouldBe("application/json"));

        ReadResult? readResult = null;
        try
        {
            using (Stream contentStream = await response.Content.ReadAsStreamAsync(cancellationToken: TestContext.Current.CancellationToken).ConfigureAwait(true))
            {
                readResult = await new OpenApiStreamReader().ReadAsync(contentStream, cancellationToken: TestContext.Current.CancellationToken).ConfigureAwait(true);
            }
        }
        catch (OpenApiException serializationEx)
        {
            // Open API stream reader should not throw but rather return any errors in the Errors property, so this is just a precaution.
            serializationEx.ShouldBeNull($"Unable to deserialize response to Open API / Swagger document type {typeof(OpenApiDocument).FullName}: {serializationEx.Message}");
        }

        // Assert post conditions.
        readResult.ShouldSatisfyAllConditions(
            () => readResult.ShouldNotBeNull(),
            () => readResult!.OpenApiDocument.Info.ShouldNotBeNull(),
            () => readResult!.OpenApiDocument.Info.Title.ShouldNotBeNullOrWhiteSpace(),
            () => readResult!.OpenApiDocument.Info.Version.ShouldNotBeNullOrWhiteSpace(),
            () => readResult!.OpenApiDocument.Paths.ShouldNotBeNull(),
            () => readResult!.OpenApiDocument.Paths.Count.ShouldBeGreaterThan(0),
            () => readResult!.OpenApiDiagnostic.Errors.ShouldBeEmpty(GetOpenApiErrors(readResult.OpenApiDiagnostic)));
    }

    /// <summary>
    /// Tests that Get on Swagger/Open API endpoint returns the Swagger UI.
    /// </summary>
    /// <returns>An awaitable task.</returns>
    [Fact]
    public async Task Swagger_GetSwaggerUI_Returns_Content()
    {
        // Arrange & Act
        HttpResponseMessage response = await _httpClient.GetAsync(_swaggerUIUri, cancellationToken: TestContext.Current.CancellationToken).ConfigureAwait(true);

        // Assert
        response.ShouldSatisfyAllConditions(
            () => response.ShouldNotBeNull(),
            () => response.StatusCode.ShouldBe(HttpStatusCode.OK),
            () => response.Content.ShouldNotBeNull(),
            () => response.Content.Headers.ContentType.ShouldNotBeNull(),
            () => response.Content.Headers.ContentType?.MediaType.ShouldBe("text/html"));

        string content = await response.Content.ReadAsStringAsync(cancellationToken: TestContext.Current.CancellationToken).ConfigureAwait(true);

        content.ShouldSatisfyAllConditions(
            () => content.ShouldContain("<title>Swagger UI</title>"),
            () => content.ShouldContain("<div id=\"swagger-ui\">"));
    }

    #endregion Test Cases

    #region Private Methods

    private static string GetOpenApiErrors(OpenApiDiagnostic diagnostic)
    {
        StringBuilder messageBuilder = new StringBuilder();
        foreach (OpenApiError error in diagnostic.Errors)
        {
            messageBuilder.Append(error.Message);
        }

        return messageBuilder.ToString();
    }

    #endregion Private Methods
}