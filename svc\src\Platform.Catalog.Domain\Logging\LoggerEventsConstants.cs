﻿namespace Aveva.Platform.Catalog.Domain.Logging;

/// <summary>
/// Logging constants such as pre-defined event identifier ranges for this application to use with logging.
/// </summary>
public static class LoggerEventsConstants
{
    #region Public Fields

    /// <summary>
    /// First event identifier in the range of event identifiers reserved for the entry point for the application or service.
    /// </summary>
    public const int ApplicationEventIdRangeStartId = 140000;

    /// <summary>
    /// First event identifier in the range of event identifiers reserved for the API layer of the application or service.
    /// </summary>
    public const int ApiEventIdRangeStartId = ApplicationEventIdRangeStartId + 1000;

    /// <summary>
    /// First event identifier in the range of event identifiers reserved for the domain layer of the application or service.
    /// </summary>
    public const int DomainEventIdRangeStartId = ApiEventIdRangeStartId + 1000;

    /// <summary>
    /// First event identifier in the range of event identifiers reserved for the infrastructure layer of the application or service.
    /// </summary>
    public const int InfrastructureEventIdRangeStartId = DomainEventIdRangeStartId + 1000;

    /// <summary>
    /// First event identifier in the range of event identifiers reserved for the migrator infrastructure layer of the application or service.
    /// </summary>
    public const int OperatorEventIdRangeStartId = InfrastructureEventIdRangeStartId + 1000;

    /// <summary>
    /// Unauthorized access logging.
    /// </summary>
    public const int UnAuthorizedAccess = OperatorEventIdRangeStartId + 1000;

    /// <summary>
    /// First event identifier in the range of event identifiers reserved for the Events layer of the application or service.
    /// </summary>
    public const int ServiceEventsEventRangeId = UnAuthorizedAccess + 1000;

    #endregion Public Fields
}