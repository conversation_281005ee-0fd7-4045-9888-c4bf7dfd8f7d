﻿using Aveva.Platform.Catalog.Domain;
using Aveva.Platform.Catalog.Domain.K8sCustomResources;
using Aveva.Platform.Catalog.Domain.Models;
using k8s.Models;

namespace Aveva.Platform.Catalog.Infrastructure.Entities
{
    /// <summary>
    /// Class to map the schema of ServiceEntry custom resources.
    /// </summary>
    [KubernetesEntity(Group = CatalogConstants.Group, ApiVersion = CatalogConstants.V1, Kind = CatalogConstants.ServiceEntry.Kind, PluralName = CatalogConstants.ServiceEntry.Plural)]
    public class V1K8sServiceEntry : K8sCustomResource<V1ServiceEntry, K8sCustomResourceStatus>
    {
        /// <summary>
        /// Creates new ServiceEntry for catalogService.
        /// </summary>
        /// <param name="serviceEntry">Service entry model.</param>
        /// <param name="namespace">Namespace to create k8s object.</param>
        /// <returns>New service entry kubernetes resource.</returns>
        public static V1K8sServiceEntry Create(V1ServiceEntry serviceEntry, string @namespace)
        {
            ArgumentNullException.ThrowIfNull(serviceEntry);

            return new V1K8sServiceEntry
            {
                Spec = serviceEntry,
                Metadata = new V1ObjectMeta
                {
                    Name = @namespace,
                    NamespaceProperty = @namespace,
                },
                Kind = CatalogConstants.ServiceEntry.Kind,
                ApiVersion = CatalogConstants.ApiV1,
            };
        }

        /// <summary>
        /// Overidden ToString function.
        /// Provides the metadata as a comma separated value.
        /// Can be extended / modified in future as required.
        /// </summary>
        /// <returns>string.</returns>
        public override string ToString()
        {
            var labels = "{";

            if (Metadata.Labels != null)
            {
                foreach (var kvp in Metadata.Labels)
                {
                    labels += kvp.Key + " : " + kvp.Value + ", ";
                }

                labels = labels.TrimEnd(',', ' ') + "}";
            }

            return $"{Metadata.Name} (Labels: {labels})";
        }
    }
}