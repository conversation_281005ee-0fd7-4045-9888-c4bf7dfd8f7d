# To learn more about .editorconfig see https://aka.ms/editorconfigdocs
###########################################
# Core EditorConfig Options               #
###########################################
root = true
# All files
[*]
indent_style = space
# Code files
[*.{cs,csx,vb,vbx}]
indent_size = 4
insert_final_newline = true
charset = utf-8-bom
# XML project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_size = 2
# XML config files
[*.{props,targets,ruleset,config,nuspec,resx,vsixmanifest,vsct}]
indent_size = 2
# JSON files
[*.json]
indent_size = 2
# Shell script files
[*.sh]
end_of_line = lf
indent_size = 2

###########################################
# .NET Coding Conventions                 #
###########################################
[*.{cs,vb}]
# Organize usings
dotnet_sort_system_directives_first = true
# this. preferences
dotnet_style_qualification_for_field = false:suggestion
dotnet_style_qualification_for_property = false:suggestion
dotnet_style_qualification_for_method = false:suggestion
dotnet_style_qualification_for_event = false:suggestion
# Language keywords vs BCL types preferences
dotnet_style_predefined_type_for_locals_parameters_members = true:suggestion
dotnet_style_predefined_type_for_member_access = true:suggestion
# Parentheses preferences
dotnet_style_parentheses_in_arithmetic_binary_operators = always_for_clarity:silent
dotnet_style_parentheses_in_relational_binary_operators = always_for_clarity:silent
dotnet_style_parentheses_in_other_binary_operators = always_for_clarity:silent
dotnet_style_parentheses_in_other_operators = never_if_unnecessary:silent
# Modifier preferences
dotnet_style_require_accessibility_modifiers = for_non_interface_members:silent
dotnet_style_readonly_field = true:suggestion
# Expression-level preferences
dotnet_style_object_initializer = true:suggestion
dotnet_style_collection_initializer = true:suggestion
dotnet_style_explicit_tuple_names = true:suggestion
dotnet_style_null_propagation = true:suggestion
dotnet_style_coalesce_expression = true:suggestion
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:silent
dotnet_style_prefer_inferred_tuple_names = true:suggestion
dotnet_style_prefer_inferred_anonymous_type_member_names = true:suggestion
dotnet_style_prefer_auto_properties = true:silent
dotnet_style_prefer_conditional_expression_over_assignment = true:silent
dotnet_style_prefer_conditional_expression_over_return = true:silent
###########################################
# Naming Conventions                      #
###########################################
# Use PascalCase for constant fields
dotnet_naming_rule.constant_fields_should_be_pascal_case.severity               = suggestion
dotnet_naming_rule.constant_fields_should_be_pascal_case.symbols                = constant_fields
dotnet_naming_rule.constant_fields_should_be_pascal_case.style                  = pascal_case_style
dotnet_naming_symbols.constant_fields.applicable_kinds                          = field, local
dotnet_naming_symbols.constant_fields.applicable_accessibilities                = *
dotnet_naming_symbols.constant_fields.required_modifiers                        = const
dotnet_naming_style.constant_style.capitalization                               = pascal_case
# Use PascalCase for non-private static fields
dotnet_naming_rule.non_private_static_fields_should_be_pascal_case.severity     = suggestion
dotnet_naming_rule.non_private_static_fields_should_be_pascal_case.symbols      = non_private_static_fields
dotnet_naming_rule.non_private_static_fields_should_be_pascal_case.style        = non_private_static_field_style
dotnet_naming_symbols.non_private_static_fields.applicable_kinds                = field
dotnet_naming_symbols.non_private_static_fields.applicable_accessibilities      = public, protected, internal, protected_internal, private_protected
dotnet_naming_symbols.non_private_static_fields.required_modifiers              = static
dotnet_naming_style.non_private_static_field_style.capitalization               = pascal_case
# Use PascalCase for non-private readonly fields
dotnet_naming_rule.non_private_readonly_fields_should_be_pascal_case.severity   = suggestion
dotnet_naming_rule.non_private_readonly_fields_should_be_pascal_case.symbols    = non_private_readonly_fields
dotnet_naming_rule.non_private_readonly_fields_should_be_pascal_case.style      = non_private_readonly_field_style
dotnet_naming_symbols.non_private_readonly_fields.applicable_kinds              = field
dotnet_naming_symbols.non_private_readonly_fields.applicable_accessibilities    = public, protected, internal, protected_internal, private_protected
dotnet_naming_symbols.non_private_readonly_fields.required_modifiers            = readonly
dotnet_naming_style.non_private_readonly_field_style.capitalization             = pascal_case
# Use CamelCase for instance fields and start with _
dotnet_naming_rule.instance_fields_should_be_camel_case.severity                = suggestion
dotnet_naming_rule.instance_fields_should_be_camel_case.symbols                 = instance_fields
dotnet_naming_rule.instance_fields_should_be_camel_case.style                   = instance_field_style
dotnet_naming_symbols.instance_fields.applicable_kinds                          = field
dotnet_naming_style.instance_field_style.capitalization                         = camel_case
dotnet_naming_style.instance_field_style.required_prefix                        = _
# Use CamelCase for locals and parameters
dotnet_naming_rule.locals_should_be_camel_case.severity                         = suggestion
dotnet_naming_rule.locals_should_be_camel_case.symbols                          = locals_and_parameters
dotnet_naming_rule.locals_should_be_camel_case.style                            = camel_case_style
dotnet_naming_symbols.locals_and_parameters.applicable_kinds                    = parameter, local
dotnet_naming_style.camel_case_style.capitalization                             = camel_case
# Use PascalCase for local functions fields
dotnet_naming_rule.local_functions_should_be_pascal_case.severity               = suggestion
dotnet_naming_rule.local_functions_should_be_pascal_case.symbols                = local_functions
dotnet_naming_rule.local_functions_should_be_pascal_case.style                  = local_function_style
dotnet_naming_symbols.local_functions.applicable_kinds                          = local_function
dotnet_naming_style.local_function_style.capitalization                         = pascal_case
# Default Style Definitions
dotnet_naming_rule.members_should_be_pascal_case.severity                       = suggestion
dotnet_naming_rule.members_should_be_pascal_case.symbols                        = all_members
dotnet_naming_rule.members_should_be_pascal_case.style                          = pascal_case_style
dotnet_naming_symbols.all_members.applicable_kinds                              = *
dotnet_naming_style.pascal_case_style.capitalization                            = pascal_case

###########################################
# C# Coding Conventions                   #
###########################################
[*.cs]
# var preferences
csharp_style_var_for_built_in_types = true:silent
csharp_style_var_when_type_is_apparent = true:suggestion
csharp_style_var_elsewhere = true:silent
# Expression-bodied members
csharp_style_expression_bodied_methods = false:none
csharp_style_expression_bodied_constructors = false:none
csharp_style_expression_bodied_operators = false:none
csharp_style_expression_bodied_properties = true:none
csharp_style_expression_bodied_indexers = true:none
csharp_style_expression_bodied_accessors = true:none
# Pattern matching preferences
csharp_style_pattern_matching_over_is_with_cast_check = true:suggestion
csharp_style_pattern_matching_over_as_with_null_check = true:suggestion
# Null-checking preferences
csharp_style_throw_expression = true:suggestion
csharp_style_conditional_delegate_call = true:suggestion
# Modifier preferences
csharp_preferred_modifier_order = public,private,protected,internal,static,extern,new,virtual,abstract,sealed,override,readonly,unsafe,volatile,async:suggestion
# Expression-level preferences
csharp_prefer_braces = true:silent
csharp_style_deconstructed_variable_declaration = true:suggestion
csharp_prefer_simple_default_expression = true:suggestion
csharp_style_pattern_local_over_anonymous_function = true:suggestion
csharp_style_inlined_variable_declaration = true:suggestion
###########################################
# C# Formatting Rules                     #
###########################################
# New line preferences
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_between_query_expression_clauses = true
# Indentation preferences
csharp_indent_block_contents = true
csharp_indent_braces = false
csharp_indent_case_contents = true
csharp_indent_case_contents_when_block = true
csharp_indent_labels = flush_left
csharp_indent_switch_labels = true
# Space preferences
csharp_space_after_cast = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_around_binary_operators = before_and_after
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false
# Wrapping preferences
csharp_preserve_single_line_statements = true
csharp_preserve_single_line_blocks = true
###########################################
# VB Coding Conventions                   #
###########################################
[*.vb]
# Modifier preferences
visual_basic_preferred_modifier_order = Partial,Default,Private,Protected,Public,Friend,NotOverridable,Overridable,MustOverride,Overloads,Overrides,MustInherit,NotInheritable,Static,Shared,Shadows,ReadOnly,WriteOnly,Dim,Const,WithEvents,Widening,Narrowing,Custom,Async:suggestion


###########################################
# OSIsoft Code Style Rules                #
###########################################
[*.{cs,vb}]
dotnet_analyzer_diagnostic.category-security.severity = error
# SA0001: XML comment analysis disabled
dotnet_diagnostic.SA0001.severity = error
# SA0002: Invalid settings file
dotnet_diagnostic.SA0002.severity = error
# SA1000: Keywords should be spaced correctly
dotnet_diagnostic.SA1000.severity = error
# SA1001: Commas should be spaced correctly
dotnet_diagnostic.SA1001.severity = error
# SA1002: Semicolons should be spaced correctly
dotnet_diagnostic.SA1002.severity = error
# SA1003: Symbols should be spaced correctly
dotnet_diagnostic.SA1003.severity = error
# SA1004: Documentation lines should begin with single space
dotnet_diagnostic.SA1004.severity = error
# SA1005: Single line comments should begin with single space
dotnet_diagnostic.SA1005.severity = warning
# SA1006: Preprocessor keywords should not be preceded by space
dotnet_diagnostic.SA1006.severity = error
# SA1007: Operator keyword should be followed by space
dotnet_diagnostic.SA1007.severity = error
# SA1008: Opening parenthesis should be spaced correctly
dotnet_diagnostic.SA1008.severity = error
# SA1009: Closing parenthesis should be spaced correctly
dotnet_diagnostic.SA1009.severity = error
# SA1010: Opening square brackets should be spaced correctly
dotnet_diagnostic.SA1010.severity = error
# SA1011: Closing square brackets should be spaced correctly
dotnet_diagnostic.SA1011.severity = error
# SA1012: Opening braces should be spaced correctly
dotnet_diagnostic.SA1012.severity = error
# SA1013: Closing braces should be spaced correctly
dotnet_diagnostic.SA1013.severity = error
# SA1014: Opening generic brackets should be spaced correctly
dotnet_diagnostic.SA1014.severity = error
# SA1015: Closing generic brackets should be spaced correctly
dotnet_diagnostic.SA1015.severity = error
# SA1016: Opening attribute brackets should be spaced correctly
dotnet_diagnostic.SA1016.severity = error
# SA1017: Closing attribute brackets should be spaced correctly
dotnet_diagnostic.SA1017.severity = error
# SA1018: Nullable type symbols should be spaced correctly
dotnet_diagnostic.SA1018.severity = error
# SA1019: Member access symbols should be spaced correctly
dotnet_diagnostic.SA1019.severity = error
# SA1020: Increment decrement symbols should be spaced correctly
dotnet_diagnostic.SA1020.severity = error
# SA1021: Negative signs should be spaced correctly
dotnet_diagnostic.SA1021.severity = error
# SA1022: Positive signs should be spaced correctly
dotnet_diagnostic.SA1022.severity = error
# SA1023: Dereference and access of symbols should be spaced correctly
dotnet_diagnostic.SA1023.severity = error
# SA1024: Colons Should Be Spaced Correctly
dotnet_diagnostic.SA1024.severity = error
# SA1025: Code should not contain multiple whitespace in a row
dotnet_diagnostic.SA1025.severity = warning
# SA1026: Code should not contain space after new or stackalloc keyword in implicitly typed array allocation
dotnet_diagnostic.SA1026.severity = error
# SA1027: Use tabs correctly
dotnet_diagnostic.SA1027.severity = error
# SA1028: Code should not contain trailing whitespace
dotnet_diagnostic.SA1028.severity = none
# SA1100: Do not prefix calls with base unless local implementation exists
dotnet_diagnostic.SA1100.severity = error
# SA1101: Prefix local calls with this
dotnet_diagnostic.SA1101.severity = none
# SA1102: Query clause should follow previous clause
dotnet_diagnostic.SA1102.severity = error
# SA1103: Query clauses should be on separate lines or all on one line
dotnet_diagnostic.SA1103.severity = error
# SA1104: Query clause should begin on new line when previous clause spans multiple lines
dotnet_diagnostic.SA1104.severity = error
# SA1105: Query clauses spanning multiple lines should begin on own line
dotnet_diagnostic.SA1105.severity = error
# SA1106: Code should not contain empty statements
dotnet_diagnostic.SA1106.severity = error
# SA1107: Code should not contain multiple statements on one line
dotnet_diagnostic.SA1107.severity = error
# SA1108: Block statements should not contain embedded comments
dotnet_diagnostic.SA1108.severity = none
# SA1110: Opening parenthesis or bracket should be on declaration line
dotnet_diagnostic.SA1110.severity = error
# SA1111: Closing parenthesis should be on line of last parameter
dotnet_diagnostic.SA1111.severity = error
# SA1112: Closing parenthesis should be on line of opening parenthesis
dotnet_diagnostic.SA1112.severity = error
# SA1113: Comma should be on the same line as previous parameter
dotnet_diagnostic.SA1113.severity = error
# SA1114: Parameter list should follow declaration
dotnet_diagnostic.SA1114.severity = error
# SA1115: Parameter should follow comma
dotnet_diagnostic.SA1115.severity = error
# SA1116: Split parameters should start on line after declaration
dotnet_diagnostic.SA1116.severity = none
# SA1117: Parameters should be on same line or separate lines
dotnet_diagnostic.SA1117.severity = none
# SA1118: Parameter should not span multiple lines
dotnet_diagnostic.SA1118.severity = none
# SA1119: Statement should not use unnecessary parenthesis
dotnet_diagnostic.SA1119.severity = error
# SA1120: Comments should contain text
dotnet_diagnostic.SA1120.severity = error
# SA1121: Use built-in type alias
dotnet_diagnostic.SA1121.severity = error
# SA1122: Use string.Empty for empty strings
dotnet_diagnostic.SA1122.severity = suggestion
# SA1123: Do not place regions within elements
dotnet_diagnostic.SA1123.severity = none
# SA1124: Do not use regions
dotnet_diagnostic.SA1124.severity = none
# SA1125: Use shorthand for nullable types
dotnet_diagnostic.SA1125.severity = error
# SA1127: Generic type constraints should be on their own line
dotnet_diagnostic.SA1127.severity = none
# SA1128: Put constructor initializers on their own line
dotnet_diagnostic.SA1128.severity = none
# SA1129: Do not use default value type constructor
dotnet_diagnostic.SA1129.severity = none
# SA1130: Use lambda syntax
dotnet_diagnostic.SA1130.severity = error
# SA1131: Use readable conditions
dotnet_diagnostic.SA1131.severity = error
# SA1132: Do not combine fields
dotnet_diagnostic.SA1132.severity = none
# SA1133: Do not combine attributes
dotnet_diagnostic.SA1133.severity = none
# SA1134: Attributes should not share line
dotnet_diagnostic.SA1134.severity = error
# SA1135: Using directives should be qualified
dotnet_diagnostic.SA1135.severity = error
# SA1136: Enum values should be on separate lines
dotnet_diagnostic.SA1136.severity = error
# SA1137: Elements should have the same indentation
dotnet_diagnostic.SA1137.severity = error
# SA1139: Use literal suffix notation instead of casting
dotnet_diagnostic.SA1139.severity = error
# SA1200: Using directives should be placed correctly
dotnet_diagnostic.SA1200.severity = none
# SA1201: Elements should appear in the correct order
dotnet_diagnostic.SA1201.severity = warning
# SA1202: Elements should be ordered by access
dotnet_diagnostic.SA1202.severity = warning
# SA1203: Constants should appear before fields
dotnet_diagnostic.SA1203.severity = warning
# SA1204: Static elements should appear before instance elements
dotnet_diagnostic.SA1204.severity = warning
# SA1205: Partial elements should declare access
dotnet_diagnostic.SA1205.severity = error
# SA1206: Declaration keywords should follow order
dotnet_diagnostic.SA1206.severity = error
# SA1207: Protected should come before internal
dotnet_diagnostic.SA1207.severity = error
# SA1208: System using directives should be placed before other using directives
dotnet_diagnostic.SA1208.severity = error
# SA1209: Using alias directives should be placed after other using directives
dotnet_diagnostic.SA1209.severity = error
# SA1210: Using directives should be ordered alphabetically by namespace
dotnet_diagnostic.SA1210.severity = error
# SA1211: Using alias directives should be ordered alphabetically by alias name
dotnet_diagnostic.SA1211.severity = error
# SA1212: Property accessors should follow order
dotnet_diagnostic.SA1212.severity = error
# SA1213: Event accessors should follow order
dotnet_diagnostic.SA1213.severity = error
# SA1214: Readonly fields should appear before non-readonly fields
dotnet_diagnostic.SA1214.severity = error
# SA1216: Using static directives should be placed at the correct location.
dotnet_diagnostic.SA1216.severity = error
# SA1217: Using static directives should be ordered alphabetically
dotnet_diagnostic.SA1217.severity = error
# SA1300: Element should begin with upper-case letter
dotnet_diagnostic.SA1300.severity = error
# SA1301: Element should begin with lower-case letter
dotnet_diagnostic.SA1301.severity = none
# SA1302: Interface names should begin with I
dotnet_diagnostic.SA1302.severity = error
# SA1303: Const field names should begin with upper-case letter
dotnet_diagnostic.SA1303.severity = error
# SA1304: Non-private readonly fields should begin with upper-case letter
dotnet_diagnostic.SA1304.severity = error
# SA1305: Field names should not use Hungarian notation
dotnet_diagnostic.SA1305.severity = error
# SA1306: Field names should begin with lower-case letter
dotnet_diagnostic.SA1306.severity = error
# SA1307: Accessible fields should begin with upper-case letter
dotnet_diagnostic.SA1307.severity = error
# SA1308: Variable names should not be prefixed
dotnet_diagnostic.SA1308.severity = error
# SA1309: Field names should not begin with underscore
dotnet_diagnostic.SA1309.severity = none
# SA1310: Field names should not contain underscore
dotnet_diagnostic.SA1310.severity = error
# SA1311: Static readonly fields should begin with upper-case letter
dotnet_diagnostic.SA1311.severity = error
# SA1312: Variable names should begin with lower-case letter
dotnet_diagnostic.SA1312.severity = error
# SA1313: Parameter names should begin with lower-case letter
dotnet_diagnostic.SA1313.severity = error
# SA1314: Type parameter names should begin with T
dotnet_diagnostic.SA1314.severity = error
# SA1400: Access modifier should be declared
dotnet_diagnostic.SA1400.severity = error
# SA1401: Fields should be private
dotnet_diagnostic.SA1401.severity = error
# SA1402: File may only contain a single type
dotnet_diagnostic.SA1402.severity = warning
# SA1403: File may only contain a single namespace
dotnet_diagnostic.SA1403.severity = error
# SA1404: Code analysis suppression should have justification
dotnet_diagnostic.SA1404.severity = error
# SA1405: Debug.Assert should provide message text
dotnet_diagnostic.SA1405.severity = error
# SA1406: Debug.Fail should provide message text
dotnet_diagnostic.SA1406.severity = error
# SA1407: Arithmetic expressions should declare precedence
dotnet_diagnostic.SA1407.severity = error
# SA1408: Conditional expressions should declare precedence
dotnet_diagnostic.SA1408.severity = error
# SA1410: Remove delegate parenthesis when possible
dotnet_diagnostic.SA1410.severity = error
# SA1411: Attribute constructor should not use unnecessary parenthesis
dotnet_diagnostic.SA1411.severity = error
# SA1412: Store files as UTF-8 with byte order mark
dotnet_diagnostic.SA1412.severity = error
# SA1413: Use trailing comma in multi-line initializers
dotnet_diagnostic.SA1413.severity = error
# SA1500: Braces for multi-line statements should not share line
dotnet_diagnostic.SA1500.severity = error
# SA1501: Statement should not be on a single line
dotnet_diagnostic.SA1501.severity = none
# SA1502: Element should not be on a single line
dotnet_diagnostic.SA1502.severity = error
# SA1503: Braces should not be omitted
dotnet_diagnostic.SA1503.severity = none
# SA1504: All accessors should be single-line or multi-line
dotnet_diagnostic.SA1504.severity = error
# SA1505: Opening braces should not be followed by blank line
dotnet_diagnostic.SA1505.severity = warning
# SA1506: Element documentation headers should not be followed by blank line
dotnet_diagnostic.SA1506.severity = error
# SA1507: Code should not contain multiple blank lines in a row
dotnet_diagnostic.SA1507.severity = warning
# SA1508: Closing braces should not be preceded by blank line
dotnet_diagnostic.SA1508.severity = warning
# SA1509: Opening braces should not be preceded by blank line
dotnet_diagnostic.SA1509.severity = error
# SA1510: Chained statement blocks should not be preceded by blank line
dotnet_diagnostic.SA1510.severity = error
# SA1511: While-do footer should not be preceded by blank line
dotnet_diagnostic.SA1511.severity = error
# SA1512: Single-line comments should not be followed by blank line
dotnet_diagnostic.SA1512.severity = error
# SA1513: Closing brace should be followed by blank line
dotnet_diagnostic.SA1513.severity = error
# SA1514: Element documentation header should be preceded by blank line
dotnet_diagnostic.SA1514.severity = error
# SA1515: Single-line comment should be preceded by blank line
dotnet_diagnostic.SA1515.severity = error
# SA1516: Elements should be separated by blank line
dotnet_diagnostic.SA1516.severity = none
# SA1517: Code should not contain blank lines at start of file
dotnet_diagnostic.SA1517.severity = error
# SA1518: Use line endings correctly at end of file
dotnet_diagnostic.SA1518.severity = error
# SA1519: Braces should not be omitted from multi-line child statement
dotnet_diagnostic.SA1519.severity = error
# SA1520: Use braces consistently
dotnet_diagnostic.SA1520.severity = error
# SA1600: Elements should be documented
dotnet_diagnostic.SA1600.severity = none
# SA1601: Partial elements should be documented
dotnet_diagnostic.SA1601.severity = none
# SA1602: Enumeration items should be documented
dotnet_diagnostic.SA1602.severity = none
# SA1604: Element documentation should have summary
dotnet_diagnostic.SA1604.severity = none
# SA1605: Partial element documentation should have summary
dotnet_diagnostic.SA1605.severity = none
# SA1606: Element documentation should have summary text
dotnet_diagnostic.SA1606.severity = none
# SA1607: Partial element documentation should have summary text
dotnet_diagnostic.SA1607.severity = none
# SA1608: Element documentation should not have default summary
dotnet_diagnostic.SA1608.severity = none
# SA1609: Property documentation should have value
dotnet_diagnostic.SA1609.severity = none
# SA1610: Property documentation should have value text
dotnet_diagnostic.SA1610.severity = error
# SA1611: Element parameters should be documented
dotnet_diagnostic.SA1611.severity = none
# SA1612: Element parameter documentation should match element parameters
dotnet_diagnostic.SA1612.severity = error
# SA1613: Element parameter documentation should declare parameter name
dotnet_diagnostic.SA1613.severity = error
# SA1614: Element parameter documentation should have text
dotnet_diagnostic.SA1614.severity = error
# SA1615: Element return value should be documented
dotnet_diagnostic.SA1615.severity = none
# SA1616: Element return value documentation should have text
dotnet_diagnostic.SA1616.severity = error
# SA1617: Void return value should not be documented
dotnet_diagnostic.SA1617.severity = error
# SA1618: Generic type parameters should be documented
dotnet_diagnostic.SA1618.severity = none
# SA1619: Generic type parameters should be documented partial class
dotnet_diagnostic.SA1619.severity = none
# SA1620: Generic type parameter documentation should match type parameters
dotnet_diagnostic.SA1620.severity = none
# SA1621: Generic type parameter documentation should declare parameter name
dotnet_diagnostic.SA1621.severity = none
# SA1622: Generic type parameter documentation should have text
dotnet_diagnostic.SA1622.severity = error
# SA1623: Property summary documentation should match accessors
dotnet_diagnostic.SA1623.severity = none
# SA1624: Property summary documentation should omit accessor with restricted access
dotnet_diagnostic.SA1624.severity = none
# SA1625: Element documentation should not be copied and pasted
dotnet_diagnostic.SA1625.severity = none
# SA1626: Single-line comments should not use documentation style slashes
dotnet_diagnostic.SA1626.severity = none
# SA1627: Documentation text should not be empty
dotnet_diagnostic.SA1627.severity = error
# SA1629: Documentation text should end with a period
dotnet_diagnostic.SA1629.severity = none
# SA1633: File should have header
dotnet_diagnostic.SA1633.severity = none
# SA1634: File header should show copyright
dotnet_diagnostic.SA1634.severity = none
# SA1635: File header should have copyright text
dotnet_diagnostic.SA1635.severity = none
# SA1636: File header copyright text should match
dotnet_diagnostic.SA1636.severity = none
# SA1637: File header should contain file name
dotnet_diagnostic.SA1637.severity = none
# SA1638: File header file name documentation should match file name
dotnet_diagnostic.SA1638.severity = none
# SA1639: File header should have summary
dotnet_diagnostic.SA1639.severity = none
# SA1640: File header should have valid company text
dotnet_diagnostic.SA1640.severity = none
# SA1641: File header company name text should match
dotnet_diagnostic.SA1641.severity = none
# SA1642: Constructor summary documentation should begin with standard text
dotnet_diagnostic.SA1642.severity = none
# SA1643: Destructor summary documentation should begin with standard text
dotnet_diagnostic.SA1643.severity = none
# SA1644: Documentation headers should not contain blank lines
dotnet_diagnostic.SA1644.severity = none
# SA1645: Included documentation file does not exist
dotnet_diagnostic.SA1645.severity = error
# SA1646: Included documentation XPath does not exist
dotnet_diagnostic.SA1646.severity = error
# SA1647: Include node does not contain valid file and path
dotnet_diagnostic.SA1647.severity = error
# SA1648: inheritdoc should be used with inheriting class
dotnet_diagnostic.SA1648.severity = none
# SA1649: File name should match first type name
dotnet_diagnostic.SA1649.severity = warning
# SA1651: Do not use placeholder elements
dotnet_diagnostic.SA1651.severity = none
# SX1101: Do not prefix local calls with 'this.'
dotnet_diagnostic.SX1101.severity = error
# SX1309: Field names should begin with underscore
dotnet_diagnostic.SX1309.severity = error
# SX1309S: Static field names should begin with underscore
dotnet_diagnostic.SX1309S.severity = error


###########################################
# OSIsoft Code Analysis Rules             #
# NOTE: Requires VS2022 17.0 or later     #
###########################################
[*.{cs,vb}]
# CA1000: Do not declare static members on generic types
dotnet_diagnostic.CA1000.severity = error
# CA1001: Types that own disposable fields should be disposable
dotnet_diagnostic.CA1001.severity = error
# CA1002: Do not expose generic lists
dotnet_diagnostic.CA1002.severity = none
# CA1003: Use generic event handler instances
dotnet_diagnostic.CA1003.severity = none
# CA1005: Avoid excessive parameters on generic types
dotnet_diagnostic.CA1005.severity = none
# CA1008: Enums should have zero value
dotnet_diagnostic.CA1008.severity = none
# CA1010: Generic interface should also be implemented
dotnet_diagnostic.CA1010.severity = error
# CA1012: Abstract types should not have public constructors
dotnet_diagnostic.CA1012.severity = none
# CA1014: Mark assemblies with CLSCompliant
dotnet_diagnostic.CA1014.severity = none
# CA1016: Mark assemblies with assembly version
dotnet_diagnostic.CA1016.severity = error
# CA1017: Mark assemblies with ComVisible
dotnet_diagnostic.CA1017.severity = none
# CA1018: Mark attributes with AttributeUsageAttribute
dotnet_diagnostic.CA1018.severity = error
# CA1019: Define accessors for attribute arguments
dotnet_diagnostic.CA1019.severity = none
# CA1021: Avoid out parameters
dotnet_diagnostic.CA1021.severity = none
# CA1024: Use properties where appropriate
dotnet_diagnostic.CA1024.severity = none
# CA1027: Mark enums with FlagsAttribute
dotnet_diagnostic.CA1027.severity = none
# CA1028: Enum Storage should be Int32
dotnet_diagnostic.CA1028.severity = error
# CA1030: Use events where appropriate
dotnet_diagnostic.CA1030.severity = error
# CA1031: Do not catch general exception types
dotnet_diagnostic.CA1031.severity = error
# CA1032: Implement standard exception constructors
dotnet_diagnostic.CA1032.severity = error
# CA1033: Interface methods should be callable by child types
dotnet_diagnostic.CA1033.severity = none
# CA1034: Nested types should not be visible
dotnet_diagnostic.CA1034.severity = error
# CA1036: Override methods on comparable types
dotnet_diagnostic.CA1036.severity = error
# CA1040: Avoid empty interfaces
dotnet_diagnostic.CA1040.severity = error
# CA1041: Provide ObsoleteAttribute message
dotnet_diagnostic.CA1041.severity = error
# CA1043: Use Integral Or String Argument For Indexers
dotnet_diagnostic.CA1043.severity = error
# CA1044: Properties should not be write only
dotnet_diagnostic.CA1044.severity = error
# CA1045: Do not pass types by reference
dotnet_diagnostic.CA1045.severity = none
# CA1046: Do not overload equality operator on reference types
dotnet_diagnostic.CA1046.severity = none
# CA1050: Declare types in namespaces
dotnet_diagnostic.CA1050.severity = none
# CA1051: Do not declare visible instance fields
dotnet_diagnostic.CA1051.severity = error
# CA1052: Static holder types should be Static or NotInheritable
dotnet_diagnostic.CA1052.severity = error
# CA1054: URI-like parameters should not be strings
dotnet_diagnostic.CA1054.severity = error
# CA1055: URI-like return values should not be strings
dotnet_diagnostic.CA1055.severity = error
# CA1056: URI-like properties should not be strings
dotnet_diagnostic.CA1056.severity = error
# CA1058: Types should not extend certain base types
dotnet_diagnostic.CA1058.severity = error
# CA1060: Move pinvokes to native methods class
dotnet_diagnostic.CA1060.severity = none
# CA1061: Do not hide base class methods
dotnet_diagnostic.CA1061.severity = error
# CA1062: Validate arguments of public methods
dotnet_diagnostic.CA1062.severity = error
# CA1063: Implement IDisposable Correctly
dotnet_diagnostic.CA1063.severity = error
# CA1064: Exceptions should be public
dotnet_diagnostic.CA1064.severity = error
# CA1065: Do not raise exceptions in unexpected locations
dotnet_diagnostic.CA1065.severity = error
# CA1066: Implement IEquatable when overriding Object.Equals
dotnet_diagnostic.CA1066.severity = error
# CA1067: Override Object.Equals(object) when implementing IEquatable<T>
    dotnet_diagnostic.CA1067.severity = error
    # CA1068: CancellationToken parameters must come last
    dotnet_diagnostic.CA1068.severity = error
    # CA1069: Enums values should not be duplicated
    dotnet_diagnostic.CA1069.severity = error
    # CA1070: Do not declare event fields as virtual
    dotnet_diagnostic.CA1070.severity = error
    #
    # CA1200: Avoid using cref tags with a prefix
    dotnet_diagnostic.CA1200.severity = error
    #
    # CA1303: Do not pass literals as localized parameters
    dotnet_diagnostic.CA1303.severity = error
    # CA1304: Specify CultureInfo
    dotnet_diagnostic.CA1304.severity = error
    # CA1305: Specify IFormatProvider
    dotnet_diagnostic.CA1305.severity = error
    # CA1307: Specify StringComparison for clarity
    dotnet_diagnostic.CA1307.severity = none
    # CA1308: Normalize strings to uppercase
    dotnet_diagnostic.CA1308.severity = error
    # CA1309: Use ordinal string comparison
    dotnet_diagnostic.CA1309.severity = none
    # CA1310: Specify StringComparison for correctness
    dotnet_diagnostic.CA1310.severity = error
    #
    # CA1401: P/Invokes should not be visible
    dotnet_diagnostic.CA1401.severity = error
    # CA1416: Validate platform compatibility
    dotnet_diagnostic.CA1416.severity = error
    # CA1417: Do not use 'OutAttribute' on string parameters for P/Invokes
    dotnet_diagnostic.CA1417.severity = error
    #
    # CA1501: Avoid excessive inheritance
    dotnet_diagnostic.CA1501.severity = none
    # CA1502: Avoid excessive complexity
    dotnet_diagnostic.CA1502.severity = none
    # CA1505: Avoid unmaintainable code
    dotnet_diagnostic.CA1505.severity = none
    # CA1506: Avoid excessive class coupling
    dotnet_diagnostic.CA1506.severity = none
    # CA1507: Use nameof to express symbol names
    dotnet_diagnostic.CA1507.severity = error
    # CA1508: Avoid dead conditional code
    dotnet_diagnostic.CA1508.severity = none
    # CA1509: Invalid entry in code metrics rule specification file
    dotnet_diagnostic.CA1509.severity = none
    #
    # CA1700: Do not name enum values 'Reserved'
    dotnet_diagnostic.CA1700.severity = none
    # CA1707: Identifiers should not contain underscores
    dotnet_diagnostic.CA1707.severity = error
    # CA1708: Identifiers should differ by more than case
    dotnet_diagnostic.CA1708.severity = none
    # CA1710: Identifiers should have correct suffix
    dotnet_diagnostic.CA1710.severity = error
    # CA1711: Identifiers should not have incorrect suffix
    dotnet_diagnostic.CA1711.severity = none
    # CA1712: Do not prefix enum values with type name
    dotnet_diagnostic.CA1712.severity = error
    # CA1713: Events should not have 'Before' or 'After' prefix
    dotnet_diagnostic.CA1713.severity = error
    # CA1715: Identifiers should have correct prefix
    dotnet_diagnostic.CA1715.severity = error
    # CA1716: Identifiers should not match keywords
    dotnet_diagnostic.CA1716.severity = error
    # CA1717: Only FlagsAttribute enums should have plural names
    dotnet_diagnostic.CA1717.severity = error
    # CA1720: Identifier contains type name
    dotnet_diagnostic.CA1720.severity = error
    # CA1721: Property names should not match get methods
    dotnet_diagnostic.CA1721.severity = error
    # CA1724: Type names should not match namespaces
    dotnet_diagnostic.CA1724.severity = error
    # CA1725: Parameter names should match base declaration
    dotnet_diagnostic.CA1725.severity = none
    #
    # CA1801: Review unused parameters
    dotnet_diagnostic.CA1801.severity = error
    # CA1802: Use literals where appropriate
    dotnet_diagnostic.CA1802.severity = error
    # CA1805: Do not initialize unnecessarily
    dotnet_diagnostic.CA1805.severity = error
    # CA1806: Do not ignore method results
    dotnet_diagnostic.CA1806.severity = error
    # CA1810: Initialize reference type static fields inline
    dotnet_diagnostic.CA1810.severity = error
    # CA1812: Avoid uninstantiated internal classes
    dotnet_diagnostic.CA1812.severity = error
    # CA1813: Avoid unsealed attributes
    dotnet_diagnostic.CA1813.severity = none
    # CA1814: Prefer jagged arrays over multidimensional
    dotnet_diagnostic.CA1814.severity = error
    # CA1815: Override equals and operator equals on value types
    dotnet_diagnostic.CA1815.severity = error
    # CA1816: Dispose methods should call SuppressFinalize
    dotnet_diagnostic.CA1816.severity = error
    # CA1819: Properties should not return arrays
    dotnet_diagnostic.CA1819.severity = error
    # CA1820: Test for empty strings using string length
    dotnet_diagnostic.CA1820.severity = error
    # CA1821: Remove empty Finalizers
    dotnet_diagnostic.CA1821.severity = error
    # CA1822: Mark members as static
    dotnet_diagnostic.CA1822.severity = error
    # CA1823: Avoid unused private fields
    dotnet_diagnostic.CA1823.severity = error
    # CA1824: Mark assemblies with NeutralResourcesLanguageAttribute
    dotnet_diagnostic.CA1824.severity = error
    # CA1825: Avoid zero-length array allocations
    dotnet_diagnostic.CA1825.severity = error
    # CA1826: Do not use Enumerable methods on indexable collections
    dotnet_diagnostic.CA1826.severity = error
    # CA1827: Do not use Count() or LongCount() when Any() can be used
    dotnet_diagnostic.CA1827.severity = error
    # CA1828: Do not use CountAsync() or LongCountAsync() when AnyAsync() can be used
    dotnet_diagnostic.CA1828.severity = error
    # CA1829: Use Length/Count property instead of Count() when available
    dotnet_diagnostic.CA1829.severity = error
    # CA1830: Prefer strongly-typed Append and Insert method overloads on StringBuilder
    dotnet_diagnostic.CA1830.severity = error
    # CA1831: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
    dotnet_diagnostic.CA1831.severity = error
    # CA1832: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
    dotnet_diagnostic.CA1832.severity = error
    # CA1833: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
    dotnet_diagnostic.CA1833.severity = error
    # CA1834: Consider using 'StringBuilder.Append(char)' when applicable
    dotnet_diagnostic.CA1834.severity = error
    # CA1835: Prefer the 'Memory'-based overloads for 'ReadAsync' and 'WriteAsync'
    dotnet_diagnostic.CA1835.severity = error
    # CA1836: Prefer IsEmpty over Count
    dotnet_diagnostic.CA1836.severity = error
    # CA1837: Use 'Environment.ProcessId'
    dotnet_diagnostic.CA1837.severity = error
    # CA1838: Avoid 'StringBuilder' parameters for P/Invokes
    dotnet_diagnostic.CA1838.severity = none
    #
    # CA2000: Dispose objects before losing scope
    dotnet_diagnostic.CA2000.severity = error
    # CA2002: Do not lock on objects with weak identity
    dotnet_diagnostic.CA2002.severity = error
    # CA2007: Consider calling ConfigureAwait on the awaited task
    dotnet_diagnostic.CA2007.severity = error
    # CA2008: Do not create tasks without passing a TaskScheduler
    dotnet_diagnostic.CA2008.severity = error
    # CA2009: Do not call ToImmutableCollection on an ImmutableCollection value
    dotnet_diagnostic.CA2009.severity = error
    # CA2011: Avoid infinite recursion
    dotnet_diagnostic.CA2011.severity = error
    # CA2012: Use ValueTasks correctly
    dotnet_diagnostic.CA2012.severity = error
    # CA2013: Do not use ReferenceEquals with value types
    dotnet_diagnostic.CA2013.severity = error
    # CA2014: Do not use stackalloc in loops
    dotnet_diagnostic.CA2014.severity = error
    # CA2015: Do not define finalizers for types derived from MemoryManager<T>
        dotnet_diagnostic.CA2015.severity = error
        # CA2016: Forward the 'CancellationToken' parameter to methods that take one
        dotnet_diagnostic.CA2016.severity = error
        #
        # CA2100: Review SQL queries for security vulnerabilities
        dotnet_diagnostic.CA2100.severity = error
        # CA2101: Specify marshaling for P/Invoke string arguments
        dotnet_diagnostic.CA2101.severity = error
        # CA2109: Review visible event handlers
        dotnet_diagnostic.CA2109.severity = none
        # CA2119: Seal methods that satisfy private interfaces
        dotnet_diagnostic.CA2119.severity = error
        # CA2153: Do Not Catch Corrupted State Exceptions
        dotnet_diagnostic.CA2153.severity = error
        # CA2200: Rethrow to preserve stack details
        dotnet_diagnostic.CA2200.severity = error
        # CA2201: Do not raise reserved exception types
        dotnet_diagnostic.CA2201.severity = none
        # CA2207: Initialize value type static fields inline
        dotnet_diagnostic.CA2207.severity = error
        # CA2208: Instantiate argument exceptions correctly
        dotnet_diagnostic.CA2208.severity = error
        # CA2211: Non-constant fields should not be visible
        dotnet_diagnostic.CA2211.severity = error
        # CA2213: Disposable fields should be disposed
        dotnet_diagnostic.CA2213.severity = error
        # CA2214: Do not call overridable methods in constructors
        dotnet_diagnostic.CA2214.severity = error
        # CA2215: Dispose methods should call base class dispose
        dotnet_diagnostic.CA2215.severity = error
        # CA2216: Disposable types should declare finalizer
        dotnet_diagnostic.CA2216.severity = error
        # CA2217: Do not mark enums with FlagsAttribute
        dotnet_diagnostic.CA2217.severity = none
        # CA2219: Do not raise exceptions in finally clauses
        dotnet_diagnostic.CA2219.severity = error
        # CA2225: Operator overloads have named alternates
        dotnet_diagnostic.CA2225.severity = error
        # CA2226: Operators should have symmetrical overloads
        dotnet_diagnostic.CA2226.severity = error
        # CA2227: Collection properties should be read only
        dotnet_diagnostic.CA2227.severity = error
        # CA2229: Implement serialization constructors
        dotnet_diagnostic.CA2229.severity = error
        # CA2231: Overload operator equals on overriding value type Equals
        dotnet_diagnostic.CA2231.severity = error
        # CA2234: Pass system uri objects instead of strings
        dotnet_diagnostic.CA2234.severity = error
        # CA2235: Mark all non-serializable fields
        dotnet_diagnostic.CA2235.severity = error
        # CA2237: Mark ISerializable types with serializable
        dotnet_diagnostic.CA2237.severity = error
        # CA2241: Provide correct arguments to formatting methods
        dotnet_diagnostic.CA2241.severity = error
        # CA2242: Test for NaN correctly
        dotnet_diagnostic.CA2242.severity = error
        # CA2243: Attribute string literals should parse correctly
        dotnet_diagnostic.CA2243.severity = error
        # CA2244: Do not duplicate indexed element initializations
        dotnet_diagnostic.CA2244.severity = error
        # CA2245: Do not assign a property to itself
        dotnet_diagnostic.CA2245.severity = error
        # CA2246: Assigning symbol and its member in the same statement
        dotnet_diagnostic.CA2246.severity = error
        # CA2247: Argument passed to TaskCompletionSource constructor should be TaskCreationOptions enum instead of TaskContinuationOptions enum
        dotnet_diagnostic.CA2247.severity = error
        # CA2248: Provide correct 'enum' argument to 'Enum.HasFlag'
        dotnet_diagnostic.CA2248.severity = error
        # CA2249: Consider using 'string.Contains' instead of 'string.IndexOf'
        dotnet_diagnostic.CA2249.severity = error
        # CA2300: Do not use insecure deserializer BinaryFormatter
        dotnet_diagnostic.CA2300.severity = none
        # CA2301: Do not call BinaryFormatter.Deserialize without first setting BinaryFormatter.Binder
        dotnet_diagnostic.CA2301.severity = none
        # CA2302: Ensure BinaryFormatter.Binder is set before calling BinaryFormatter.Deserialize
        dotnet_diagnostic.CA2302.severity = none
        # CA2305: Do not use insecure deserializer LosFormatter
        dotnet_diagnostic.CA2305.severity = none
        # CA2310: Do not use insecure deserializer NetDataContractSerializer
        dotnet_diagnostic.CA2310.severity = none
        # CA2311: Do not deserialize without first setting NetDataContractSerializer.Binder
        dotnet_diagnostic.CA2311.severity = none
        # CA2312: Ensure NetDataContractSerializer.Binder is set before deserializing
        dotnet_diagnostic.CA2312.severity = none
        # CA2315: Do not use insecure deserializer ObjectStateFormatter
        dotnet_diagnostic.CA2315.severity = none
        # CA2321: Do not deserialize with JavaScriptSerializer using a SimpleTypeResolver
        dotnet_diagnostic.CA2321.severity = none
        # CA2322: Ensure JavaScriptSerializer is not initialized with SimpleTypeResolver before deserializing
        dotnet_diagnostic.CA2322.severity = none
        # CA2326: Do not use TypeNameHandling values other than None
        dotnet_diagnostic.CA2326.severity = none
        # CA2327: Do not use insecure JsonSerializerSettings
        dotnet_diagnostic.CA2327.severity = none
        # CA2328: Ensure that JsonSerializerSettings are secure
        dotnet_diagnostic.CA2328.severity = none
        # CA2329: Do not deserialize with JsonSerializer using an insecure configuration
        dotnet_diagnostic.CA2329.severity = none
        # CA2330: Ensure that JsonSerializer has a secure configuration when deserializing
        dotnet_diagnostic.CA2330.severity = none
        # CA2350: Do not use DataTable.ReadXml() with untrusted data
        dotnet_diagnostic.CA2350.severity = none
        # CA2351: Do not use DataSet.ReadXml() with untrusted data
        dotnet_diagnostic.CA2351.severity = none
        # CA2352: Unsafe DataSet or DataTable in serializable type can be vulnerable to remote code execution attacks
        dotnet_diagnostic.CA2352.severity = none
        # CA2353: Unsafe DataSet or DataTable in serializable type
        dotnet_diagnostic.CA2353.severity = none
        # CA2354: Unsafe DataSet or DataTable in deserialized object graph can be vulnerable to remote code execution attacks
        dotnet_diagnostic.CA2354.severity = none
        # CA2355: Unsafe DataSet or DataTable type found in deserializable object graph
        dotnet_diagnostic.CA2355.severity = none
        # CA2356: Unsafe DataSet or DataTable type in web deserializable object graph
        dotnet_diagnostic.CA2356.severity = none
        # CA2361: Ensure auto-generated class containing DataSet.ReadXml() is not used with untrusted data
        dotnet_diagnostic.CA2361.severity = none
        # CA2362: Unsafe DataSet or DataTable in auto-generated serializable type can be vulnerable to remote code execution attacks
        dotnet_diagnostic.CA2362.severity = none
        #
        # CA3001: Review code for SQL injection vulnerabilities
        dotnet_diagnostic.CA3001.severity = none
        # CA3002: Review code for XSS vulnerabilities
        dotnet_diagnostic.CA3002.severity = none
        # CA3003: Review code for file path injection vulnerabilities
        dotnet_diagnostic.CA3003.severity = none
        # CA3004: Review code for information disclosure vulnerabilities
        dotnet_diagnostic.CA3004.severity = none
        # CA3005: Review code for LDAP injection vulnerabilities
        dotnet_diagnostic.CA3005.severity = none
        # CA3006: Review code for process command injection vulnerabilities
        dotnet_diagnostic.CA3006.severity = none
        # CA3007: Review code for open redirect vulnerabilities
        dotnet_diagnostic.CA3007.severity = none
        # CA3008: Review code for XPath injection vulnerabilities
        dotnet_diagnostic.CA3008.severity = none
        # CA3009: Review code for XML injection vulnerabilities
        dotnet_diagnostic.CA3009.severity = none
        # CA3010: Review code for XAML injection vulnerabilities
        dotnet_diagnostic.CA3010.severity = none
        # CA3011: Review code for DLL injection vulnerabilities
        dotnet_diagnostic.CA3011.severity = none
        # CA3012: Review code for regex injection vulnerabilities
        dotnet_diagnostic.CA3012.severity = none
        # CA3061: Do Not Add Schema By URL
        dotnet_diagnostic.CA3061.severity = error
        # CA3075: Insecure DTD processing in XML
        dotnet_diagnostic.CA3075.severity = error
        # CA3076: Insecure XSLT script processing.
        dotnet_diagnostic.CA3076.severity = error
        # CA3077: Insecure Processing in API Design, XmlDocument and XmlTextReader
        dotnet_diagnostic.CA3077.severity = error
        # CA3147: Mark Verb Handlers With Validate Antiforgery Token
        dotnet_diagnostic.CA3147.severity = error
        #
        # CA5350: Do Not Use Weak Cryptographic Algorithms
        dotnet_diagnostic.CA5350.severity = error
        # CA5351: Do Not Use Broken Cryptographic Algorithms
        dotnet_diagnostic.CA5351.severity = error
        # CA5358: Review cipher mode usage with cryptography experts
        dotnet_diagnostic.CA5358.severity = none
        # CA5359: Do Not Disable Certificate Validation
        dotnet_diagnostic.CA5359.severity = error
        # CA5360: Do Not Call Dangerous Methods In Deserialization
        dotnet_diagnostic.CA5360.severity = error
        # CA5361: Do Not Disable SChannel Use of Strong Crypto
        dotnet_diagnostic.CA5361.severity = none
        # CA5362: Potential reference cycle in deserialized object graph
        dotnet_diagnostic.CA5362.severity = none
        # CA5363: Do Not Disable Request Validation
        dotnet_diagnostic.CA5363.severity = error
        # CA5364: Do Not Use Deprecated Security Protocols
        dotnet_diagnostic.CA5364.severity = error
        # CA5365: Do Not Disable HTTP Header Checking
        dotnet_diagnostic.CA5365.severity = error
        # CA5366: Use XmlReader for 'DataSet.ReadXml()'
        dotnet_diagnostic.CA5366.severity = error
        # CA5367: Do Not Serialize Types With Pointer Fields
        dotnet_diagnostic.CA5367.severity = none
        # CA5368: Set ViewStateUserKey For Classes Derived From Page
        dotnet_diagnostic.CA5368.severity = error
        # CA5369: Use XmlReader for 'XmlSerializer.Deserialize()'
        dotnet_diagnostic.CA5369.severity = error
        # CA5370: Use XmlReader for XmlValidatingReader constructor
        dotnet_diagnostic.CA5370.severity = error
        # CA5371: Use XmlReader for 'XmlSchema.Read()'
        dotnet_diagnostic.CA5371.severity = error
        # CA5372: Use XmlReader for XPathDocument constructor
        dotnet_diagnostic.CA5372.severity = error
        # CA5373: Do not use obsolete key derivation function
        dotnet_diagnostic.CA5373.severity = error
        # CA5374: Do Not Use XslTransform
        dotnet_diagnostic.CA5374.severity = error
        # CA5375: Do Not Use Account Shared Access Signature
        dotnet_diagnostic.CA5375.severity = none
        # CA5376: Use SharedAccessProtocol HttpsOnly
        dotnet_diagnostic.CA5376.severity = none
        # CA5377: Use Container Level Access Policy
        dotnet_diagnostic.CA5377.severity = none
        # CA5378: Do not disable ServicePointManagerSecurityProtocols
        dotnet_diagnostic.CA5378.severity = none
        # CA5379: Ensure Key Derivation Function algorithm is sufficiently strong
        dotnet_diagnostic.CA5379.severity = error
        # CA5380: Do Not Add Certificates To Root Store
        dotnet_diagnostic.CA5380.severity = none
        # CA5381: Ensure Certificates Are Not Added To Root Store
        dotnet_diagnostic.CA5381.severity = none
        # CA5382: Use Secure Cookies In ASP.Net Core
        dotnet_diagnostic.CA5382.severity = none
        # CA5383: Ensure Use Secure Cookies In ASP.Net Core
        dotnet_diagnostic.CA5383.severity = none
        # CA5384: Do Not Use Digital Signature Algorithm (DSA)
        dotnet_diagnostic.CA5384.severity = error
        # CA5385: Use Rivest–Shamir–Adleman (RSA) Algorithm With Sufficient Key Size
        dotnet_diagnostic.CA5385.severity = error
        # CA5386: Avoid hardcoding SecurityProtocolType value
        dotnet_diagnostic.CA5386.severity = none
        # CA5387: Do Not Use Weak Key Derivation Function With Insufficient Iteration Count
        dotnet_diagnostic.CA5387.severity = none
        # CA5388: Ensure Sufficient Iteration Count When Using Weak Key Derivation Function
        dotnet_diagnostic.CA5388.severity = none
        # CA5389: Do Not Add Archive Item's Path To The Target File System Path
        dotnet_diagnostic.CA5389.severity = none
        # CA5390: Do not hard-code encryption key
        dotnet_diagnostic.CA5390.severity = none
        # CA5391: Use antiforgery tokens in ASP.NET Core MVC controllers
        dotnet_diagnostic.CA5391.severity = none
        # CA5392: Use DefaultDllImportSearchPaths attribute for P/Invokes
        dotnet_diagnostic.CA5392.severity = none
        # CA5393: Do not use unsafe DllImportSearchPath value
        dotnet_diagnostic.CA5393.severity = none
        # CA5394: Do not use insecure randomness
        dotnet_diagnostic.CA5394.severity = none
        # CA5395: Miss HttpVerb attribute for action methods
        dotnet_diagnostic.CA5395.severity = none
        # CA5396: Set HttpOnly to true for HttpCookie
        dotnet_diagnostic.CA5396.severity = none
        # CA5397: Do not use deprecated SslProtocols values
        dotnet_diagnostic.CA5397.severity = error
        # CA5398: Avoid hardcoded SslProtocols values
        dotnet_diagnostic.CA5398.severity = none
        # CA5399: HttpClients should enable certificate revocation list checks
        dotnet_diagnostic.CA5399.severity = none
        # CA5400: Ensure HttpClient certificate revocation list check is not disabled
        dotnet_diagnostic.CA5400.severity = none
        # CA5401: Do not use CreateEncryptor with non-default IV
        dotnet_diagnostic.CA5401.severity = none
        # CA5402: Use CreateEncryptor with the default IV
        dotnet_diagnostic.CA5402.severity = none
        # CA5403: Do not hard-code certificate
        dotnet_diagnostic.CA5403.severity = none
        #
        # IL3000: Avoid using accessing Assembly file path when publishing as a single-file
        dotnet_diagnostic.IL3000.severity = error
        # IL3001: Avoid using accessing Assembly file path when publishing as a single-file
        dotnet_diagnostic.IL3001.severity = error


        ####################################################################
        # Team-specific Rules, Extensions, and Overrides
        # Description:
        #   Rules from the common OSIsoft rules may be overridden here.
        #   Extensions to enable additional rules is always acceptable.
        #   Overrides do disable a rule are by exception only and should
        #   include one of the following:
        #   1)  A PBI number for the eventual removal of the override.
        #       For catalog, in a case where compliance will take
        #       time to implement.
        #   2)  A justification of why the override is required.
        ####################################################################
        # Catalog to add enforcement of CA1008 and disable SA1000 with PBI:
        #   # CA1008: Enums should have zero value
        #   dotnet_diagnostic.CA1008.severity = error
        #   # SA1000: Keywords should be spaced correctly
        #   dotnet_diagnostic.SA1000.severity = none # PBI: <link to="" PBI="">
####################################################################

# Top-level, team-specific editorconfig preferences

[*.md]
trim_trailing_whitespace = true

# CSharp code style settings:
[*.cs]

###################################################################################################
# Code Analysis Rule extensions (no justification or PBI reference required)
###################################################################################################
# IDE0008: Explicit type versus var - silent preference for explicit types but permissive with no notifications either way
csharp_style_var_for_built_in_types = false:silent
csharp_style_var_when_type_is_apparent = false:silent
csharp_style_var_elsewhere = false:silent
# IDE0090: Do not use 'new(...)'
csharp_style_implicit_object_creation_when_type_is_apparent = false
# IDE0063: Use simple 'using' statement
csharp_prefer_simple_using_statement = true:silent
# Spacing
csharp_space_after_comma = true
# IDE0005: Remove unnecessary imports
dotnet_diagnostic.IDE0005.severity = warning
stylecop.readability.allowBuiltInTypeAliases = true
stylecop.naming.allowedHungarianPrefixes = op, na, eu, af, ai, db, dr, ef, ex, id, ip, my, ns, ok, pi
stylecop.documentation.documentInternalElements = false

###################################################################################################
# Code Analysis Rule removals / overrides (include justification or PBI #)
###################################################################################################

# Code files
[*.{cs,csx,vb,vbx}]
# NO trailing newline ending every file - Justification: This value is consistent with the original SA1518:CodeMustNotContainBlankLinesAtEndOfFile setting: "To improve the layout of the code, StyleCop requires no blank lines at the end of files. A violation of this rule occurs when one or more blank lines are at the end of the file."
insert_final_newline = false