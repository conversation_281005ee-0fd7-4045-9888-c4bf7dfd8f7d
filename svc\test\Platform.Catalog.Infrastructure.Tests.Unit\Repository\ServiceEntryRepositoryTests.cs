﻿using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Domain.Queries;
using Aveva.Platform.Catalog.Infrastructure.Entities;
using Aveva.Platform.Catalog.Infrastructure.Facades;
using Aveva.Platform.Catalog.Infrastructure.Repository;
using Microsoft.Extensions.Caching.Memory;
using Moq;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Infrastructure.Tests.Unit.Repository;

/// <summary>
/// <see cref="ServiceEntryRepositoryTests"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Unit")]
[Trait("Category", "Infrastructure")]
[Trait("Category", "Infrastructure.Unit")]
[Trait("Tag", "Repository")]
public sealed class ServiceEntryRepositoryTests : IDisposable
{
    private readonly ServiceEntryRepository _repository;
    private readonly V1ServiceEntry _serviceEntry;
    private readonly Mock<IKubernetesFacade> _mockKubernetes;
    private readonly MemoryCache _memoryCache;

    public ServiceEntryRepositoryTests()
    {
        _serviceEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            IconUrl = new System.Uri("https://www.example.com"),
            Description = "Test description",
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };

        _mockKubernetes = new Mock<IKubernetesFacade>();
        _memoryCache = new MemoryCache(new MemoryCacheOptions());

        _mockKubernetes
            .Setup(x => x.ListServiceEntriesAsync())
            .ReturnsAsync(new V1K8sServiceEntryList
            {
                Items =
                [
                    V1K8sServiceEntry.Create(_serviceEntry, "namespace"),
                ],
            });

        _repository = new ServiceEntryRepository(
            _mockKubernetes.Object,
            _memoryCache,
            new Mock<CatalogMetrics>().Object);
    }

    [Fact]
    public async Task QueryAsync_ShouldReturnServiceEntries()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            IconUrl = new System.Uri("https://www.example.com"),
            Description = "Test description",
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };

        var query = new ServiceEntryQuery();

        // Act
        var response = await _repository.QueryAsync(query).ConfigureAwait(true);

        // Assert
        response.Count().ShouldBeGreaterThan(0);
        foreach (V1ServiceEntry entry in response)
        {
           newEntry.Equals(entry).ShouldBeTrue();
        }
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnServiceEntry()
    {
        // Act
        var response = await _repository.GetByIdAsync(_serviceEntry.Id).ConfigureAwait(true);

        // Assert
        response!.Equals(_serviceEntry).ShouldBeTrue();
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenServiceEntryNotExists()
    {
        // Act
        var response = await _repository.GetByIdAsync("notexists").ConfigureAwait(true);

        // Assert
        response.ShouldBeNull();
    }

    [Fact]
    public void ClearCache_ShouldNotThrow()
    {
        // Act & Assert
        Should.NotThrow(_repository.ClearCache);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldOnlyGetFromK8sOnce_WhenMultipleParallelCalls()
    {
        // Arrange
        _mockKubernetes.Reset();
        _mockKubernetes
            .Setup(x => x.ListServiceEntriesAsync())
            .Returns(async () =>
            {
                await Task.Delay(1000).ConfigureAwait(false);
                return new V1K8sServiceEntryList
                {
                    Items =
                    [
                        V1K8sServiceEntry.Create(_serviceEntry, "namespace"),
                    ],
                };
            });

        // Act
        var tasks = Enumerable.Repeat(0, 100).Select(x => _repository.GetByIdAsync(_serviceEntry.Id));
        var results = await Task.WhenAll(tasks).ConfigureAwait(true);

        // Assert
        results.ShouldAllBe((actual) => actual == _serviceEntry);
        _mockKubernetes.Verify(x => x.ListServiceEntriesAsync(), Times.Once);
    }

    public void Dispose()
    {
        _memoryCache?.Dispose();
    }
}