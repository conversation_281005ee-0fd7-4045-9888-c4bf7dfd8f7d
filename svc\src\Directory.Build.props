<Project>
  <!-- 
        Shared build properties file - reference: https://docs.microsoft.com/en-us/visualstudio/msbuild/customize-your-build
        Hierarchical if you add this to child Directory.Build.props files:
        <Import Project="$([MSBuild]::GetPathOfFileAbove($(MSBuildThisFile), $(MSBuildThisFileDirectory)..))" />
  -->
  <!-- Versioning properties -->
  <PropertyGroup>
    <Version Condition="$(Version) == ''">0.1</Version>
    <VersionSuffix Condition="$(VersionSuffix) == ''">.0</VersionSuffix>
    <PackageVersionSuffix Condition="$(PackageVersionSuffix) == ''"></PackageVersionSuffix>
    <FileVersion>$(Version)$(VersionSuffix).0</FileVersion>
    <InformationalVersion>$(Version)$(VersionSuffix).0$(PackageVersionSuffix)$(SourceRevisionId)</InformationalVersion>
    <PackageVersion>$(Version)$(VersionSuffix)$(PackageVersionSuffix)</PackageVersion>
    <!-- Suppresses a nuget pack warning about our prerelease labels not being compatible with older clients
    <NoWarn>NU5105;</NoWarn>
    -->
  </PropertyGroup>

  <!-- NuGet packaging properties -->
  <PropertyGroup>
    <IsPackable>true</IsPackable>
    <Company>AVEVA</Company>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
    <Authors>Connect Development&lt;<EMAIL>&gt;</Authors>
    <PackageTags>Clould Platform Connect Service</PackageTags>
    <PackageProjectUrl>https://dev.azure.com/AVEVA-VSTS/Cloud%20Platform/_wiki/wikis/Platform/47902/Catalog</PackageProjectUrl>
    <PackageLicenseFile>LICENSE.md</PackageLicenseFile>
    <PackageIcon>images\PackageIcon.png</PackageIcon>
    <PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
  </PropertyGroup>

  <ItemGroup>
    <None Include="$(MSBuildThisFileDirectory)..\docs\LICENSE.md" Visible="false">
      <Pack>True</Pack>
      <PackagePath></PackagePath>
    </None>
    <None Include="$(MSBuildThisFileDirectory)..\docs\PackageIcon.png" Visible="false">
      <Pack>True</Pack>
      <PackagePath>images\</PackagePath>
    </None>
  </ItemGroup>

  <!-- Conditionally add debugging support to package (include symbols, enable SourceLink, etc.) when publishing for internal consumption -->
  <PropertyGroup Condition="$(PublicPublishing) == '' Or $(PublicPublishing.Equals('false', StringComparison.OrdinalIgnoreCase))">
    <!-- Packaging optional: Build symbol package with symbol package format of symbols.nupkg or snupkg (.snupkg) to distribute the PDB -->
    <!--<IncludeSymbols>true</IncludeSymbols>-->
    <!--<SymbolPackageFormat>snupkg</SymbolPackageFormat>-->
    <!-- Source Link Support - Include symbols in package (needed for Source Link with Azure DevOps Artifacts since it doesn't support snupkg format) -->
    <!--<AllowedOutputExtensionsInPackageBuildOutputFolder>$(AllowedOutputExtensionsInPackageBuildOutputFolder);.pdb</AllowedOutputExtensionsInPackageBuildOutputFolder>-->

    <RepositoryUrl>https://dev.azure.com/AVEVA-VSTS/Cloud%20Platform/_git/cloud-platform-libs</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <!-- Source Link Support - optional: Publish the repository URL in the built .nupkg (in the NuSpec <Repository> element) -->
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <!-- Source Link Support - optional: Embed source files that are not tracked by the source control manager in the PDB -->
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
  </PropertyGroup>

  <ItemGroup Condition="$(PublicPublishing) == '' Or $(PublicPublishing.Equals('false', StringComparison.OrdinalIgnoreCase))">
    <!-- <PackageReference Include="Microsoft.SourceLink.AzureRepos.Git" Version="$(MicrosoftSourceLinkAzureReposGitVersion)"> -->
    <!--  <PrivateAssets>all</PrivateAssets> -->
    <!--  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets> -->
    <!-- </PackageReference> -->
  </ItemGroup>

  <!--Turn off websigning for now-->
  <!--<Import Project="$(MSBuildThisFileDirectory)..\..\svc\WebSign.props" />-->
  <Import Project="$([MSBuild]::GetPathOfFileAbove($(MSBuildThisFile), $(MSBuildThisFileDirectory)..))" />
</Project>