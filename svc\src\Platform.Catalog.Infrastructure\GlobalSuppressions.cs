﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Performance", "CA1859:Use concrete types when possible for improved performance", Justification = "Unneeded", Scope = "member", Target = "~M:Aveva.Platform.Catalog.Infrastructure.Repository.ServiceAvailabilityRepository.FilterServicesWithAvailability(System.Collections.Generic.IEnumerable{Aveva.Platform.Catalog.Domain.Models.V1ServiceEntry})~System.Collections.Generic.IEnumerable{Aveva.Platform.Catalog.Domain.Models.V1ServiceEntry}")]