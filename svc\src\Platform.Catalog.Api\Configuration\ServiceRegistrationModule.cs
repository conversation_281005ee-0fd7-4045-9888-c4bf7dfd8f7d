﻿using Aveva.Platform.Catalog.Api.Routing;
using Aveva.Platform.Catalog.Api.Swagger.SchemaFilters;
using Aveva.Platform.Common.Abstractions.Extensions;
using Aveva.Platform.Common.Framework.Abstractions.Configuration;
using Aveva.Platform.Common.Framework.Abstractions.Extensions;
using Aveva.Platform.Common.Framework.AspNetCore.Routing.Extensions;
using Aveva.Platform.Common.Framework.AspNetCore.Swagger;
using Aveva.Platform.Common.Framework.ExceptionHandling.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Aveva.Platform.Catalog.Api.Configuration;

/// <summary>
/// Initializes a new instance of the <see cref="ServiceRegistrationModule"/> class.
/// </summary>
internal sealed class ServiceRegistrationModule() : IServiceRegistrationModule
{
    #region Public Methods

    /// <inheritdoc/>
    public void AddServices(IServiceCollection services)
    {
        // Define the default JSON serialization settings used by the API.
        services.ConfigureHttpJsonOptions(options => options.SerializerOptions.ConfigureDefaults(enumsAsStrings: true));

        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        // Catalog: Relocate this boilerplate code to the libraries' Swagger related registration code.
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(options =>
        {
            options.DocumentFilter<TagDescriptionDocumentFilter>();
            options.OperationFilter<SwaggerDefaultValues>();
            options.SchemaFilter<ProtocolOptionsSchemaFilter>();
        });
        services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();

        // Enable API versioning.
        // Catalog: Relocate this boilerplate code to the libraries' API related registration code.
        services
            .AddApiVersioning((Asp.Versioning.ApiVersioningOptions options) =>
            {
                options.ReportApiVersions = true;
            })
            .AddApiExplorer(options =>
            {
                options.GroupNameFormat = "'v'VVV";
                options.FormatGroupName = (group, version) => $"{group}";
                options.SubstituteApiVersionInUrl = true;
            });

        services.AddSwagger(
            true,
            SwaggerConstants.DefaultSwaggerDescriptions);

        // Add health checks for the service and the Dapr side car.
        services
            .AddHealthChecks()
            .AddCheck("self", () => HealthCheckResult.Healthy())
            .AddDapr();

        // Register API DTO validators.
        services.AddValidators(ServiceLifetime.Singleton);

        // Add route handlers that implement minimal API endpoints.
        services.AddRouteHandlerServices();

        // Add exception handling services.
        services.AddExceptionHandlingServices();

        // Add formatted error response support (for reporting formatted error information back to API consumers).
        services.AddErrorResponseServices();
    }

    #endregion Public Methods
}