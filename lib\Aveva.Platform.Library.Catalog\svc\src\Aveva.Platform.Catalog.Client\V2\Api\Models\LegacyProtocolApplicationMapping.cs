// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Api.Models
{
    /// <summary>
    /// Defines the mapping of an application to a legacy Solution and Capability Management (SCM) capability definition. This mapping enables backward compatibility with systems that use the older SCM protocol.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class LegacyProtocolApplicationMapping : IParsable
    {
        /// <summary>The name of the legacy SCM capability definition to which the application corresponds. This identifier is used by legacy systems to recognize and interact with the application.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CapabilityDefinition { get; set; }
#nullable restore
#else
        public string CapabilityDefinition { get; set; }
#endif
        /// <summary>The name of the application to be mapped. This must match an application name defined in the service&apos;s application collection.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Models.LegacyProtocolApplicationMapping"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::Aveva.Platform.Catalog.Client.V2.Api.Models.LegacyProtocolApplicationMapping CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::Aveva.Platform.Catalog.Client.V2.Api.Models.LegacyProtocolApplicationMapping();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "capabilityDefinition", n => { CapabilityDefinition = n.GetStringValue(); } },
                { "name", n => { Name = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("capabilityDefinition", CapabilityDefinition);
            writer.WriteStringValue("name", Name);
        }
    }
}
#pragma warning restore CS0618
