﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;

/// <summary>
/// Defines the supported external identity providers that can be used for authentication and authorization with services.
/// </summary>
public enum ExternalIdentityType
{
    /// <summary>
    /// AVEVA Azure Entra ID integration for research and development environments. This identity type is used for authentication and authorization in non-production environments.
    /// </summary>
    AvevaRnDEntraID,
}