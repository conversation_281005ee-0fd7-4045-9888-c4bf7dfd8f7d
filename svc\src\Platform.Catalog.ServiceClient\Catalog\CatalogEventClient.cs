﻿using System.Text.Json;
using Aveva.Platform.Authentication.Sdk.S2SCommon.Exceptions;
using Aveva.Platform.Catalog.Domain.Events;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Microsoft.Extensions.Configuration;

namespace Aveva.Platform.Catalog.ServiceClient.Catalog
{
    /// <summary>
    /// Client class to communicate with Catalog API service.
    /// </summary>
    public class CatalogEventClient : ICatalogEventClient
    {
        private readonly IConfiguration _config;
        private readonly HttpClient _client;

        /// <summary>
        /// Initializes a new instance of the <see cref="CatalogEventClient"/> class.
        /// </summary>
        /// <param name="client">Http client.</param>
        /// <param name="configuration">App configuration object.</param>
        public CatalogEventClient(IConfiguration configuration, HttpClient client)
        {
            ArgumentNullException.ThrowIfNull(client, nameof(client));
            ArgumentNullException.ThrowIfNull(configuration, nameof(configuration));

            _config = configuration;
            EventsBaseEndPoint = _config.GetValue<string>(key: "Apis:CatalogEventsService:PostEvents");
            _client = client;
        }

        private string? EventsBaseEndPoint { get; set; }

        /// <summary>
        /// Calls Catalog events internal endpoints to create the events.
        /// </summary>
        public async Task PublishEventAsync(string serviceEntryEventType, V1ServiceEntry serviceEntry)
        {
            ArgumentException.ThrowIfNullOrEmpty(serviceEntryEventType, nameof(serviceEntryEventType));
            ArgumentNullException.ThrowIfNull(serviceEntry, nameof(serviceEntry));

            string endpoint = EventsBaseEndPoint + $"{serviceEntryEventType}";

            using var activity = CatalogTraceSource.OperatorTrace.StartActivity("catalog.serviceentry.publishevent", System.Diagnostics.ActivityKind.Client);
            {
                try
                {
                    using var httpRequest = new HttpRequestMessage(HttpMethod.Post, endpoint)
                    {
                        Content = new StringContent(JsonSerializer.Serialize(serviceEntry), new System.Net.Http.Headers.MediaTypeHeaderValue("application/json")),
                    };

                    HttpResponseMessage? response = await _client.SendAsync(httpRequest).ConfigureAwait(false);
                    response.EnsureSuccessStatusCode();
                }
                catch (HttpRequestException requestEx)
                {
                    activity?.AddException(requestEx);
                    activity?.SetStatus(System.Diagnostics.ActivityStatusCode.Error, requestEx.Message);
                    throw;
                }
                catch (ServiceAuthenticationException authEx)
                {
                    activity?.AddException(authEx);
                    activity?.SetStatus(System.Diagnostics.ActivityStatusCode.Error, authEx.Message);
                    throw;
                }
            }
        }

        /// <inheritdoc/>
        public async Task PublishUpdateEventAsync(V1ServiceEntry newServiceEntry, V1ServiceEntry oldServiceEntry)
        {
            ArgumentNullException.ThrowIfNull(oldServiceEntry, nameof(oldServiceEntry));
            ArgumentNullException.ThrowIfNull(newServiceEntry, nameof(newServiceEntry));

            string endpoint = EventsBaseEndPoint + "update";

            using var activity = CatalogTraceSource.OperatorTrace.StartActivity("catalog.serviceentry.publishupdateevent", System.Diagnostics.ActivityKind.Client);
            {
                try
                {
                    var updateRequest = new ServiceEntryUpdateRequest(NewServiceEntry: newServiceEntry, OldServiceEntry: oldServiceEntry);

                    using var httpRequest = new HttpRequestMessage(HttpMethod.Post, endpoint)
                    {
                        Content = new StringContent(
                            JsonSerializer.Serialize(updateRequest),
                            new System.Net.Http.Headers.MediaTypeHeaderValue("application/json")),
                    };

                    var response = await _client.SendAsync(httpRequest).ConfigureAwait(false);
                    response.EnsureSuccessStatusCode();
                }
                catch (HttpRequestException requestEx)
                {
                    activity?.AddException(requestEx);
                    activity?.SetStatus(System.Diagnostics.ActivityStatusCode.Error, requestEx.Message);
                    throw;
                }
                catch (ServiceAuthenticationException authEx)
                {
                    activity?.AddException(authEx);
                    activity?.SetStatus(System.Diagnostics.ActivityStatusCode.Error, authEx.Message);
                    throw;
                }
            }
        }
    }
}