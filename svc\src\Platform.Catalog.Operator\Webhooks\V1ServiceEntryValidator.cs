﻿using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Domain.Validation;
using Aveva.Platform.Catalog.Operator.Logging;
using Aveva.Platform.Catalog.ServiceClient;
using Aveva.Platform.Catalog.ServiceClient.AccountMgmt;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.InstanceMgmt.Client.Ops.Ops.InstanceMgmt.V1.Instances;
using FluentValidation;
using k8s.Models;
using KubeOps.Operator.Web.Webhooks.Admission.Validation;
using Microsoft.Extensions.Options;

namespace Aveva.Platform.Catalog.Operator.Webhooks;

/// <summary>
/// Catalog custom resource validator.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="V1ServiceEntryValidator"/> class.
/// </remarks>
/// <param name="accountMgmtClient">Client for Account Mgmt Api Service.</param>
/// <param name="catalogClient">Client for Catalog Api Service.</param>
/// <param name="catalogEventClient">Client for Catalog Event Service.</param>
/// <param name="instanceMgmtClient">Client for Instance Mgmt Api Service.</param>
/// <param name="logger">ILogger{IServiceEntryValidator, ServiceEntryValidator}.</param>
/// <param name="options">Options.</param>
[ValidationWebhook(typeof(Entities.V1ServiceEntry))]
public class V1ServiceEntryValidator(
    IAccountMgmtClient accountMgmtClient,
    ICatalogClient catalogClient,
    ICatalogEventClient catalogEventClient,
    IInstanceMgmtClient instanceMgmtClient,
    ILogger<V1ServiceEntryValidator> logger,
    IOptionsMonitor<OperatorSettings> options) : ValidationWebhook<Entities.V1ServiceEntry>
{
    /// <summary>
    /// Validates the new ServiceEntry created in Kubernetes cluster.
    /// Triggers the creation of CatalogServiceAdd event.
    /// </summary>
    /// <param name="entity">new service entry.</param>
    /// <param name="dryRun">boolean.</param>
    /// <param name="cancellationToken">The token to monitor for cancellation requests.</param>
    /// <returns>ValidationResult whether true or false.</returns>
    [SuppressMessage("Naming", "CA1031:Do not catch general exception types", Justification = "These are operator classes. Specific exceptions handled within Kubeops.", Scope = "module")]
    public override async Task<ValidationResult> CreateAsync(Entities.V1ServiceEntry entity, bool dryRun, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(entity);
        using var activity = CatalogTraceSource.OperatorTrace.StartActivity("catalog.serviceEntry.create", ActivityKind.Server);
        {
            activity?.AddTag("catalog.serviceentry_id", entity!.Spec.Id);
            activity?.AddTag("catalog.dryrun", dryRun);
            logger.CreateV1ServiceEntry(entity!.Metadata.Name, entity!.ApiVersion, entity!.Metadata.NamespaceProperty, dryRun);
            try
            {
                var existingServiceEntryData = await catalogClient.GetAllAsync().ConfigureAwait(false);
                var skipExistingEntriesValidation = ShouldSkipExistingEntriesValidation(entity);

                CreateCatalogValidator validationRules = new CreateCatalogValidator(existingServiceEntryData, skipExistingEntriesValidation);
                ValidationContext<V1ServiceEntry> validationContext = new ValidationContext<V1ServiceEntry>(entity.Spec);

                var validationResult = await validationRules.ValidateAsync(validationContext, cancellationToken).ConfigureAwait(false);

                if (validationResult.Errors.Count > 0)
                {
                    return Fail(validationResult!.Errors!.FirstOrDefault()!.ToString(), 400);
                }

                if (entity.Spec.HostingType == V1HostingType.External)
                {
                    var validateGeographiesResult = await ValidateGeographies(entity.Spec, null).ConfigureAwait(false);
                    if (validateGeographiesResult.Count > 0)
                    {
                        return Fail(string.Join("; ", validateGeographiesResult), 400);
                    }
                }

                if (!dryRun)
                {
                    await catalogEventClient.PublishEventAsync("create", entity.Spec).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
                activity?.AddException(ex);
                logger.CreateV1ServiceEntryFailed(entity!.Metadata.Name, entity!.ApiVersion, entity!.Metadata.NamespaceProperty, ex.Message, dryRun);
                return Fail(ex.Message, 500);
            }
        }

        return Success();
    }

    /// <summary>
    /// Validates the ServiceEntry updated in Kubernetes cluster.
    /// Triggers the creation of CatalogServiceUpdate event.
    /// </summary>
    /// <param name="oldEntity">old service entry.</param>
    /// <param name="newEntity">updated service entry.</param>
    /// <param name="dryRun">boolean.</param>
    /// <param name="cancellationToken">The token to monitor for cancellation requests.</param>
    /// <returns>ValidationResult whether true or false.</returns>
    [SuppressMessage("Naming", "CA1031:Do not catch general exception types", Justification = "These are operator classes. Specific exceptions handled within Kubeops.", Scope = "module")]
    public override async Task<ValidationResult> UpdateAsync(Entities.V1ServiceEntry oldEntity, Entities.V1ServiceEntry newEntity, bool dryRun, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(oldEntity);
        ArgumentNullException.ThrowIfNull(newEntity);

        if (oldEntity.Spec.Equals(newEntity.Spec))
        {
            return Success();
        }

        using var activity = CatalogTraceSource.OperatorTrace.StartActivity("catalog.serviceEntry.update", ActivityKind.Server);
        {
            activity?.AddTag("catalog.serviceentry_id", newEntity.Spec.Id);
            activity?.AddTag("catalog.dryrun", dryRun);
            logger.UpdateV1ServiceEntry(newEntity.Metadata.Name, newEntity.ApiVersion, newEntity.Metadata.NamespaceProperty, dryRun);

            try
            {
                // Validate the update
                var existingServiceEntryData = await catalogClient.GetAllAsync().ConfigureAwait(false);
                var skipExistingEntriesValidation = ShouldSkipExistingEntriesValidation(newEntity);

                var validationRules = new UpdateCatalogValidator(existingServiceEntryData, skipExistingEntriesValidation);
                var validationContext = new ValidationContext<V1ServiceEntry>(newEntity.Spec);
                var validationResult = await validationRules.ValidateAsync(validationContext, cancellationToken).ConfigureAwait(false);

                if (validationResult?.Errors.Count > 0)
                {
                    return Fail(validationResult.Errors.FirstOrDefault()!.ToString(), 400);
                }

                if (newEntity.Spec.HostingType == V1HostingType.External)
                {
                    var validateGeographiesResult = await ValidateGeographies(newEntity.Spec, oldEntity.Spec).ConfigureAwait(false);
                    if (validateGeographiesResult.Count > 0)
                    {
                        return Fail(string.Join("; ", validateGeographiesResult), 400);
                    }
                }

                if (!ValidLegacyProtocolOptions(newEntity.Spec, oldEntity.Spec))
                {
                    return Fail("The ProtocolOptions for the Legacy Protocol cannot be changed.", 400);
                }

                if (EnabledChangedToDisabled(oldEntity, newEntity) &&
                    InstancesExist(newEntity.Spec.Id))
                {
                    return Fail("Enabled cannot be set to false when instances of this service exists.", 400);
                }

                var dependencyConfigValidationResult = ValidDependencyConfigOptions(newEntity.Spec, oldEntity.Spec);
                if (dependencyConfigValidationResult.Count > 0)
                {
                    return Fail(string.Join("; ", dependencyConfigValidationResult), 400);
                }

                if (!dryRun)
                {
                    // Use the new specific method for updates
                    await catalogEventClient
                        .PublishUpdateEventAsync(newServiceEntry: newEntity.Spec, oldServiceEntry: oldEntity.Spec)
                        .ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
                activity?.AddException(ex);
                logger.UpdateV1ServiceEntryFailed(newEntity.Metadata.Name, newEntity.ApiVersion, newEntity.Metadata.NamespaceProperty, ex.Message, dryRun);
                return Fail(ex.Message, 500);
            }
        }

        return Success();
    }

    /// <summary>
    /// Triggers the creation of CatalogServiceDelete event.
    /// </summary>
    /// <param name="entity">Catalog service entry.</param>
    /// <param name="dryRun">boolean.</param>
    /// <param name="cancellationToken">The token to monitor for cancellation requests.</param>
    /// <returns>ValidationResult whether true or false.</returns>
    [SuppressMessage("Naming", "CA1031:Do not catch general exception types", Justification = "These are operator classes. Specific exceptions handled within Kubeops.", Scope = "module")]
    public override async Task<ValidationResult> DeleteAsync(Entities.V1ServiceEntry entity, bool dryRun, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(entity);
        using var activity = CatalogTraceSource.OperatorTrace.StartActivity("catalog.serviceEntry.delete", ActivityKind.Server);
        {
            activity?.AddTag("catalog.serviceentry_id", entity!.Spec.Id);
            activity?.AddTag("catalog.dryrun", dryRun);
            logger.DeleteV1ServiceEntry(entity.Metadata.Name, entity.ApiVersion, entity.Metadata.NamespaceProperty, dryRun);

            try
            {
                if (!dryRun)
                {
                    await catalogEventClient.PublishEventAsync("delete", entity.Spec).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
                activity?.AddException(ex);
                logger.DeleteV1ServiceEntryFailed(entity.Metadata.Name, entity.ApiVersion, entity.Metadata.NamespaceProperty, ex.Message, dryRun);
                return Fail(ex.Message, 500);
            }
        }

        return Success();
    }

    private static bool EnabledChangedToDisabled(Entities.V1ServiceEntry oldEntity, Entities.V1ServiceEntry newEntity)
    {
        var newEntityDisabled = newEntity.Spec.Availability != null && (newEntity.Spec.Availability.Enabled == false);
        var oldEntityDisabled = oldEntity.Spec.Availability != null && (oldEntity.Spec.Availability.Enabled == false);
        return newEntityDisabled && !oldEntityDisabled;
    }

    private static bool ValidLegacyProtocolOptions(V1ServiceEntry newEntry, V1ServiceEntry oldEntry)
    {
        if (newEntry.Lifecycle.Protocol != V1IntegrationProtocol.Legacy)
        {
            return true;
        }

        if (newEntry.Lifecycle.ProtocolOptions == null)
        {
            return false;
        }

        return oldEntry.Lifecycle.Protocol != V1IntegrationProtocol.Legacy ||
               newEntry.Lifecycle.ProtocolOptions.Equals(oldEntry.Lifecycle.ProtocolOptions);
    }

    private static List<string> ValidDependencyConfigOptions(V1ServiceEntry newEntry, V1ServiceEntry oldEntry)
    {
        var errors = new List<string>();

        var oldEntryDependencies = oldEntry.Dependencies ?? [];
        var newEntryDependencies = newEntry.Dependencies ?? [];

        errors.AddRange(ValidateRemovedConfigs(oldEntryDependencies, newEntryDependencies));
        errors.AddRange(ValidateUpdatedConfigs(oldEntryDependencies, newEntryDependencies));
        errors.AddRange(ValidateAddedConfigs(oldEntryDependencies, newEntryDependencies));

        return errors;
    }

    private static IEnumerable<string> ValidateRemovedConfigs(Dictionary<string, V1CatalogDataDependency> oldEntryDependencies, Dictionary<string, V1CatalogDataDependency> newEntryDependencies)
    {
        foreach (var oldEntryDependency in oldEntryDependencies)
        {
            var oldDependencyConfig = oldEntryDependency.Value.Config ?? new Dictionary<string, V1CatalogDataDependencyConfig>();

            if (newEntryDependencies.TryGetValue(oldEntryDependency.Key, out V1CatalogDataDependency? newEntryDependency))
            {
                var newDependencyConfig = newEntryDependency.Config ?? new Dictionary<string, V1CatalogDataDependencyConfig>();

                foreach (var oldConfig in oldDependencyConfig)
                {
                    if (oldConfig.Value.Required && !newDependencyConfig.ContainsKey(oldConfig.Key))
                    {
                        yield return $"Required config cannot be removed for dependency: {oldEntryDependency.Key} config: {oldConfig.Key}";
                    }
                }
            }
            else
            {
                foreach (var config in oldDependencyConfig)
                {
                    if (config.Value.Required)
                    {
                        yield return $"Required config cannot be removed for dependency: {oldEntryDependency.Key} config: {config.Key}";
                    }
                }
            }
        }
    }

    private static IEnumerable<string> ValidateUpdatedConfigs(Dictionary<string, V1CatalogDataDependency> oldEntryDependencies, Dictionary<string, V1CatalogDataDependency> newEntryDependencies)
    {
        foreach (var oldEntryDependency in oldEntryDependencies)
        {
            var oldDependencyConfig = oldEntryDependency.Value.Config ?? new Dictionary<string, V1CatalogDataDependencyConfig>();

            if (!newEntryDependencies.TryGetValue(oldEntryDependency.Key, out var newDependency))
            {
                continue;
            }

            var newDependencyConfig = newDependency.Config ?? new Dictionary<string, V1CatalogDataDependencyConfig>();

            foreach (var oldConfig in oldDependencyConfig)
            {
                if (!newDependencyConfig.TryGetValue(oldConfig.Key, out var newConfig))
                {
                    continue;
                }

                if (oldConfig.Value.Required && !newConfig.Required)
                {
                    yield return $"Required config cannot be changed to optional for dependency: {oldEntryDependency.Key} config: {oldConfig.Key}";
                }

                if (!oldConfig.Value.Required && newConfig.Required)
                {
                    yield return $"Optional config cannot be changed to required for dependency: {oldEntryDependency.Key} config: {oldConfig.Key}";
                }

                if (newConfig.Min.HasValue && !oldConfig.Value.Min.HasValue)
                {
                    yield return $"Config min cannot be updated from null for dependency: {oldEntryDependency.Key} config: {oldConfig.Key}";
                }

                if (newConfig.Max.HasValue && !oldConfig.Value.Max.HasValue)
                {
                    yield return $"Config max cannot be updated from null for dependency: {oldEntryDependency.Key} config: {oldConfig.Key}";
                }

                if (newConfig.Min > oldConfig.Value.Min)
                {
                    yield return $"Config min cannot be increased for dependency: {oldEntryDependency.Key} config: {oldConfig.Key}";
                }

                if (newConfig.Max < oldConfig.Value.Max)
                {
                    yield return $"Config max cannot be decreased for dependency: {oldEntryDependency.Key} config: {oldConfig.Key}";
                }
            }
        }
    }

    private static IEnumerable<string> ValidateAddedConfigs(Dictionary<string, V1CatalogDataDependency> oldEntryDependencies, Dictionary<string, V1CatalogDataDependency> newEntryDependencies)
    {
        foreach (var newEntryDependency in newEntryDependencies)
        {
            var newDependencyConfig = newEntryDependency.Value.Config ?? new Dictionary<string, V1CatalogDataDependencyConfig>();

            if (oldEntryDependencies.TryGetValue(newEntryDependency.Key, out var oldDependency))
            {
                var oldDependencyConfig = oldDependency.Config ?? new Dictionary<string, V1CatalogDataDependencyConfig>();

                foreach (var newConfig in newDependencyConfig.Where(newConfig => newConfig.Value.Required && !oldDependencyConfig.ContainsKey(newConfig.Key)))
                {
                    yield return $"Required config cannot be added to existing dependency: {newEntryDependency.Key} config: {newConfig.Key}";
                }
            }
            else
            {
                foreach (var config in newDependencyConfig.Where(config => config.Value.Required))
                {
                    yield return $"Required config cannot be added to existing dependency: {newEntryDependency.Key} config: {config.Key}";
                }
            }
        }
    }

    private bool ShouldSkipExistingEntriesValidation(Entities.V1ServiceEntry entity)
    {
        // ON CONNECT entities are applied all at once during a flux reconciliation. If one service entry
        // is dependent on another service entry, both ON CONNECT, they will never reconcile if we include this validation.
        // Therefore, we skip validation against existing service entries if the service entry is ON CONNECT.
        return string.Equals(
            entity.Metadata.Namespace(),
            options.CurrentValue.OnConnectNamespace,
            StringComparison.OrdinalIgnoreCase);
    }

    private bool InstancesExist(string serviceId)
    {
        var exisitngInstances = instanceMgmtClient.GetInstancesAsync(
            new InstancesRequestBuilder.InstancesRequestBuilderGetQueryParameters
            {
                ServiceId = [serviceId],
                Limit = 1,
            }).GetAwaiter().GetResult();

        return exisitngInstances.Any();
    }

    private async Task<List<string>> ValidateGeographies(V1ServiceEntry newEntry, V1ServiceEntry? oldEntry)
    {
        var result = new List<string>();

        if (newEntry.Geographies == null || newEntry.Geographies.Count == 0)
        {
            return result;
        }

        var geographies = (await accountMgmtClient.QueryGeographiesAsync().ConfigureAwait(false)).ToList();

        result.AddRange(from geography in newEntry.Geographies
            where !geographies.Exists(x => x.Id!.Equals(geography.Id, StringComparison.OrdinalIgnoreCase))
            select $"Invalid geography {geography.Id} specified : Not Found.");

        if (oldEntry?.Geographies == null || oldEntry.Geographies.Count == 0)
        {
            return result;
        }

        foreach (V1Geography geography in oldEntry.Geographies)
        {
            if (newEntry.Geographies.Exists(x => x.Id.Equals(geography.Id, StringComparison.OrdinalIgnoreCase)))
            {
                continue;
            }

            var queryInstanceParams = new InstancesRequestBuilder.InstancesRequestBuilderGetQueryParameters
            {
                ServiceId = [newEntry.Id],
                Geography = geography.Id,
                Limit = 1,
            };

            var instances = await instanceMgmtClient.GetInstancesAsync(queryInstanceParams).ConfigureAwait(false);
            if (instances.Any())
            {
                result.Add($"Can not remove geography {geography.Id} as it's in use.");
            }
        }

        return result;
    }
}