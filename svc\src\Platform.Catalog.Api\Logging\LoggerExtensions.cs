﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Logging;

namespace Aveva.Platform.Catalog.Api.Logging;

[ExcludeFromCodeCoverage(Justification = "LoggerMessage logging pattern implementation")]
internal static partial class LoggerExtensions
{
    #region Internal Methods

    [LoggerMessage((int)LoggerEvents.UnAuthorizedAccess, LogLevel.Error, "The client is unauthorized")]
    internal static partial void UnauthorizedAccess(this ILogger logger);

    [LoggerMessage((int)LoggerEvents.ServiceEntryWatcherError, LogLevel.Warning, "ServiceEntry Watcher Error: {message}")]
    internal static partial void ServiceEntryWatcherError(this ILogger logger, string message);
    #endregion Internal Methods
}