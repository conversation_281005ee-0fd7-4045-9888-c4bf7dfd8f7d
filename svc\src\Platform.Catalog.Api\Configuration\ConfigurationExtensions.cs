﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;

namespace Aveva.Platform.Catalog.Api.Configuration;

/// <summary>
/// Extension methods for accessing <see cref="IConfiguration"/> for type-safe settings retrieval.
/// </summary>
[ExcludeFromCodeCoverage(Justification = "Unused class")]
internal static class ConfigurationExtensions
{
    #region Internal Methods

    /// <summary>
    /// Determines whether or not Swagger is enabled for the specified configuration.
    /// </summary>
    /// <param name="configuration">The configuration.</param>
    /// <returns><c>true</c> if Swagger is enabled for the specified configuration; otherwise, <c>false</c>.</returns>
    internal static bool IsSwaggerUIEnabled(this IConfiguration configuration)
    {
        return configuration?.GetValue<bool>(ServiceConfiguration.Settings.EnableSwaggerUISetting) ?? false;
    }

    /// <summary>
    /// Returns the configured value representing the human readable title for the Swagger / Open API document for this service (e.g., <c>MyService API</c>).
    /// </summary>
    /// <value>The human readable title for the Swagger / Open API endpoint for this service.</value>
    /// <param name="configuration">The configuration.</param>
    /// <returns>The human readable title for the Swagger / Open API endpoint for this service or an empty string if not set.</returns>
    internal static string SwaggerServiceTitle(this IConfiguration configuration)
    {
        return configuration?.GetValue<string>(ServiceConfiguration.Settings.SwaggerServiceTitleSetting) ?? string.Empty;
    }

    /// <summary>
    /// Returns the configured value representing the human readable description for the Swagger / Open API document for this service (e.g., <c>MyService API</c>).
    /// </summary>
    /// <value>The human readable description for the Swagger / Open API endpoint for this service.</value>
    /// <param name="configuration">The configuration.</param>
    /// <returns>The human readable description for the Swagger / Open API endpoint for this service or an empty string if not set.</returns>
    internal static string SwaggerServiceDescription(this IConfiguration configuration)
    {
        return configuration?.GetValue<string>(ServiceConfiguration.Settings.SwaggerServiceDescriptionSetting) ?? string.Empty;
    }

    /// <summary>
    /// Returns the configured value representing the human readable name for the Swagger / Open API document for this service (e.g., <c>MyService API</c>).
    /// </summary>
    /// <value>The human readable name for the Swagger / Open API endpoint for this service.</value>
    /// <param name="configuration">The configuration.</param>
    /// <returns>The human readable name for the Swagger / Open API endpoint for this service or an empty string if not set.</returns>
    internal static string SwaggerDocumentName(this IConfiguration configuration)
    {
        return configuration?.GetValue<string>(ServiceConfiguration.Settings.SwaggerDocumentNameSetting) ?? string.Empty;
    }

    /// <summary>
    /// Retuns the configured value representing the environment-independent API service route prefix path for the Swagger / Open API endpoint for this service (e.g.,
    /// <c>api-docs/MyService</c> portion of localhost/api-docs/MyService/swagger.json).
    /// </summary>
    /// <value>The environment-independent service route prefix path for the Swagger / Open API endpoint for this service.</value>
    /// <param name="configuration">The configuration.</param>
    /// <returns>The environment-independent service route prefix path for the Swagger / Open API endpoint for this service or an empty string if not set.</returns>
    internal static string SwaggerServiceApiRoutePrefix(this IConfiguration configuration)
    {
        return configuration?.GetValue<string>(ServiceConfiguration.Settings.SwaggerServiceApiRoutePrefixSetting) ?? string.Empty;
    }

    #endregion Internal Methods
}