﻿using Asp.Versioning.ApiExplorer;
using Aveva.Platform.Catalog.Events.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.OpenApi.Models;
using Moq;
using Shouldly;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Aveva.Platform.Catalog.Events.Tests.Unit.Configuration;

/// <summary>
/// <see cref="ConfigureSwaggerOptions"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "App")]
[Trait("Category", "Unit")]
[Trait("Category", "App.Unit")]
[Trait("Tag", "Configuration")]
public class ConfigureSwaggerOptionsTests
{
    #region Test Cases

    [Fact]
    public void ConfigureSwaggerOptions_Configure()
    {
        // Arrange
        List<ApiVersionDescription> apiVersionDescriptions = new List<ApiVersionDescription>()
        {
            new ApiVersionDescription(new Asp.Versioning.ApiVersion(1.0), "V1"),
        };
        var testTitle = "Test title";
        var testDescription = "Test description";
        var applicationSettings = new Dictionary<string, string?>
        {
            { "Application:SwaggerServiceTitle", testTitle },
            { "Application:SwaggerServiceDescription", testDescription },
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(applicationSettings)
            .Build();

        Mock<IApiVersionDescriptionProvider> apiVersionDescriptionProviderMock = new Mock<IApiVersionDescriptionProvider>();
        Dictionary<string, OpenApiInfo> swaggerDocs = new Dictionary<string, OpenApiInfo>();
        SwaggerGeneratorOptions swaggerGeneratorOptions = new SwaggerGeneratorOptions
        {
            SwaggerDocs = swaggerDocs,
        };
        SwaggerGenOptions swaggerGenOptions = new SwaggerGenOptions
        {
            SwaggerGeneratorOptions = swaggerGeneratorOptions,
        };
        apiVersionDescriptionProviderMock
            .SetupGet(x => x.ApiVersionDescriptions)
            .Returns(apiVersionDescriptions);
        ConfigureSwaggerOptions subject = new ConfigureSwaggerOptions(apiVersionDescriptionProviderMock.Object, configuration);

        // Act
        subject.Configure(swaggerGenOptions);

        // Assert
        swaggerDocs.ShouldSatisfyAllConditions(
            () => swaggerDocs.Count.ShouldBe(1),
            () => swaggerDocs.ShouldContainKey("V1"),
            () => swaggerDocs["V1"].Title.ShouldBe(testTitle),
            () => swaggerDocs["V1"].Version.ShouldBe("1.0"),
            () => swaggerDocs["V1"].Description.ShouldBe(testDescription));
    }

    #endregion Test Cases
}