﻿using Aveva.Platform.InstanceMgmt.Client.Ops.Models;
using Aveva.Platform.InstanceMgmt.Client.Ops.Ops.InstanceMgmt.V1.Instances;

namespace Aveva.Platform.Catalog.ServiceClient;

/// <summary>
/// Instance Mgmt class to communicate with Instance Mgt API service.
/// </summary>
public class InstanceMgmtClient(InstanceMgmt.Client.Ops.Client client) : IInstanceMgmtClient
{
    /// <summary>
    /// Get instances based on a filter.
    /// </summary>
    public async Task<IEnumerable<InstanceResponse>> GetInstancesAsync(InstancesRequestBuilder.InstancesRequestBuilderGetQueryParameters request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(request);

        var instances = new List<InstanceResponse>();
        do
        {
            var response = (await client.Ops.InstanceMgmt.V1.Instances
                .GetAsync(options => options.QueryParameters = request, cancellationToken: cancellationToken)
                .ConfigureAwait(false)) ?? new InstanceCollectionResponse();
            request.ContinuationToken = response.ContinuationToken;
            instances.AddRange(response?.Items ?? Enumerable.Empty<InstanceResponse>());
        }
        while (!string.IsNullOrEmpty(request.ContinuationToken) && request.Limit == null);
        return instances;
    }
}