﻿using Aveva.Platform.Catalog.Domain.Instrumentation;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Instrumentation;

/// <summary>
/// <see cref="CatalogMetricsTests"/> unit tests class.
/// </summary>
[Trait("Category", "Unit")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Unit")]
public static class CatalogMetricsTests
{
    #region Test Cases

    [Fact]
    public static void CatalogMetrics_Init_Sucessfull()
    {
        // Act & Assert
        Should.NotThrow(() =>
        {
            var catalogMetrics = new CatalogMetrics();
            catalogMetrics.RecordServiceEntryCount(10);
        });
    }

    [Fact]
    public static void CatalogMetrics_RecordWatcherStatus_NotThrows()
    {
        // Act & Assert
        Should.NotThrow(() =>
        {
            var catalogMetrics = new CatalogMetrics();
            catalogMetrics.RecordWatcherStatus(1);
        });
    }
    #endregion Test Cases
}