deploymentMode: local

# -- Dapr Configuration
dapr:
  # -- Enables dapr which is required to talk to services in the cluster for example InstMgmt and Accounts
  enabled: true
  cacheStateStore:
    # -- Enables the dapr cache state store.
    # This needs to be enabled when using any Resolver and Cache that requires a dapr state store.
    # Useful to disable locally and test independently of dapr.
    enabled: false
  envPubSub:
    # -- Enables the environment pubsub
    # This needs to be enabled when events.enabled true otherwise the component it requires will not be available.
    enabled: true
    namespaceName: "helloworld.servicebus.windows.net"
    consumerId: "catalog"
    name: environment-pubsub

# -- Catalog Api service Configuration
api:
  runAsNonRoot: false
  image:
    pullPolicy: IfNotPresent
  scaling:
    maxReplicas: 1
  readinessProbe:
    periodSeconds: 120
  env:
    Instrumentation__DebugEnabled: "false"
    Instrumentation__OtelExporterEndpoint: "http://jaeger-all-in-one.jaeger.svc.cluster.local:4317"

# -- Catalog events service Configuration
events:
  runAsNonRoot: false
  image:
    pullPolicy: IfNotPresent
  scaling:
    maxReplicas: 1
  env:
    Instrumentation__DebugEnabled: "false"
    Instrumentation__OtelExporterEndpoint: "http://jaeger-all-in-one.jaeger.svc.cluster.local:4317"
  livenessProbe:
    initialDelaySeconds: 5
    periodSeconds: 60
    timeoutSeconds: 10
  readinessProbe:
    initialDelaySeconds: 5
    periodSeconds: 120
    timeoutSeconds: 10
  startupProbe:
    initialDelaySeconds: 5
    timeoutSeconds: 10

# -- Catalog Operator Configuration
operator:
  runAsNonRoot: false
  dapr:
    sidecarCpuRequest: "10m"
    sidecarCpuLimit: "150m"
    sidecarMemoryRequest: "16Mi"
    sidecarMemoryLimit: "256Mi"
  timeoutSeconds: 30
  image:
    tag: latest
    pullPolicy: IfNotPresent
  env:
    Instrumentation__DebugEnabled: "false"
    Instrumentation__OtelExporterEndpoint: "http://jaeger-all-in-one.jaeger.svc.cluster.local:4317"
    Authentication__DebugDisabled: "true"
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 25m
      memory: 64Mi

buildVariables:
  tag: latest

environment:
  containerRegistryServer: ""
  identityDnsZoneName: "identity.infradev.platform.capdev-connect.aveva.com"
  environmentType: "Development"
  environment: "eucliddev"

scaling:
  cpuRequested: 25m
  cpuLimit: 75m
  memoryRequested: 64Mi
  memoryLimit: 256Mi

# -- EDA pipeline configuration
# -- Certain variable are expected as part of the EDA pipeline.
region:
  region: eastus2
  geography: us
geography:
  name: us

instance:
  workloadIdentity: workloadIdentityClientId
