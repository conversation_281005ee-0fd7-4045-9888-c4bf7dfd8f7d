﻿using System.ComponentModel.DataAnnotations;

namespace Aveva.Platform.Catalog.Domain.Models
{
    /// <summary>
    /// Service Entry.
    /// </summary>
    public class V1ServiceEntry
    {
        #region Public Properties

#pragma warning disable CS8618
        /// <summary>
        /// Gets or sets the service entry identifier.
        /// </summary>
        /// <value>The identifier for the service entry.</value>
        [Required]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets icon url of the service.
        /// </summary>
        public Uri? IconUrl { get; set; }

        /// <summary>
        /// Gets or sets the description of the service entry .
        /// </summary>
        /// <value>The description of the service entry.</value>
        public string? Description { get; set; }

        /// <summary>
        /// Gets or sets the name of the service entry.
        /// </summary>
        /// <value>The name of the service entry.</value>
        [Required]
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the category of the service entry.
        /// </summary>
        /// <value>The category of the service entry.</value>
        public V1Category? Category { get; set; }

        /// <summary>
        /// Gets or sets hosting type of the service entry.
        /// </summary>
        /// <value>The hosting type of the service entry.</value>
        [Required]
        public V1HostingType HostingType { get; set; }

        /// <summary>
        /// Gets or sets Tags.
        /// </summary>
    #pragma warning disable CA2227 // required for serialization
        public List<string>? Tags { get; set; }
    #pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// Gets or sets Lifecycle.
        /// </summary>
        [Required]
        public V1Lifecycle Lifecycle { get; set; }

        /// <summary>
        /// Gets or sets Dependencies of the service entry.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public Dictionary<string, V1CatalogDataDependency>? Dependencies { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// Gets or sets applications of the service entry.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public List<V1Application>? Applications { get; set; }
    #pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// Gets or sets the allowed geographies.
        /// </summary>
    #pragma warning disable CA2227 // Collection properties should be read only
        public List<V1Geography>? Geographies { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only
#pragma warning restore CS8618

        /// <summary>
        /// Gets or sets the availability of the service.
        /// </summary>
        public V1ServiceAvailability? Availability { get; set; }

        /// <summary>
        /// Gets or sets the availability of the service.
        /// </summary>
    #pragma warning disable CA2227 // Collection properties should be read only
        public List<V1ExternalIdentity>? ExternalIdentities { get; set; }
    #pragma warning restore CA2227 // Collection properties should be read only

        #endregion Public Properties

        #region Public Methods

        /// <summary>
        /// Updates the current instance from another instance.
        /// </summary>
        /// <param name="from">The instance to use as the source of the update to this instance's values.</param>
        /// <returns>The updated instance.</returns>
        /// <remarks>This update process encapsulates the rules about what can and can't be updated and in what manner.</remarks>
        public V1ServiceEntry UpdateFrom(V1ServiceEntry from)
        {
            ArgumentNullException.ThrowIfNull(from);

            DisplayName = from.DisplayName;
            Category = from.Category;
            HostingType = from.HostingType;
            IconUrl = from.IconUrl;
            Description = from.Description;
            Tags = from.Tags;
            Lifecycle = from.Lifecycle;
            Dependencies = from.Dependencies;
            Applications = from.Applications;
            Geographies = from.Geographies;
            Availability = from.Availability;
            ExternalIdentities = from.ExternalIdentities;
            return this;
        }

        /// <summary>
        /// Rename the CatalogData item.
        /// </summary>
        public void Rename(string displayName)
        {
            DisplayName = displayName;
        }

        #region Overrides

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj is not V1ServiceEntry item)
            {
                return false;
            }

            var result =
                string.Equals(item.Id, Id, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.IconUrl?.ToString(), IconUrl?.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.Description, Description, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.DisplayName, DisplayName, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.HostingType.ToString(), HostingType.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.Category.ToString(), Category.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && ((item.Availability == null && Availability == null) || (item.Availability != null && item.Availability.Equals(Availability)))
                && ((item.Lifecycle == null && Lifecycle == null) || (item.Lifecycle != null && item.Lifecycle.Equals(Lifecycle)));

            if (Dependencies == null && item.Dependencies != null)
            {
                if (item.Dependencies.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Dependencies != null && item.Dependencies == null)
            {
                if (Dependencies.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Dependencies != null && item.Dependencies != null)
            {
                result = result
                    && (Dependencies.Count == item.Dependencies.Count)
                    && (Dependencies.Except(item.Dependencies).Any() == false)
                    && (item.Dependencies.Except(Dependencies).Any() == false)
                    && item.Dependencies.Aggregate(result, (current, dependency) => current && Dependencies[dependency.Key].Equals(dependency.Value));
            }

            if (Applications == null && item.Applications != null)
            {
                if (item.Applications.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Applications != null && item.Applications == null)
            {
                if (Applications.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Applications != null && item.Applications != null)
            {
                result = result
                         && (Applications.Count == item.Applications.Count)
                         && !(from application in Applications
                             let match = item.Applications.Find(x => x.Name.Equals(application.Name, StringComparison.Ordinal))
                             where !application.Equals(match)
                             select application).Any();
            }

            if (Geographies == null && item.Geographies != null)
            {
                if (item.Geographies.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Geographies != null && item.Geographies == null)
            {
                if (Geographies.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Geographies != null && item.Geographies != null)
            {
                result = result
                         && (Geographies.Count == item.Geographies.Count)
                         && !(from geography in Geographies
                              let match = item.Geographies.Find(x => x.Id.Equals(geography.Id, StringComparison.Ordinal))
                             where !geography.Equals(match)
                             select geography).Any();
            }

            if (ExternalIdentities == null && item.ExternalIdentities != null)
            {
                if (item.ExternalIdentities.Count > 0)
                {
                    result = result && false;
                }
            }

            if (ExternalIdentities != null && item.ExternalIdentities == null)
            {
                if (ExternalIdentities.Count > 0)
                {
                    result = result && false;
                }
            }

            if (ExternalIdentities != null && item.ExternalIdentities != null)
            {
                result = result
                         && (ExternalIdentities.Count == item.ExternalIdentities.Count)
                         && !(from externalIdentity in ExternalIdentities
                              let match = item.ExternalIdentities.Find(x => x.Id.Equals(externalIdentity.Id, StringComparison.Ordinal))
                              where !externalIdentity.Equals(match)
                              select externalIdentity).Any();
            }

            return result;
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return Id.GetHashCode(StringComparison.InvariantCulture);
        }

        /// <inheritdoc/>
        public override string ToString()
        {
            return $"ServiceEntry {Id} with name [{DisplayName}].";
        }

        #endregion Overrides

        #endregion Public Methods
    }
}