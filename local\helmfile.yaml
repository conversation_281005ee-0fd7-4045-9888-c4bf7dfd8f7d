repositories:
- name: dapr
  url: https://dapr.github.io/helm-charts
- name: jetstack
  url: https://charts.jetstack.io
- name: bitnami
  url: https://charts.bitnami.com/bitnami
- name: cloudplatformbuild
  url: cloudplatformbuildregistry.azurecr.io
  oci: true
- name: jaeger-all-in-one
  url: https://raw.githubusercontent.com/hansehe/jaeger-all-in-one/master/helm/charts

releases:
- name: dapr
  namespace: dapr-system
  chart: dapr/dapr
  wait: true
  version: 1.14.4

- name: rabbitmq
  namespace: rabbitmq
  chart: bitnami/rabbitmq
  set:
    - name: auth.username
      value: fish
    - name: auth.password
      value: bowl
    - name: auth.erlangCookie
      value: secretcookie

- name: jaeger-all-in-one
  namespace: jaeger
  chart: jaeger-all-in-one/jaeger-all-in-one
  set:
    - name: enableHttpOpenTelemetryCollector
      value: true

- name: cert-manager
  chart: jetstack/cert-manager
  version: v1.15.0
  namespace: cert-manager
  wait: true
  hooks:
  - events: ["presync"]
    showlogs: true
    command: "kubectl"
    args:
        - "apply"
        - "-f"
        - "https://github.com/cert-manager/cert-manager/releases/download/v1.15.0/cert-manager.crds.yaml"

- name: gateway-mock
  namespace: platform-gateway
  chart: ./gateway-mock
  needs:
    - dapr-system/dapr
    
- name: authentication-mock
  namespace: platform-identity
  chart: ./authentication-mock
  needs:
    - dapr-system/dapr

- name: platform-catalog
  namespace: platform-catalog
  chart: ../deploy/helm
  disableValidationOnInstall: true
  hooks:
  - events: ["presync"]
    showlogs: true
    command: "kubectl"
    args:
        - "apply"
        - "-f"
        - "https://github.com/cert-manager/cert-manager/releases/download/v1.15.0/cert-manager.crds.yaml"
  needs:
    - dapr-system/dapr
    - cert-manager/cert-manager
    - rabbitmq/rabbitmq
    - jaeger/jaeger-all-in-one
    - platform-gateway/gateway-mock
    - platform-identity/authentication-mock
  values:
    - ../deploy/helm/local.values.yaml 
  set:
  - name: deploymentMode
    value: local    