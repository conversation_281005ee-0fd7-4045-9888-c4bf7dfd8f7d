apiVersion: chaos-mesh.org/v1alpha1
kind: Schedule
metadata:
  name: stress-memory
  namespace: chaos-mesh
spec:
  schedule: "@every 30s"
  type: StressChaos
  historyLimit: 5 
  concurrencyPolicy: Allow
  stressChaos:
    mode: all  
    selector:
      namespaces: 
        - platform-catalog
      labelSelectors:
        pod-selector: catalog-api
    stressors:
      memory:
        workers: 8
        size: '90%'
    duration: "25s"