﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;

/// <summary>
/// Defines the catalog service allowed geographies. Geographies represent regions where a service can be deployed and accessed.
/// </summary>
public class Geography
{
    /// <summary>
    /// Initializes a new instance of the <see cref="Geography"/> class.
    /// </summary>
    public Geography() // Required by EF
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="Geography"/> class with the specified ID.
    /// </summary>
    public Geography(string id)
    {
        Id = id;
    }

    /// <summary>
    /// The geography identifier. This unique code represents a specific geographic region where a service can be deployed and accessed.
    /// </summary>
    public string? Id { get; set; }

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        if (obj is not Geography item)
        {
            return false;
        }

        var result = string.Equals(item.Id, Id, StringComparison.InvariantCultureIgnoreCase);

        return result;
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
}