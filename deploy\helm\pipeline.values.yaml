deploymentMode: pipeline

# -- Dapr Configuration
dapr:
  # -- Enables dapr which is required to talk to services in the cluster for example InstMgmt and Accounts
  enabled: true
  cacheStateStore:
    # -- Enables the dapr cache state store.
    # This needs to be enabled when using any Resolver and Cache that requires a dapr state store.
    # Useful to disable locally and test independently of dapr.
    enabled: false
  envPubSub:
    # -- Enables the environment pubsub
    # This needs to be enabled when events.enabled true otherwise the component it requires will not be available.
    enabled: true
    namespaceName: "helloworld.servicebus.windows.net"
    consumerId: "catalog"
    name: environment-pubsub

# -- Catalog Api service Configuration
api:
  scaling:
    maxReplicas: 10

# -- Catalog Operator Configuration
operator:
  image:
    pullPolicy: IfNotPresent
    tag: PLACEHOLDER
  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 75m
      memory: 64Mi

geography:
  name: PLACEHOLDER

envBicepOp:
  workloadClientIdsForGeographies:
    - item:
      geography: PLACEHOLDER

buildVariables:
  tag: PLACEHOLDER

environment:
  environmentType: PLACEHOLDER

scaling:
  cpuRequested: 75m
  cpuLimit: 500m
  memoryRequested: 200Mi
  memoryLimit: 500Mi