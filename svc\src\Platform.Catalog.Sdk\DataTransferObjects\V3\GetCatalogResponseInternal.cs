﻿﻿namespace Aveva.Platform.Catalog.Sdk.DataTransferObjects.V3;

/// <summary>
/// Dto.
/// </summary>
public record GetCatalogResponseInternal : GetCatalogResponseBase
{
    #region Public Properties

    /// <summary>
    /// Gets hostingType.
    /// </summary>
    public string? HostingType { get; init; }

    /// <summary>
    /// Gets tags.
    /// </summary>
    public List<string>? Tags { get; init; }

    /// <summary>
    /// Gets lifecycle.
    /// </summary>
    public Lifecycle? Lifecycle { get; init; }

    #endregion Public Properties
}