/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { createServiceResponseFromDiscriminatorValue, type ServiceResponse } from '../../../../../models/index.js';
// @ts-ignore
import { type BaseRequestBuilder, type Parsable, type ParsableFactory, type RequestConfiguration, type RequestInformation, type RequestsMetadata } from '@microsoft/kiota-abstractions';

/**
 * Builds and executes requests for operations under /ops/catalog/v2/services/{id}
 */
export interface ServicesItemRequestBuilder extends BaseRequestBuilder<ServicesItemRequestBuilder> {
    /**
     * Gets a specific catalog service entry by its unique `id`. This operations endpoint returns detailed information about a single service, including its availability configuration for different accounts.
     * @param requestConfiguration Configuration for the request such as headers, query parameters, and middleware options.
     * @returns {Promise<ServiceResponse>}
     */
     get(requestConfiguration?: RequestConfiguration<ServicesItemRequestBuilderGetQueryParameters> | undefined) : Promise<ServiceResponse | undefined>;
    /**
     * Gets a specific catalog service entry by its unique `id`. This operations endpoint returns detailed information about a single service, including its availability configuration for different accounts.
     * @param requestConfiguration Configuration for the request such as headers, query parameters, and middleware options.
     * @returns {RequestInformation}
     */
     toGetRequestInformation(requestConfiguration?: RequestConfiguration<ServicesItemRequestBuilderGetQueryParameters> | undefined) : RequestInformation;
}
/**
 * Gets a specific catalog service entry by its unique `id`. This operations endpoint returns detailed information about a single service, including its availability configuration for different accounts.
 */
export interface ServicesItemRequestBuilderGetQueryParameters {
    /**
     * The unique identifier of the account. This ID represents the specific account that will access or own the resources being managed.
     */
    accountId?: string;
}
/**
 * Uri template for the request builder.
 */
export const ServicesItemRequestBuilderUriTemplate = "{+baseurl}/ops/catalog/v2/services/{id}{?accountId*}";
/**
 * Metadata for all the requests in the request builder.
 */
export const ServicesItemRequestBuilderRequestsMetadata: RequestsMetadata = {
    get: {
        uriTemplate: ServicesItemRequestBuilderUriTemplate,
        responseBodyContentType: "application/json",
        adapterMethodName: "send",
        responseBodyFactory:  createServiceResponseFromDiscriminatorValue,
    },
};
/* tslint:enable */
/* eslint-enable */
