﻿using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Asp.Versioning.Builder;
using Aveva.Platform.Authorization.Sdk;
using Aveva.Platform.Authorization.Sdk.Domain.Models;
using Aveva.Platform.Authorization.Sdk.Domain.Models.Target;
using Aveva.Platform.Catalog.Api.Logging;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Domain.Queries;
using Aveva.Platform.Catalog.ServiceClient.AccountMgmt;
using Aveva.Platform.Common.Framework.Abstractions.Mapping;
using Aveva.Platform.Common.Framework.AspNetCore.Routing;
using Aveva.Platform.Common.Framework.AspNetCore.Swagger;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using ApiModels = Aveva.Platform.Catalog.Domain.DataTransferObjects.Api.v2;
using OpsModels = Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;

namespace Aveva.Platform.Catalog.Api.Routing
{
    /// <summary>
    /// The route registration module for the Catalog API V2.
    /// </summary>
    public class CatalogV2RouteRegistrationModule : IRouteRegistrationModule
    {
        #region Constants
        private const string GetAllAccountScopedCatalogsV2 = nameof(GetAllAccountScopedCatalogsV2);
        private const string GetAllAccountScopedCatalogsOpsV2 = nameof(GetAllAccountScopedCatalogsOpsV2);
        private const string GetAccountScopedCatalogByIdV2 = nameof(GetAccountScopedCatalogByIdV2);
        private const string GetAccountScopedCatalogByIdOpsV2 = nameof(GetAccountScopedCatalogByIdOpsV2);
        #endregion Constants

        #region IRouteRegistrationModule Implementation

        /// <inheritdoc/>
        [ExcludeFromCodeCoverage(Justification = "Not unit testable")]
        public void AddRoutes(IEndpointRouteBuilder app)
        {
            ArgumentNullException.ThrowIfNull(app);

            IVersionedEndpointRouteBuilder catalogData = app.NewVersionedApi("CatalogV2");

            // Primary API.
            // Account scoped API.
            RouteGroupBuilder accountScopedCatalogV2 = catalogData
                .MapGroup("api/account/{accountId}/v{version:apiVersion}/services")
                .WithOpenApi()
                .WithGroupName("api-v2")
                .HasApiVersion(2.0);

            accountScopedCatalogV2.MapGet(string.Empty, GetAccountScopedServicesApi)
                .WithName(GetAllAccountScopedCatalogsV2)
                .WithOpenApi(SwaggerExtensions.BuildExtensionsProperties<ApiModels.ServiceCollectionResponse>)
                .WithSummary("Get catalog services")
                .WithDescription("Gets a collection of catalog service entries for an account. This endpoint returns only services that are made available to your account.");

            accountScopedCatalogV2.MapGet("{id}", GetAccountScopedServiceByIdApi)
                .WithName(GetAccountScopedCatalogByIdV2)
                .WithOpenApi(SwaggerExtensions.BuildExtensionsProperties<ApiModels.ServiceResponse>)
                .WithSummary("Get catalog service by ID")
                .WithDescription("Gets a specific catalog service entry for an account by its unique `id`. This endpoint returns detailed information about a single service only if it is made available to your account.");

            // Ops.
            RouteGroupBuilder catalogOpsV1 = catalogData
                .MapGroup("ops/v{version:apiVersion}/services")
                .WithOpenApi()
                .WithGroupName("operations-v2")
                .HasApiVersion(2.0);

            catalogOpsV1.MapGet(string.Empty, GetAccountScopedServicesOps)
                .WithName(GetAllAccountScopedCatalogsOpsV2)
                .WithOpenApi(SwaggerExtensions.BuildExtensionsProperties<OpsModels.ServiceCollectionResponse>)
                .WithSummary("Get catalog services")
                .WithDescription("Gets a collection of all catalog service entries. This operations endpoint allows viewing all services regardless of their availability to specific accounts.");

            catalogOpsV1.MapGet("{id}", GetAccountScopedServiceByIdOps)
                .WithName(GetAccountScopedCatalogByIdOpsV2)
                .WithOpenApi(SwaggerExtensions.BuildExtensionsProperties<OpsModels.ServiceResponse>)
                .WithSummary("Get catalog service by ID")
                .WithDescription("Gets a specific catalog service entry by its unique `id`. This operations endpoint returns detailed information about a single service, including its availability configuration for different accounts.");
        }

        #endregion IRouteRegistrationModule Implementation

        #region Explicitly Implemented Route Handlers (for simple route implementations)

        #region Account Scoped Api v2

        internal static async Task<Results<Ok<ApiModels.ServiceCollectionResponse>, ForbidHttpResult, NotFound>> GetAccountScopedServicesApi(
            [FromRoute] string accountId,
            [AsParameters] DataTransferObjects.Api.v2.ServiceQueryRequest query,
            ITypeMappingService typeMapper,
            IServiceEntryRepository repository,
            IServiceAvailabilityRepository serviceAvailabilityRepository,
            IAuthorizationProvider authorizationProvider,
            IAccountMgmtClient accountMgmtClient,
            ILogger<CatalogV2RouteRegistrationModule> logger,
            CatalogMetrics metrics,
            CancellationToken cancellationToken)
        {
            ArgumentNullException.ThrowIfNull(query);
            ArgumentException.ThrowIfNullOrWhiteSpace(accountId);
            ArgumentNullException.ThrowIfNull(typeMapper);
            ArgumentNullException.ThrowIfNull(repository);
            ArgumentNullException.ThrowIfNull(authorizationProvider);
            ArgumentNullException.ThrowIfNull(logger);
            ArgumentNullException.ThrowIfNull(serviceAvailabilityRepository);

            using (var activity = CatalogTraceSource.ApiTrace.StartActivity("catalog.authorization", ActivityKind.Client))
            {
                activity?.AddTag("catalog.account_id", accountId);

                var authResult = await authorizationProvider.AuthorizeResourceAsync(
                    ResourcePath.Parse($"services/*"),
                    ResourceAction.Read,
                    AuthorizationTarget.Api(ServiceId.Parse("catalog"), AccountId.Parse(accountId), InstanceId.Parse("default")),
                    new AuthorizationMetadata(),
                    cancellationToken).ConfigureAwait(true);

                if (!authResult.IsSuccess)
                {
                    activity?.SetStatus(ActivityStatusCode.Error, "Unauthorized Access");
                    logger.UnauthorizedAccess();
                    return TypedResults.Forbid();
                }
            }

            var accountAvailabilities = await accountMgmtClient.QueryAvailabilityAsync(accountId, cancellationToken).ConfigureAwait(false);

            if (accountAvailabilities == null)
            {
                return TypedResults.NotFound();
            }

            var serviceEntries = await repository.QueryAsync(typeMapper.Map<DataTransferObjects.Api.v2.ServiceQueryRequest, ServiceEntryQuery>(query)).ConfigureAwait(true);
            var serviceAvailability = serviceAvailabilityRepository.GetForEntries(serviceEntries, accountAvailabilities);

            var outputServiceEntries = serviceAvailability
                .Where(x => x.Value == null || x.Value.Enabled == true)
                .Select(x =>
                {
                    var y = new V1ServiceEntry() { Id = x.Key.Id }.UpdateFrom(x.Key);
                    if (y.Lifecycle.Trigger == V1Trigger.Catalog)
                    {
                        y.Availability = serviceAvailability[x.Key];
                    }

                    return y;
                });

            return TypedResults.Ok(new ApiModels.ServiceCollectionResponse()
            {
                Items = typeMapper.Map<ApiModels.ServiceResponse, V1ServiceEntry>(outputServiceEntries).ToList(),
            });
        }

        internal static async Task<Results<Ok<ApiModels.ServiceResponse>, ForbidHttpResult, NotFound>> GetAccountScopedServiceByIdApi(
            [FromRoute] string id,
            [FromRoute] string accountId,
            ITypeMappingService typeMapper,
            IServiceEntryRepository repository,
            IServiceAvailabilityRepository serviceAvailabilityRepository,
            IAuthorizationProvider authorizationProvider,
            IAccountMgmtClient accountMgmtClient,
            ILogger<CatalogV2RouteRegistrationModule> logger,
            CatalogMetrics metrics,
            CancellationToken cancellationToken)
        {
            ArgumentException.ThrowIfNullOrEmpty(id);
            ArgumentException.ThrowIfNullOrEmpty(accountId);
            ArgumentNullException.ThrowIfNull(typeMapper);
            ArgumentNullException.ThrowIfNull(repository);
            ArgumentNullException.ThrowIfNull(authorizationProvider);
            ArgumentNullException.ThrowIfNull(logger);

            using (var activity = CatalogTraceSource.ApiTrace.StartActivity("catalog.authorization", ActivityKind.Client))
            {
                activity?.AddTag("catalog.service_id", id);
                activity?.AddTag("catalog.account_id", accountId);
                var authResult = await authorizationProvider.AuthorizeResourceAsync(
                    ResourcePath.Parse($"services/{id}"),
                    ResourceAction.Read,
                    AuthorizationTarget.Api(ServiceId.Parse("catalog"), AccountId.Parse(accountId), InstanceId.Parse("default")),
                    new AuthorizationMetadata(),
                    cancellationToken).ConfigureAwait(false);

                if (!authResult.IsSuccess)
                {
                    activity?.SetStatus(ActivityStatusCode.Error, "Unauthorized Access");
                    logger.UnauthorizedAccess();
                    return TypedResults.Forbid();
                }
            }

            var accountAvailabilities = await accountMgmtClient.QueryAvailabilityAsync(accountId, cancellationToken).ConfigureAwait(false);
            if (accountAvailabilities == null)
            {
                return TypedResults.NotFound();
            }

            var serviceEntry = await repository.GetByIdAsync(id).ConfigureAwait(false);
            if (serviceEntry == null)
            {
                return TypedResults.NotFound();
            }

            var serviceAvailability = serviceAvailabilityRepository.GetForEntry(serviceEntry, accountAvailabilities);
            if (serviceAvailability != null && serviceAvailability.Enabled == false)
            {
                return TypedResults.NotFound();
            }

            var outputServiceEntry = new V1ServiceEntry() { Id = serviceEntry.Id }.UpdateFrom(serviceEntry);
            if (outputServiceEntry.Lifecycle.Trigger == V1Trigger.Catalog)
            {
                outputServiceEntry.Availability = serviceAvailability;
            }

            return TypedResults.Ok(typeMapper.Map<ApiModels.ServiceResponse, V1ServiceEntry>(outputServiceEntry));
        }

        #endregion

        #region Account Scoped Ops v2

        internal static async Task<Results<Ok<OpsModels.ServiceCollectionResponse>, ForbidHttpResult, NotFound>> GetAccountScopedServicesOps(
            [AsParameters] DataTransferObjects.Ops.v2.ServiceQueryRequest query,
            ITypeMappingService typeMapper,
            IServiceEntryRepository repository,
            IServiceAvailabilityRepository serviceAvailabilityRepository,
            IAuthorizationProvider authorizationProvider,
            IAccountMgmtClient accountMgmtClient,
            ILogger<CatalogV2RouteRegistrationModule> logger,
            CatalogMetrics metrics,
            CancellationToken cancellationToken)
        {
            ArgumentNullException.ThrowIfNull(query);
            ArgumentNullException.ThrowIfNull(typeMapper);
            ArgumentNullException.ThrowIfNull(repository);
            ArgumentNullException.ThrowIfNull(authorizationProvider);
            ArgumentNullException.ThrowIfNull(logger);

            using (var activity = CatalogTraceSource.ApiTrace.StartActivity("catalog.authorization", ActivityKind.Client))
            {
                activity?.AddTag("catalog.account_id", query.AccountId);
                var authResult = await authorizationProvider.AuthorizeResourceAsync(
                    ResourcePath.Parse($"services/*"),
                    ResourceAction.Read,
                    AuthorizationTarget.Ops(ServiceId.Parse("catalog")),
                    new AuthorizationMetadata(),
                    cancellationToken).ConfigureAwait(true);

                if (!authResult.IsSuccess)
                {
                    activity?.SetStatus(ActivityStatusCode.Error, "Unauthorized Access");
                    logger.UnauthorizedAccess();
                    return TypedResults.Forbid();
                }
            }

            var accountAvailabilities = new Dictionary<string, V1ServiceAvailability>();
            if (!string.IsNullOrEmpty(query.AccountId))
            {
                accountAvailabilities = await accountMgmtClient.QueryAvailabilityAsync(query.AccountId, cancellationToken).ConfigureAwait(false);
            }

            var serviceEntries = await repository.QueryAsync(typeMapper.Map<DataTransferObjects.Ops.v2.ServiceQueryRequest, ServiceEntryQuery>(query)).ConfigureAwait(true);
            var serviceAvailability = serviceAvailabilityRepository.GetForEntries(serviceEntries.ToList(), accountAvailabilities ?? new Dictionary<string, V1ServiceAvailability>());

            var outputServiceEntries = serviceAvailability
                .Select(x =>
                {
                    var y = new V1ServiceEntry() { Id = x.Key.Id }.UpdateFrom(x.Key);
                    if (y.Lifecycle.Trigger == V1Trigger.Catalog)
                    {
                        y.Availability = serviceAvailability[x.Key];
                    }

                    return y;
                });

            return TypedResults.Ok(new OpsModels.ServiceCollectionResponse()
            {
                Items = typeMapper.Map<OpsModels.ServiceResponse, V1ServiceEntry>(outputServiceEntries).ToList(),
            });
        }

        internal static async Task<Results<Ok<OpsModels.ServiceResponse>, ForbidHttpResult, NotFound>> GetAccountScopedServiceByIdOps(
            [FromRoute] string id,
            [FromQuery] string? accountId,
            ITypeMappingService typeMapper,
            IServiceEntryRepository repository,
            IServiceAvailabilityRepository serviceAvailabilityRepository,
            IAuthorizationProvider authorizationProvider,
            IAccountMgmtClient accountMgmtClient,
            ILogger<CatalogV2RouteRegistrationModule> logger,
            CatalogMetrics metrics,
            CancellationToken cancellationToken)
        {
            ArgumentException.ThrowIfNullOrEmpty(id);
            ArgumentNullException.ThrowIfNull(typeMapper);
            ArgumentNullException.ThrowIfNull(repository);
            ArgumentNullException.ThrowIfNull(authorizationProvider);
            ArgumentNullException.ThrowIfNull(logger);

            using (var activity = CatalogTraceSource.ApiTrace.StartActivity("catalog.authorization", ActivityKind.Client))
            {
                activity?.AddTag("catalog.service_id", id);
                activity?.AddTag("catalog.account_id", accountId);

                var authResult = await authorizationProvider.AuthorizeResourceAsync(
                    ResourcePath.Parse($"services/{id}"),
                    ResourceAction.Read,
                    AuthorizationTarget.Ops(ServiceId.Parse("catalog")),
                    new AuthorizationMetadata(),
                    cancellationToken).ConfigureAwait(false);

                if (!authResult.IsSuccess)
                {
                    activity?.SetStatus(ActivityStatusCode.Error, "Unauthorized Access");
                    logger.UnauthorizedAccess();
                    return TypedResults.Forbid();
                }
            }

            var accountAvailabilities = new Dictionary<string, V1ServiceAvailability>();
            if (!string.IsNullOrEmpty(accountId))
            {
                accountAvailabilities = await accountMgmtClient.QueryAvailabilityAsync(accountId, cancellationToken).ConfigureAwait(false);
            }

            var serviceEntry = await repository.GetByIdAsync(id).ConfigureAwait(false);
            if (serviceEntry == null)
            {
                return TypedResults.NotFound();
            }

            var serviceAvailability = serviceAvailabilityRepository.GetForEntry(serviceEntry, accountAvailabilities ?? new Dictionary<string, V1ServiceAvailability>());
            var outputServiceEntry = new V1ServiceEntry() { Id = serviceEntry.Id }.UpdateFrom(serviceEntry);
            if (outputServiceEntry.Lifecycle.Trigger == V1Trigger.Catalog)
            {
                outputServiceEntry.Availability = serviceAvailability;
            }

            return TypedResults.Ok(typeMapper.Map<OpsModels.ServiceResponse, V1ServiceEntry>(outputServiceEntry));
        }

        #endregion

        #endregion Explicitly Implemented Route Handlers (for simple route implementations)
    }
}