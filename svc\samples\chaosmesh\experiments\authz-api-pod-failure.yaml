apiVersion: chaos-mesh.org/v1alpha1
kind: PodChaos
metadata:
  name: authz-api-pod-failure-experiment
  namespace: chaos-mesh
spec:
  action: pod-failure                # Action to simulate pod failure
  mode: all           # Apply to all matching pods
  selector:
    namespaces:
      - platform-authorization             # Target namespace
    labelSelectors:
      pod-selector: authorization-api  # Label to identify specific pods in deployment
  duration: "2m"                     # Total duration of the experiment (1 minute)

# Key parameters for the PodChaos experiment:
# - action: Specifies the type of pod disruption.
# - mode: Determines whether to target all pods or a specific set.
# - selector: Filters the pods based on namespace and labels.
# - duration: The total time the pod failure experiment will run.
