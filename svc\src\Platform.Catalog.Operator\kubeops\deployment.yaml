apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    operator-deployment: kubernetes-operator
  name: operator
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      operator-deployment: kubernetes-operator
  template:
    metadata:
      labels:
        operator-deployment: kubernetes-operator
    spec:
      containers:
      - env:
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        envFrom:
        - configMapRef:
            name: webhook-config
        image: operator
        name: operator
        ports:
        - containerPort: 5001
          name: https
        resources:
          limits:
            cpu: 100m
            memory: 128Mi
          requests:
            cpu: 100m
            memory: 64Mi
        volumeMounts:
        - mountPath: /certs
          name: certificates
          readOnly: true
        - mountPath: /ca
          name: ca-certificates
          readOnly: true
      terminationGracePeriodSeconds: 10
      volumes:
      - name: certificates
        secret:
          secretName: webhook-cert
      - name: ca-certificates
        secret:
          secretName: webhook-ca