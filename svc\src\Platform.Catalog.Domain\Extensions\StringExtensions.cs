﻿namespace Aveva.Platform.Catalog.Domain.Extensions
{
    /// <summary>
    /// Extension class for all string operations.
    /// </summary>
    public static class StringExtensions
    {
        /// <summary>
        /// Gets the substring of a string up to maxLenth.
        /// </summary>
        /// <param name="input">string.</param>
        /// <param name="maxLength">Lenth to truncate the string.</param>
        /// <returns>Substring up to maxLenth or the original string if lesser.</returns>
        public static string Shorten(this string input, int maxLength)
        {
            if (string.IsNullOrWhiteSpace(input))
            {
                return input;
            }

            if (input.Length > maxLength)
            {
                return input[..maxLength];
            }

            return input;
        }
    }
}