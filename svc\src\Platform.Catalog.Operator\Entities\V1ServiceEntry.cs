﻿using Aveva.Platform.Catalog.Domain;
using k8s.Models;
using KubeOps.Abstractions.Entities;
using Models = Aveva.Platform.Catalog.Domain.Models;

namespace Aveva.Platform.Catalog.Operator.Entities
{
    /// <summary>
    /// ServiceEntry entity class for custom controller and webhook.
    /// </summary>
    [KubernetesEntity(Group = CatalogConstants.Group, ApiVersion = CatalogConstants.V1, Kind = CatalogConstants.ServiceEntry.Kind, PluralName = CatalogConstants.ServiceEntry.Plural)]
    public class V1ServiceEntry : CustomKubernetesEntity<Models.V1ServiceEntry>
    {
    }
}