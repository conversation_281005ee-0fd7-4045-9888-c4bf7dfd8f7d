<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.Operator.Tests.Unit</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.Operator.Tests.Unit</RootNamespace>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
    <OutputType>exe</OutputType>
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="TestResults\**" />
    <EmbeddedResource Remove="TestResults\**" />
    <None Remove="TestResults\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="OperatorBuilder.g.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aveva.Platform.Common.Testing" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Shouldly" />
    <PackageReference Include="xunit.v3" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\Platform.Catalog.Operator\Platform.Catalog.Operator.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <Target Name="DisableAnalyzers" BeforeTargets="CoreCompile">
    <ItemGroup>
      <Analyzer Remove="@(Analyzer)" Condition="'%(Filename)' == 'KubeOps.Generator'" />
    </ItemGroup>
  </Target>
</Project>