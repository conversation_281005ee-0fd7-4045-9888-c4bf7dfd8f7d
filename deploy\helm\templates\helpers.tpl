{{/* The service account name */}}
{{- define "operator.serviceaccount" -}}
{{ .Release.Name }}-operator-serviceaccount
{{- end }}

{{/* Catalog Operator */}}
{{/* The name of the operator */}}
{{- define "operator.name" -}}
{{- if .Values.operator.name -}}
{{ .Values.operator.name }}
{{- else -}}
{{ .Release.Name }}-operator
{{- end -}}
{{- end }}

{{/* The operator resources */}}
{{- define "operator.resources" -}}
{{- toYaml .Values.operator.resources | nindent 10 }}
{{- end }}

{{/* The image name for the operator */}}
{{- define "operator.image" -}}
{{- if .Values.environment.containerRegistryServer -}}
{{ .Values.environment.containerRegistryServer }}/{{ .Values.operator.image.name }}:{{ .Values.buildVariables.tag }}
{{- else -}}
{{ .Values.operator.image.name }}:{{ .Values.operator.image.tag }}
{{- end -}}
{{- end }}

{{/* The chart name for the operator. */}}
{{- define "operator.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/* The name for the self signed issuer for the operator. */}}
{{- define "operator.selfSignedIssuer" -}}
{{ printf "%s-selfsign" (include "operator.name" .) }}
{{- end -}}

{{/* The name of the root CA issuer for the operator. */}}
{{- define "operator.rootCAIssuer" -}}
{{ printf "%s-ca" (include "operator.name" .) }}
{{- end -}}

{{/* The name used for the secret for the root CA certificate. */}}
{{- define "operator.rootCACertificate" -}}
{{ printf "%s-ca" (include "operator.name" .) }}
{{- end -}}

{{/* The name used for the secret for the serving certificate for the operator app. */}}
{{- define "operator.servingCertificate" -}}
{{ printf "%s-webhook-tls" (include "operator.name" .) }}
{{- end -}}

{{/* Standard core or performance environment */}}
{{- define "helpers.isStandardCoreOrPerformanceEnv" -}}
{{- if or (eq .Values.environment.isStandardCoreEnvironment "true") (and (eq .Values.deploymentMode "pipeline") (or ( eq .Values.environment.environment "perfdev") ( eq .Values.environment.environment "schperf")) ) -}}
true
{{- else -}}
false
{{- end -}}
{{- end }}

{{/* Production environment type */}}
{{- define "helpers.isProductionEnvType" -}}
{{- if and (eq .Values.deploymentMode "pipeline") (or (eq .Values.environment.environment "perfdev") ( eq .Values.environment.environment "schperf") (eq .Values.environment.environment "eucliddev") (eq .Values.environment.environmentType "production")) -}}
true
{{- else -}}
false
{{- end -}}
{{- end }}

{{/* Development environment type */}}
{{- define "helpers.isDevelopmentEnvType" -}}
{{- if and (eq .Values.deploymentMode "pipeline") (eq .Values.environment.environmentType "development") -}}
true
{{- else -}}
false
{{- end -}}
{{- end }}

{{/* Connect Environment level */}}
{{- define "helpers.environmentLevel" -}}
{{ trim .Values.environment.environmentLevel }}
{{- end -}}

{{/* Catalog Teams Channel Action Group Name */}}
{{- define "helpers.teamsChannelActionGroupName" -}}
{{ printf "ag-platform-catalog-%s-teams-channel" ( include "helpers.environmentLevel" .) }}
{{- end -}}

{{/* Action Group Teams channel Webhook Url */}}
{{- define "helpers.teamWebhookUrl" -}}
{{- if and (eq .Values.environment.environmentLevel "prod") (eq .Values.environment.isStandardCoreEnvironment "true") -}}
'https://prod-29.westeurope.logic.azure.com:443/workflows/372cbf7c02a741fc91e1b603ad51ebee/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=Ftt4jPGZUuFpUbzLPYr9HPT1RCwAbvJ1eUO9b4neLt8'
{{- else if and (eq .Values.environment.environmentLevel "preprod") (eq .Values.environment.isStandardCoreEnvironment "true") -}}
'https://prod-206.westeurope.logic.azure.com:443/workflows/1b44909d3fff4f22b5f263ddfbe2953b/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=L7jkvYe1hdE0I601QSLg-ZLlxpfPP9haBC2sgWjTs2o'
{{- else if and (eq .Values.environment.environmentLevel "dev") (eq .Values.environment.isStandardCoreEnvironment "true") -}}
'https://prod-252.westeurope.logic.azure.com:443/workflows/927c60465ba84f08a8cbe18c3c210088/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=1nW1qa8aO2J53mOm9Ei7kxkGAFlXlEOI4HL-s8sXmBg'
{{- end -}}
{{- end }}

{{/* Include Action Group Teams channel only if it is core standard environment */}}
{{- define "helpers.actionGroupReference" -}}
{{- if eq .Values.environment.isStandardCoreEnvironment "true" }}
actions:
- actionGroupReference: "{{ include "helpers.teamsChannelActionGroupName" $ }}"
{{- end }}  
{{- end }}