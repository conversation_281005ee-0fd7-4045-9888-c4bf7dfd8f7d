﻿using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using Aveva.Platform.Authentication.Ops.Handler;
using Shouldly;
using AccountMgmtApi = Aveva.Platform.AccountMgmt.Client.Api;
using AccountMgmtOps = Aveva.Platform.AccountMgmt.Client.Ops;
using CatalogApiV2 = Aveva.Platform.Catalog.Client.V2.Api;
using CatalogOpsV2 = Aveva.Platform.Catalog.Client.V2.Ops;

namespace Aveva.Platform.Catalog.Tests.Deployment
{
    public class CatalogOperationsV2Tests : IClassFixture<CatalogTestFixture>
    {
        private const int Timeout = 300000; // 5 minutes
        private const string CatalogServiceEntryId = "catalog";
        private readonly CatalogTestFixture _fixture;
        private readonly ITestOutputHelper _output;

        public CatalogOperationsV2Tests(CatalogTestFixture fixture, ITestOutputHelper testOutput)
        {
            _fixture = fixture;
            _output = testOutput;
            _fixture.TestOutputHelper = testOutput;
        }

        [Theory(Timeout = Timeout)]
        [Trait("Category", "Functional")]
        [InlineData(AuthenticationOpsHandler.AdminRole)]
        [InlineData(AuthenticationOpsHandler.OperatorRole)]
        [InlineData(AuthenticationOpsHandler.SupportRole)]
        [InlineData(AuthenticationOpsHandler.NoRoleRole)]
        [InlineData(AuthenticationOpsHandler.SecretAdminRole)]
        [SuppressMessage("StyleCop.CSharp.SpacingRules", "SA1010:Opening square brackets should be spaced correctly", Justification = "Contradicts with other whitespace rules")]
        public async Task CatalogServiceV2_GetServicesOps_AllRegions(string role)
        {
            var opsCatalogClient = _fixture.ClientBuilder.CreateClientOps<CatalogOpsV2.Client>(role);
            var opsAccountClient = _fixture.ClientBuilder.CreateClientOps<AccountMgmtOps.Client>(AuthenticationOpsHandler.AdminRole);
            var geographies = await opsAccountClient!.Ops.AccountMgmt.V1.Geographies.GetAsync(cancellationToken: TestContext.Current.CancellationToken);
            geographies.ShouldNotBeNull();

            var responseExpected = new CatalogOpsV2.Models.ServiceResponse()
            {
                Id = CatalogServiceEntryId,
                DisplayName = "Catalog services",
                HostingType = "Environment",
                IconUrl = "https://cdn.aveva.design/icons/svg/outlined/32/content/content--library.svg",
                Description = "Catalog services",
                Lifecycle = new CatalogOpsV2.Models.Lifecycle
                {
                    Trigger = "Account",
                    InstanceMode = "Shared",
                },
                Tags = [],
            };
            var numberOfServicesInPreviousRegion = 0;

            foreach (var geography in geographies)
            {
                geography.Regions.ShouldNotBeNull();
                geography.Id.ShouldNotBeNull();
                foreach (var region in geography.Regions)
                {
                    // Test the services collection API
                    _output.WriteLine($"Checking service entries in geo:{geography.Id} region:{region.Id}");
                    region.Id.ShouldNotBeNull();
                    var servicesInRegion = await opsCatalogClient!.Ops.Catalog.V2.Services.GetAsync(
                        x =>
                        {
                            x.Headers.Add("connect-geography-id", geography.Id);
                            x.Headers.Add("connect-region-id", region.Id);
                        },
                        cancellationToken: TestContext.Current.CancellationToken);

                    servicesInRegion.ShouldNotBeNull();
                    servicesInRegion.Items.ShouldNotBeNull();
                    if (numberOfServicesInPreviousRegion > 0)
                    {
                        servicesInRegion.Items.Count.ShouldBe(numberOfServicesInPreviousRegion, "Number of service entries in different regions differ");
                        numberOfServicesInPreviousRegion = servicesInRegion.Items.Count;
                    }

                    servicesInRegion.Items.ExceptBy(servicesInRegion.Items.Select(x => x.Id), x => x.Id).Any().ShouldBeFalse();

                    var catalogMatch = servicesInRegion.Items.FirstOrDefault(s => s.Id!.ToLower(CultureInfo.CurrentCulture).Equals(CatalogServiceEntryId));
                    catalogMatch.ShouldNotBeNull();
                    catalogMatch.Id.ShouldBe(responseExpected.Id, StringCompareShould.IgnoreCase);
                    catalogMatch.DisplayName.ShouldBe(responseExpected.DisplayName, StringCompareShould.IgnoreCase);
                    catalogMatch.HostingType.ShouldBe(responseExpected.HostingType, StringCompareShould.IgnoreCase);
                    catalogMatch.Category.ShouldBe(responseExpected.Category);
                    catalogMatch.Lifecycle!.Trigger.ShouldBe(responseExpected.Lifecycle.Trigger, StringCompareShould.IgnoreCase);
                    catalogMatch.Lifecycle!.InstanceMode.ShouldBe(responseExpected.Lifecycle.InstanceMode, StringCompareShould.IgnoreCase);

                    // Test the single serivce API
                    var catalogServiceEntryInRegion = await opsCatalogClient!.Ops.Catalog.V2.Services[CatalogServiceEntryId].GetAsync(
                        x =>
                        {
                            x.Headers.Add("connect-geography-id", geography.Id);
                            x.Headers.Add("connect-region-id", region.Id);
                        },
                        cancellationToken: TestContext.Current.CancellationToken);

                    catalogServiceEntryInRegion.ShouldNotBeNull();
                    catalogServiceEntryInRegion.Id.ShouldBe(responseExpected.Id, StringCompareShould.IgnoreCase);
                    catalogServiceEntryInRegion.DisplayName.ShouldBe(responseExpected.DisplayName, StringCompareShould.IgnoreCase);
                    catalogServiceEntryInRegion.HostingType.ShouldBe(responseExpected.HostingType, StringCompareShould.IgnoreCase);
                    catalogServiceEntryInRegion.Category.ShouldBe(responseExpected.Category);
                    catalogServiceEntryInRegion.Lifecycle!.Trigger.ShouldBe(responseExpected.Lifecycle.Trigger, StringCompareShould.IgnoreCase);
                    catalogServiceEntryInRegion.Lifecycle!.InstanceMode.ShouldBe(responseExpected.Lifecycle.InstanceMode, StringCompareShould.IgnoreCase);
                }
            }
        }

        [Fact(Timeout = Timeout)]
        [Trait("Category", "Functional")]
        [SuppressMessage("StyleCop.CSharp.SpacingRules", "SA1010:Opening square brackets should be spaced correctly", Justification = "Contradicts with other whitespace rules")]
        public async Task CatalogServiceV2_GetServicesApi()
        {
            var accountBuilder = _fixture.GetAccountBuilderInstance();
            var account = await accountBuilder.BuildAsync().ConfigureAwait(true);
            account.ShouldNotBeNull();
            account.Id.ShouldNotBeNull();

            var apiCatalogClient = await _fixture.ClientBuilder.CreateClientApi<CatalogApiV2.Client>(account.Id);

            // Test the collection response
            var services = await apiCatalogClient!.Api.Account[account.Id].Catalog.V2.Services.GetAsync(cancellationToken: TestContext.Current.CancellationToken);

            var responseExpected = new CatalogOpsV2.Models.ServiceResponse()
            {
                Id = CatalogServiceEntryId,
                DisplayName = "Catalog services",
                HostingType = "Environment",
                IconUrl = "https://cdn.aveva.design/icons/svg/outlined/32/content/content--library.svg",
                Description = "Catalog services",
                Lifecycle = new CatalogOpsV2.Models.Lifecycle
                {
                    Trigger = "Account",
                    InstanceMode = "Shared",
                },
                Tags = [],
            };

            services.ShouldNotBeNull();
            services.Items.ShouldNotBeNull();
            services.Items.ExceptBy(services.Items.Select(x => x.Id), x => x.Id).Any().ShouldBeFalse();

            var catalogMatch = services.Items.FirstOrDefault(s => s.Id!.ToLower(CultureInfo.CurrentCulture).Equals(CatalogServiceEntryId));
            catalogMatch.ShouldNotBeNull();
            catalogMatch.Id.ShouldNotBeNull();
            catalogMatch.Id.ToLower(CultureInfo.CurrentCulture).ShouldBe(responseExpected.Id.ToLower(CultureInfo.CurrentCulture));
            catalogMatch.DisplayName!.ToLower(CultureInfo.CurrentCulture).ShouldBe(responseExpected.DisplayName.ToLower(CultureInfo.CurrentCulture));
            catalogMatch.HostingType!.ToLower(CultureInfo.CurrentCulture).ShouldBe(responseExpected.HostingType.ToLower(CultureInfo.CurrentCulture));
            catalogMatch.Lifecycle!.Trigger!.ToLower(CultureInfo.CurrentCulture).ShouldBe(responseExpected.Lifecycle.Trigger.ToLower(CultureInfo.CurrentCulture));
            catalogMatch.Lifecycle!.InstanceMode!.ToLower(CultureInfo.CurrentCulture).ShouldBe(responseExpected.Lifecycle.InstanceMode.ToLower(CultureInfo.CurrentCulture));

            // Test the single response
            var catalogServiceEntry = await apiCatalogClient!.Api.Account[account.Id].Catalog.V2.Services[CatalogServiceEntryId].GetAsync(cancellationToken: TestContext.Current.CancellationToken);
            catalogServiceEntry.ShouldNotBeNull();
            catalogServiceEntry.Id.ShouldNotBeNull();
            catalogServiceEntry.Id.ToLower(CultureInfo.CurrentCulture).ShouldBe(responseExpected.Id.ToLower(CultureInfo.CurrentCulture));
            catalogServiceEntry.DisplayName!.ToLower(CultureInfo.CurrentCulture).ShouldBe(responseExpected.DisplayName.ToLower(CultureInfo.CurrentCulture));
            catalogServiceEntry.HostingType!.ToLower(CultureInfo.CurrentCulture).ShouldBe(responseExpected.HostingType.ToLower(CultureInfo.CurrentCulture));
            catalogServiceEntry.Lifecycle!.Trigger!.ToLower(CultureInfo.CurrentCulture).ShouldBe(responseExpected.Lifecycle.Trigger.ToLower(CultureInfo.CurrentCulture));
            catalogServiceEntry.Lifecycle!.InstanceMode!.ToLower(CultureInfo.CurrentCulture).ShouldBe(responseExpected.Lifecycle.InstanceMode.ToLower(CultureInfo.CurrentCulture));
        }

        [Fact(Timeout = Timeout)]
        [Trait("Category", "Functional")]
        [SuppressMessage("StyleCop.CSharp.SpacingRules", "SA1010:Opening square brackets should be spaced correctly", Justification = "Contradicts with other whitespace rules")]
        public async Task CatalogServiceV2_GetServicesApi_AllGeographies()
        {
            var accountBuilder = _fixture.GetAccountBuilderInstance();
            var account = await accountBuilder.BuildAsync().ConfigureAwait(true);
            account.ShouldNotBeNull();
            account.Id.ShouldNotBeNull();

            // Set the account to be available in all geographies
            var listOfAvailableGeographies = new List<string>();
            listOfAvailableGeographies.AddRange(_fixture.EnvironmentGeographies.Select(x => x.Id!));

            var updateGeographiesForAccount = await account.UpdateGeographiesAsync(listOfAvailableGeographies).ConfigureAwait(true);
            updateGeographiesForAccount.ShouldNotBeNull();

            var apiAccountClient = await _fixture.ClientBuilder.CreateClientApi<AccountMgmtApi.Client>(account.Id);
            var getUpdatedGeoForAccount = await apiAccountClient!.Api.Account[account.Id].AccountMgmt.V1.Geographies.GetAsync(cancellationToken: TestContext.Current.CancellationToken).ConfigureAwait(true);
            getUpdatedGeoForAccount.ShouldNotBeNull();

            listOfAvailableGeographies.Sort();
            getUpdatedGeoForAccount.Sort();
            getUpdatedGeoForAccount.ShouldBe(listOfAvailableGeographies);

            // Test the service entries in all geographies
            var apiCatalogClient = await _fixture.ClientBuilder.CreateClientApi<CatalogApiV2.Client>(account.Id);
            var geographies = await apiAccountClient!.Api.AccountMgmt.V1.Geographies.GetAsync(cancellationToken: TestContext.Current.CancellationToken);

            geographies.ShouldNotBeNull();

            var responseExpected = new CatalogApiV2.Models.ServiceResponse()
            {
                Id = CatalogServiceEntryId,
                DisplayName = "Catalog services",
                HostingType = "Environment",
                IconUrl = "https://cdn.aveva.design/icons/svg/outlined/32/content/content--library.svg",
                Description = "Catalog services",
                Lifecycle = new CatalogApiV2.Models.Lifecycle
                {
                    Trigger = "Account",
                    InstanceMode = "Shared",
                },
                Tags = [],
            };

            foreach (var geography in geographies)
            {
                _output.WriteLine($"Checking service entries in geo:{geography.Id}");
                geography.Id.ShouldNotBeNull();
                var servicesForGeo = await apiCatalogClient!.Api.Account[account.Id].Catalog.V2.Services.GetAsync(
                x =>
                {
                    x.Headers.Add("connect-geography-id", geography.Id);
                },
                cancellationToken: TestContext.Current.CancellationToken);

                servicesForGeo.ShouldNotBeNull();
                servicesForGeo.Items.ShouldNotBeNull();

                var catalogMatch = servicesForGeo.Items.FirstOrDefault(s => s.Id!.ToLower(CultureInfo.CurrentCulture).Equals(CatalogServiceEntryId));
                catalogMatch.ShouldNotBeNull();
                catalogMatch.Id.ShouldNotBeNull();
                catalogMatch.Id.ShouldBe(responseExpected.Id, StringCompareShould.IgnoreCase);
                catalogMatch.DisplayName.ShouldBe(responseExpected.DisplayName, StringCompareShould.IgnoreCase);
                catalogMatch.HostingType.ShouldBe(responseExpected.HostingType, StringCompareShould.IgnoreCase);
                catalogMatch.Lifecycle!.Trigger.ShouldBe(responseExpected.Lifecycle.Trigger, StringCompareShould.IgnoreCase);
                catalogMatch.Lifecycle!.InstanceMode.ShouldBe(responseExpected.Lifecycle.InstanceMode, StringCompareShould.IgnoreCase);
            }
        }
    }
}