﻿using System.Diagnostics.Metrics;

namespace Aveva.Platform.Catalog.Domain.Instrumentation
{
    /// <summary>
    /// A class to define all the meters for tracking instrumentation.
    /// </summary>
    public class CatalogMetrics
    {
        /// <summary>
        /// Catalog Api metrics meter name.
        /// </summary>
        public const string MeterName = "Aveva.Platform.Catalog";
        private const string ServiceEntryCountName = "Catalog.ServiceEntries.Count";
        private const string K8sWatcherOn = "Catalog.k8sWatcher.Status";
        private static readonly Meter _meter = new Meter(MeterName, "1.0.0");
        private int _serviceEntryCount;
        private int _watcherOn;

        /// <summary>
        /// Initializes a new instance of the <see cref="CatalogMetrics"/> class.
        /// </summary>
        public CatalogMetrics()
        {
            _meter.CreateObservableGauge(ServiceEntryCountName, () => _serviceEntryCount, "Count", "Number of Catalog Service Entires");
            _meter.CreateObservableGauge(K8sWatcherOn, () => _watcherOn, "On_Off", "Watcher Status");
        }

        /// <summary>
        /// Records the number of catalog service entries to the monitoring dashboard.
        /// </summary>
        /// <param name="count">Number of Catalog Service Entries.</param>
        public void RecordServiceEntryCount(int count) => _serviceEntryCount = count;

        /// <summary>
        /// Records the status of K8s Watcher.
        /// </summary>
        /// <param name="status">Watcher On(1)/ Off(0).</param>
        public void RecordWatcherStatus(int status) => _watcherOn = status;
    }
}