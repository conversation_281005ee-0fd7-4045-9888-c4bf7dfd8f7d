﻿using Aveva.Platform.Catalog.Domain.Models;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Models;

/// <summary>
/// <see cref="V1ServiceEntry"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Unit")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Unit")]
public static class V1ServiceEntryTests
{
    #region Test Cases

    [Fact]
    public static void V1ServiceEntryTests_Initializes_To_ProvidedValues()
    {
        // Arrange
        string name = "name";
        string id = "2";
        V1ServiceEntry actual = new V1ServiceEntry()
        {
            Id = id,
            DisplayName = name,
            HostingType = V1HostingType.External,
        };

        // Assert
        actual.ShouldSatisfyAllConditions(
            () => actual.DisplayName.ShouldBe(name),
            () => actual.Id.ShouldBe(id),
            () => actual.HostingType.ShouldBe(V1HostingType.External),
            () => actual.Category.ShouldBeNull());
    }

    [Fact]
    public static void V1ServiceEntryTests_With_Category_Initializes_To_ProvidedValues()
    {
        // Arrange
        string name = "name";
        string id = "2";
        V1ServiceEntry actual = new V1ServiceEntry()
        {
            Id = id,
            DisplayName = name,
            HostingType = V1HostingType.External,
            Category = V1Category.Data,
        };

        // Assert
        actual.ShouldSatisfyAllConditions(
            () => actual.DisplayName.ShouldBe(name),
            () => actual.Id.ShouldBe(id),
            () => actual.HostingType.ShouldBe(V1HostingType.External),
            () => actual.Category.ShouldBe(V1Category.Data));
    }

    [Fact]
    public static void V1ServiceEntryTests_Rename_RenamesTask()
    {
        // Arrange
        const string ExpectedName = "New name";
        V1ServiceEntry subject = new V1ServiceEntry()
        {
           Id = "2",
           DisplayName = "existingName",
           HostingType = V1HostingType.External,
        };

        // Act
        subject.Rename(ExpectedName);

        // Assert
        subject.DisplayName.ShouldBe(ExpectedName);
    }

    [Fact]
    public static void V1ServiceEntryTests_UpdateFrom_Null_Throws()
    {
        // Arrange
        V1ServiceEntry? from = null;
        V1ServiceEntry subject = new V1ServiceEntry();

        // Act & Assert
        Should.Throw<ArgumentNullException>(() => subject.UpdateFrom(from!))
            .ParamName.ShouldBe(nameof(from));
    }

    [Fact]
    public static void V1ServiceEntryTests_UpdateFrom_Succeeds()
    {
        // Arrange
        string name = "newName";
        string id = "2";
        Uri iconUrl = new Uri("https://www.example.com");
        V1ServiceEntry from = new V1ServiceEntry()
        {
            Id = id,
            DisplayName = name,
            IconUrl = iconUrl,
            HostingType = V1HostingType.External,
        };
        V1ServiceEntry subject = new V1ServiceEntry();

        // Act
        subject.UpdateFrom(from);

        // Assert
        subject.ShouldSatisfyAllConditions(
            () => subject.DisplayName.ShouldBe(name),
            () => subject.IconUrl.ShouldBe(iconUrl),
            () => subject.HostingType.ShouldBe(V1HostingType.External),
            () => subject.Category.ShouldBeNull());
    }

    [Fact]
    public static void V1ServiceEntryTests_GetHashCode_Succeeds()
    {
        // Arrange
        string name = "newName";
        string id = "2";
        V1ServiceEntry subject = new V1ServiceEntry()
        {
            Id = id,
            DisplayName = name,
            HostingType = V1HostingType.External,
        };

        // Act
        var result = subject.GetHashCode();

        // Assert
        result.ShouldBeOfType<int>();
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_ReturnsTrue()
    {
        // Arrange
        string name = "newName";
        string id = "2";
        string description = "some description";
        Uri iconUrl = new Uri("https://www.example.com");
        V1ServiceEntry subject = new V1ServiceEntry()
        {
            Id = id,
            DisplayName = name,
            HostingType = V1HostingType.External,
            IconUrl = iconUrl,
            Description = description,
        };
        V1ServiceEntry subject2 = new V1ServiceEntry()
        {
            Id = id,
            DisplayName = name,
            HostingType = V1HostingType.External,
            IconUrl = iconUrl,
            Description = description,
        };

        // Act
        var result = subject.Equals(subject2);

        // Assert
        result.ShouldBe(true);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithMatchingDependencies_ReturnsTrue()
    {
        // Arrange
        var firstDependencies = new Dictionary<string, V1CatalogDataDependency>()
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency()
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                }
            },
        };
        V1ServiceEntry first = new V1ServiceEntry()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Dependencies = firstDependencies,
        };
        V1ServiceEntry second = new V1ServiceEntry()
        {
            Id = "CATALOG",
            DisplayName = "NAME",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://WWW.EXAMPLE.COM"),
            Description = "SOME DESCRIPTION",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "PROVIDERID",
                Trigger = V1Trigger.None,
            },
            Dependencies = firstDependencies,
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(true);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithMatchingDependenciesConfig_ReturnsTrue()
    {
        // Arrange
        var firstDependencies = new Dictionary<string, V1CatalogDataDependency>
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                    Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                    {
                        { "config1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Required = true, Min = 10, Max = 100 } },
                        { "config2", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Required = false, Min = 20, Max = 200 } },
                    },
                }
            },
        };
        var first = new V1ServiceEntry
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Dependencies = firstDependencies,
        };
        var second = new V1ServiceEntry
        {
            Id = "CATALOG",
            DisplayName = "NAME",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://WWW.EXAMPLE.COM"),
            Description = "SOME DESCRIPTION",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "PROVIDERID",
                Trigger = V1Trigger.None,
            },
            Dependencies = firstDependencies,
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(true);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithMatchingApplications_ReturnsTrue()
    {
        // Arrange
        V1ServiceEntry first = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Availability = new V1ServiceAvailability()
            {
                Enabled = true,
                Limit = 10,
            },
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Applications =
            [
                new()
                {
                    Name = "app1",
                    Urls = new Dictionary<string, string>
                    {
                        { "url1", "http://test.url" },
                    },
                },
            ],
        };
        V1ServiceEntry second = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Availability = new V1ServiceAvailability()
            {
                Enabled = true,
                Limit = 10,
            },
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Applications =
            [
                new()
                {
                    Name = "app1",
                    Urls = new Dictionary<string, string>
                    {
                        { "url1", "http://test.url" },
                    },
                },
            ],
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(true);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithMatchingGeographies_ReturnsTrue()
    {
        // Arrange
        V1ServiceEntry first = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Availability = new V1ServiceAvailability()
            {
                Enabled = true,
                Limit = 10,
            },
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Geographies = [new() { Id = "eu" }, new() { Id = "us" }],
        };
        V1ServiceEntry second = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Availability = new V1ServiceAvailability()
            {
                Enabled = true,
                Limit = 10,
            },
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Geographies = [new() { Id = "us" }, new() { Id = "eu" }],
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(true);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithMatchingExternalIdentities_ReturnsTrue()
    {
        // Arrange
        V1ServiceEntry first = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Availability = new V1ServiceAvailability()
            {
                Enabled = true,
                Limit = 10,
            },
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Geographies = [new() { Id = "eu" }, new() { Id = "us" }],
            ExternalIdentities = new List<V1ExternalIdentity>
            {
                new V1ExternalIdentity()
                {
                    Id = "identity1",
                    Type = V1ExternalIdentityType.AvevaRnDEntraID,
                    Scopes = new List<V1Scope> { V1Scope.opsRole, V1Scope.apiRole },
                },
                new V1ExternalIdentity()
                {
                    Id = "identity2",
                    Type = V1ExternalIdentityType.AvevaRnDEntraID,
                    Scopes = new List<V1Scope> { V1Scope.apiRole },
                },
            },
        };
        V1ServiceEntry second = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Availability = new V1ServiceAvailability()
            {
                Enabled = true,
                Limit = 10,
            },
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Geographies = [new() { Id = "us" }, new() { Id = "eu" }],
            ExternalIdentities = new List<V1ExternalIdentity>
            {
                new V1ExternalIdentity()
                {
                    Id = "identity1",
                    Type = V1ExternalIdentityType.AvevaRnDEntraID,
                    Scopes = new List<V1Scope> { V1Scope.opsRole, V1Scope.apiRole },
                },
                new V1ExternalIdentity()
                {
                    Id = "identity2",
                    Type = V1ExternalIdentityType.AvevaRnDEntraID,
                    Scopes = new List<V1Scope> { V1Scope.apiRole },
                },
            },
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(true);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_ReturnsFalse()
    {
        // Arrange
        string name = "newName";
        string id = "1";
        V1ServiceEntry subject = new V1ServiceEntry()
        {
            Id = id,
            DisplayName = name,
            HostingType = V1HostingType.External,
        };
        V1ServiceEntry subject2 = new V1ServiceEntry()
        {
            Id = id,
            DisplayName = name,
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            IconUrl = new Uri("http://test.com"),
        };

        // Act
        var result = subject.Equals(subject2);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_Null_ReturnsFalse()
    {
        // Arrange
        string name = "newName";
        string id = "2";
        string description = "some description";
        Uri iconUrl = new Uri("https://www.example.com");
        V1ServiceEntry subject = new V1ServiceEntry()
        {
            Id = id,
            DisplayName = name,
            HostingType = V1HostingType.External,
            IconUrl = iconUrl,
            Description = description,
        };

        // Act
        var result = subject.Equals(null);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithUnMatchedDependencies_ReturnsFalse()
    {
        // Arrange
        var firstDependencies = new Dictionary<string, V1CatalogDataDependency>()
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency()
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                }
            },
        };

        V1ServiceEntry first = new V1ServiceEntry()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Dependencies = firstDependencies,
        };

        var secondDependencies = new Dictionary<string, V1CatalogDataDependency>()
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency()
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                }
            },
            {
                "dependencyTwo",
                new V1CatalogDataDependency()
                {
                    Cardinality = V1CatalogDataDependencyCardinality.Many,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                }
            },
        };

        V1ServiceEntry second = new V1ServiceEntry()
        {
            Id = "CATALOG",
            DisplayName = "NAME",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://WWW.EXAMPLE.COM"),
            Description = "SOME DESCRIPTION",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "PROVIDERID",
                Trigger = V1Trigger.None,
            },
            Dependencies = secondDependencies,
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithUnMatchedDependenciesConfig_ReturnsFalse()
    {
        // Arrange
        var firstDependencies = new Dictionary<string, V1CatalogDataDependency>
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                    Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                    {
                        { "config1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Required = true, Min = 10, Max = 100 } },
                        { "config2", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Required = false, Min = 20, Max = 200 } },
                    },
                }
            },
        };
        var first = new V1ServiceEntry
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Dependencies = firstDependencies,
        };

        var secondDependencies = new Dictionary<string, V1CatalogDataDependency>
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                    Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                    {
                        { "config1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Required = true, Min = 10, Max = 100 } },
                    },
                }
            },
        };

        var second = new V1ServiceEntry
        {
            Id = "CATALOG",
            DisplayName = "NAME",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://WWW.EXAMPLE.COM"),
            Description = "SOME DESCRIPTION",
            Lifecycle = new V1Lifecycle
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "PROVIDERID",
                Trigger = V1Trigger.None,
            },
            Dependencies = secondDependencies,
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithUnMatchedApplications_ReturnsFalse()
    {
        // Arrange
        V1ServiceEntry first = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Applications =
            [
                new()
                {
                    Name = "app1",
                    Urls = new Dictionary<string, string>
                    {
                        { "url1", "http://test.url" },
                        { "url2", "http://test.url" },
                    },
                },
            ],
        };
        V1ServiceEntry second = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Applications =
            [
                new()
                {
                    Name = "app1",
                    Urls = new Dictionary<string, string>
                    {
                        { "url1", "http://test.url" },
                        { "url2", "http://test2.url" },
                    },
                },
            ],
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithUnMatchedGeographies_ReturnsFalse()
    {
        // Arrange
        V1ServiceEntry first = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Geographies = [new() { Id = "eu" }, new() { Id = "us" }],
        };
        V1ServiceEntry second = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Geographies = [new() { Id = "eu" }, new() { Id = "au" }],
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithUnMatchedExternalIdentites_ReturnsFalse()
    {
        // Arrange
        V1ServiceEntry first = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Geographies = [new() { Id = "eu" }, new() { Id = "us" }],
            ExternalIdentities = new List<V1ExternalIdentity>
            {
                new V1ExternalIdentity()
                {
                    Id = "identity1",
                    Type = V1ExternalIdentityType.AvevaRnDEntraID,
                    Scopes = new List<V1Scope> { V1Scope.opsRole, V1Scope.apiRole },
                },
                new V1ExternalIdentity()
                {
                    Id = "identity2",
                    Type = V1ExternalIdentityType.AvevaRnDEntraID,
                    Scopes = new List<V1Scope> { V1Scope.apiRole },
                },
            },
        };
        V1ServiceEntry second = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Geographies = [new() { Id = "us" }, new() { Id = "eu" }],
            ExternalIdentities = new List<V1ExternalIdentity>
            {
                new V1ExternalIdentity()
                {
                    Id = "identity1",
                    Type = V1ExternalIdentityType.AvevaRnDEntraID,
                    Scopes = new List<V1Scope> { V1Scope.opsRole, V1Scope.apiRole },
                },
                new V1ExternalIdentity()
                {
                    Id = "identity2",
                    Type = V1ExternalIdentityType.AvevaRnDEntraID,
                    Scopes = new List<V1Scope> { V1Scope.opsRole },
                },
            },
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithUnMatchedDependenciesProperties_ReturnsFalse()
    {
        // Arrange
        var firstDependencies = new Dictionary<string, V1CatalogDataDependency>()
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency()
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                }
            },
        };

        V1ServiceEntry first = new V1ServiceEntry()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Dependencies = firstDependencies,
        };

        var secondDependencies = new Dictionary<string, V1CatalogDataDependency>()
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency()
                {
                    Cardinality = V1CatalogDataDependencyCardinality.Many,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                }
            },
        };

        V1ServiceEntry second = new V1ServiceEntry()
        {
            Id = "CATALOG",
            DisplayName = "NAME",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://WWW.EXAMPLE.COM"),
            Description = "SOME DESCRIPTION",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "PROVIDERID",
                Trigger = V1Trigger.None,
            },
            Dependencies = secondDependencies,
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithUnMatchedAvailability_ReturnsFalse()
    {
        // Arrange
        V1ServiceEntry first = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Availability = new V1ServiceAvailability()
            {
                Enabled = true,
                Limit = 1,
            },
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Geographies = [new() { Id = "eu" }, new() { Id = "us" }],
        };
        V1ServiceEntry second = new()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Availability = new V1ServiceAvailability()
            {
                Enabled = true,
                Limit = 10,
            },
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Geographies = [new() { Id = "eu" }, new() { Id = "us" }],
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithUnMatchedDependenciesConfigKeys_ReturnsFalse()
    {
        // Arrange
        var firstDependencies = new Dictionary<string, V1CatalogDataDependency>
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                    Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                    {
                        { "config1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Required = true, Min = 10, Max = 100 } },
                        { "config2", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Required = false, Min = 20, Max = 200 } },
                    },
                }
            },
        };
        var first = new V1ServiceEntry
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Dependencies = firstDependencies,
        };

        var secondDependencies = new Dictionary<string, V1CatalogDataDependency>
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                    Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                    {
                        { "config1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help", Required = true, Min = 10, Max = 100 } },
                        { "config3", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Required = false, Min = 20, Max = 200 } },
                    },
                }
            },
        };

        var second = new V1ServiceEntry
        {
            Id = "CATALOG",
            DisplayName = "NAME",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://WWW.EXAMPLE.COM"),
            Description = "SOME DESCRIPTION",
            Lifecycle = new V1Lifecycle
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "PROVIDERID",
                Trigger = V1Trigger.None,
            },
            Dependencies = secondDependencies,
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithUnMatchedDependenciesConfigProperties_ReturnsFalse()
    {
        // Arrange
        var firstDependencies = new Dictionary<string, V1CatalogDataDependency>
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                    Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                    {
                        { "config1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Required = true, Min = 10, Max = 100 } },
                        { "config2", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Required = false, Min = 20, Max = 200 } },
                    },
                }
            },
        };
        var first = new V1ServiceEntry
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Dependencies = firstDependencies,
        };

        var secondDependencies = new Dictionary<string, V1CatalogDataDependency>
        {
            {
                "dependencyOne",
                new V1CatalogDataDependency
                {
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Type = V1CatalogDataDependencyType.Optional,
                    Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                    {
                        { "config1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help", Required = true, Min = 10, Max = 100 } },
                        { "config2", new V1CatalogDataDependencyConfig { Label = "label3", Help = "help3", Required = false, Min = 30, Max = 300 } },
                    },
                }
            },
        };

        var second = new V1ServiceEntry
        {
            Id = "CATALOG",
            DisplayName = "NAME",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://WWW.EXAMPLE.COM"),
            Description = "SOME DESCRIPTION",
            Lifecycle = new V1Lifecycle
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "PROVIDERID",
                Trigger = V1Trigger.None,
            },
            Dependencies = secondDependencies,
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(false);
    }

    [Fact]
    public static void V1ServiceEntryTests_Equals_WithUnMatchedApplicationsProperties_ReturnsFalse()
    {
        var firstApplications = new List<V1Application>
        {
            new V1Application
            {
                Name = "app1",
                Urls = new Dictionary<string, string>
                {
                    { "url1", "http://test.url" },
                },
            },
        };

        V1ServiceEntry first = new V1ServiceEntry()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Applications = firstApplications,
        };

        var secondApplications = new List<V1Application>
        {
            new V1Application
            {
                Name = "app1",
                Urls = new Dictionary<string, string>
                {
                    { "url1", "http://test.url" },
                    { "url2", "http://test.url" },
                },
            },
        };

        V1ServiceEntry second = new V1ServiceEntry()
        {
            Id = "catalog",
            DisplayName = "name",
            HostingType = V1HostingType.External,
            IconUrl = new Uri("https://www.example.com"),
            Description = "some description",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "providerId",
                Trigger = V1Trigger.None,
            },
            Applications = secondApplications,
        };

        // Act
        var result = first.Equals(second);

        // Assert
        result.ShouldBe(false);
    }
    #endregion Test Cases
}