﻿using System.Diagnostics.CodeAnalysis;
using k8s;
using Microsoft.Extensions.Logging;

namespace Aveva.Platform.Catalog.Tests.Integration.Kube.Common;

[SuppressMessage("Performance", "CA1848:Use the LoggerMessage delegates", Justification = "Not applicable", Scope = "module")]
[SuppressMessage("Usage", "CA2254:Template should be a static expression", Justification = "Not applicable", Scope = "module")]
internal sealed class HelmClient
{
    private readonly IntegrationTestOptions _options;
    private readonly ILogger _logger;
    private readonly IKubernetes _kubeClient;

    public HelmClient(IntegrationTestOptions options, ILogger logger, IKubernetes kubeClient)
    {
        _options = options;
        _logger = logger;
        _kubeClient = kubeClient;
    }

    public async Task<bool> IsCliInstalledAsync() => (await ProcessExtensions.RunAsync(_options.HelmExecutable).ConfigureAwait(false)).Success;

    public async Task<bool> ReleaseExistsAsync(string name, string @namespace)
        => (await ProcessExtensions.RunAsync(_options.HelmExecutable, $"get all {name} -n {@namespace}").ConfigureAwait(false)).Success;

    public async Task DependencyUpdateAsync(string chartPath)
    {
        var (success, output) = await ProcessExtensions.RunAsync(_options.HelmExecutable, $"dependency update {chartPath} --kube-context {_options.KubeContext} --debug").ConfigureAwait(false);
        if (!success)
        {
            _logger.LogWarning($"Failed to update dependencies: {output}");
        }
        else
        {
            _logger.LogInformation(output);
        }
    }

    public async Task PackageAsync(string chartPath)
    {
        var (success, output) = await ProcessExtensions.RunAsync(_options.HelmExecutable, $"package {chartPath} --destination {chartPath} --kube-context {_options.KubeContext} --debug").ConfigureAwait(false);
        if (!success)
        {
            _logger.LogWarning($"Failed to update dependencies: {output}");
        }
        else
        {
            _logger.LogInformation(output);
        }
    }

    public async Task AddRepoAsync(string name, Uri repoUrl)
    {
        var command = $"repo add {name} {repoUrl} --kube-context {_options.KubeContext}";

        var (success, output) = await ProcessExtensions.RunAsync(_options.HelmExecutable, command).ConfigureAwait(false);
        if (!success)
        {
            _logger.LogWarning($"Failed to add rep: {name} with url: {repoUrl} {command} {output}");
            throw new InvalidOperationException(output);
        }
        else
        {
            _logger.LogInformation(output);
        }
    }

    public async Task InstallReleaseAsync(string chartPath, string name, string @namespace, string? additionalArguments = null)
    {
        var command = $"upgrade {name} {chartPath} -i -n {@namespace} --create-namespace --kube-context {_options.KubeContext}";
        if (!string.IsNullOrWhiteSpace(additionalArguments))
        {
            command += " " + additionalArguments;
        }

        var (success, output) = await ProcessExtensions.RunAsync(_options.HelmExecutable, command).ConfigureAwait(false);
        if (!success)
        {
            _logger.LogWarning($"Failed to install chart: {name} {@namespace} {command} {output}");
            var events = await _kubeClient.EventsV1.ListNamespacedEventAsync(@namespace).ConfigureAwait(false);
            _logger.LogWarning(string.Join("\n", events.Items.OrderByDescending(x => x.EventTime).Select(x => $"Reason: {x.Reason}\n Object: {x.Regarding}\n Message: {x.Note}")));
            throw new InvalidOperationException(output);
        }
        else
        {
            _logger.LogInformation(output);
        }
    }

    public async Task<string> Get(string getCommand, string name, string @namespace)
    {
        var command = $"get {getCommand} {name} -n {@namespace}";

        var (success, output) = await ProcessExtensions.RunAsync(_options.HelmExecutable, command).ConfigureAwait(false);
        if (!success)
        {
            _logger.LogWarning($"Failed to get {getCommand} for chart: {name} {@namespace} {command} {output}");
            throw new InvalidOperationException(output);
        }

        return output;
    }
}