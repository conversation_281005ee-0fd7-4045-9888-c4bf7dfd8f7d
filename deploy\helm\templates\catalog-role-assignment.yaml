kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: serviceentry-view
  labels:
    rbac.authorization.k8s.io/aggregate-to-view: "true" 
rules:
- apiGroups: ["servicecatalog.aveva.com"] 
  resources: ["serviceentries"] 
  verbs: ["get", "list", "watch"] 
- apiGroups: [""] 
  resources: ["namespaces"] 
  verbs: ["get", "list"] 
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: serviceentry-view-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: serviceentry-view
subjects:
- kind: ServiceAccount
  name: catalog-api-service-account
  namespace: {{ .Release.Namespace }}