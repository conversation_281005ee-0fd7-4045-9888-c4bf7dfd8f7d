# Introduction
Contains catalog uses of the Cloud Platform Application Architecture Library. Consult each catalog's README.md for details, such as samples-demo\svc\docs\README.md.

# Getting Started
TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:
1.	Installation process
2.	Software dependencies
3.	Latest releases
4.	API references

# Build and Test
TODO: Describe and show how to build your code and run the tests.


## FAQ: Integrations Tests Spin for a while and then time out

If you get the following error:

System.Net.Http.HttpRequestException : No connection could be made because the target machine actively refused it. (localhost:8081)
---- System.Net.Sockets.SocketException : No connection could be made because the target machine actively refused it.

This means that your cosmos db emulator is not running or is not using default ports.

# Contribute
TODO: Explain how other users and developers can contribute to make your code better.

If you want to learn more about creating good readme files then refer the following [guidelines](https://docs.microsoft.com/en-us/azure/devops/repos/git/create-a-readme?view=azure-devops). You can also seek inspiration from the below readme files:
- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)