﻿using System.Text;
using k8s;
using k8s.Models;

namespace Aveva.Platform.Catalog.Tests.Integration.Kube.Common
{
    internal static class Helpers
    {
        public static async Task<IEnumerable<V1Pod>> GetPods(Kubernetes kubeClient, string @namespace)
        {
            try
            {
                var podsResponse = await kubeClient.CoreV1.ListNamespacedPodAsync(@namespace).ConfigureAwait(false);
                return podsResponse == null || podsResponse.Items == null ? Enumerable.Empty<V1Pod>() : podsResponse.Items;
            }
            catch
            {
                return Enumerable.Empty<V1Pod>();
            }
        }

        public static async Task<bool> WaitForPods(Kubernetes kubeClient, string @namespace, int maxTries = 10, int wait = 5)
        {
            var tries = 1;
            var pods = await GetPods(kubeClient, @namespace).ConfigureAwait(false);
            while (tries++ < maxTries && !PodsReady(pods))
            {
                await Task.Delay(TimeSpan.FromSeconds(wait)).ConfigureAwait(true);
                pods = await GetPods(kubeClient, @namespace).ConfigureAwait(false);
            }

            return tries < maxTries;
        }

        public static string GetPodTable(IEnumerable<V1Pod> pods)
        {
            var tablePods = pods != null ? pods.ToList() : Enumerable.Empty<V1Pod>();
            var maxName = tablePods.Any() ? tablePods.MaxBy(x => x.Name().Length).Name().Length : 4;
            var table = new StringBuilder($"{"Name".PadRight(maxName)}\tREADY\tSTATUS");
            foreach (var pod in tablePods)
            {
                var totalContainers = pod.Status != null && pod.Status.ContainerStatuses != null
                    ? $"{pod.Status.ContainerStatuses.Count}"
                    : "unknown";
                var readyContainers = pod.Status != null && pod.Status.ContainerStatuses != null
                    ? $"{pod.Status.ContainerStatuses.Select(x => x.Ready).Count()}"
                    : "unknown";
                var readyMessage = $"{readyContainers}/{totalContainers}";
                var phase = pod.Status != null ? pod.Status.Phase : "unknown";
                var message = $"\n{pod.Metadata.Name.PadRight(maxName)}\t{readyMessage.PadRight(5)}\t{phase}";
                table.Append(message);
            }

            return table.ToString();
        }

        private static bool PodsReady(IEnumerable<V1Pod> pods)
        {
            var checkPods = pods != null ? pods.ToList() : Enumerable.Empty<V1Pod>();

            // No pods
            if (!checkPods.Any())
            {
                return false;
            }

            // Are all pods complete
            if (checkPods.Any(pod => pod.Status == null || pod.Status.ContainerStatuses == null || !pod.Status.ContainerStatuses.Any()))
            {
                return false;
            }

            // TODO: Figure out a way to do this because the status can also be completed.
            // Any pods not ready
            var notreadyPods = checkPods.Where(pod => pod.Status.ContainerStatuses.Any(status => !status.Ready)).ToList();
            if (notreadyPods != null && notreadyPods.Count != 0)
            {
                if (notreadyPods.Any(pod => pod.Status.ContainerStatuses.Any(stat => stat.State.Terminated == null)))
                {
                    return false;
                }
            }

            return true;
        }
    }
}