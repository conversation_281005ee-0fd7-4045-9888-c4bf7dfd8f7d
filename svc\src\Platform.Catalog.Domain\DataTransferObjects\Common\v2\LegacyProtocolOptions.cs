﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2
{
    /// <summary>
    /// Defines how the legacy Solution and Capability Management (SCM) protocol options correspond to a catalog service. These options enable backward compatibility with systems that use the older SCM protocol.
    /// </summary>
    public class LegacyProtocolOptions : ProtocolOptions
    {
        /// <summary>
        /// The legacy SCM solution definition to which this service corresponds. This identifier links the catalog service to its equivalent representation in the legacy SCM system.
        /// </summary>
        public string? SolutionDefinition { get; init; }

        /// <summary>
        /// The mappings from the instances of this service created to the legacy SCM solution definition. These mappings define how various aspects of the service (geographies, dependencies, and applications) translate to the legacy SCM system.
        /// </summary>
        public LegacyProtocolMappings? Mappings { get; init; } = new LegacyProtocolMappings();

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is LegacyProtocolOptions item
                && string.Equals(item.SolutionDefinition, SolutionDefinition, StringComparison.InvariantCultureIgnoreCase)
                && ((item.Mappings == null && Mappings == null) || (item.Mappings != null && item.Mappings.Equals(Mappings)));
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}