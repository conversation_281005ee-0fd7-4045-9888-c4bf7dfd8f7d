﻿using System.Net;
using Aveva.Platform.Catalog.ServiceClient;
using Aveva.Platform.Common.Testing.ApiContracts.Helper;
using Aveva.Platform.InstanceMgmt.Client.Ops.Models;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.ContractTests.Consumer;

public class InstanceMgmtApiTests : IClassFixture<InstanceMgmtApiTestFixture>
{
    private readonly InstanceMgmtApiTestFixture _fixture;
    private readonly DateTime _utcMinDateTime = DateTime.MinValue.ToUniversalTime();

    public InstanceMgmtApiTests(InstanceMgmtApiTestFixture fixture, ITestOutputHelper output)
    {
        ArgumentNullException.ThrowIfNull(fixture);

        _fixture = (InstanceMgmtApiTestFixture)fixture.SetupPact(output);
    }

    [Fact]
    [Trait("Tag", "Pactflow")]
    public async Task GetInstancesAsync_Success()
    {
        var expected = new InstanceResponse
        {
            Id = "instance-1",
            AccountId = "account-1",
            ServiceId = "service-1",
            InstanceUniqueId = "instance-guid-1",
            Geography = "geo-1",
            Region = "region-1",
            Status = Status.Active,
            Name = "instance-name-1",
            StatusReason = "status-reason",
            Namespace = "namespace-1",
            CreatedDate = _utcMinDateTime,
            ModifiedDate = _utcMinDateTime,
        };

        var path = "/ops/v1/instances";
        _fixture.PactBuilder
            .UponReceiving("A request to get instances")
            .WithRequest(HttpMethod.Get, path)
            .WithQuery("serviceId", PactNet.Matchers.Match.Type(expected.ServiceId))
            .WithQuery("limit", PactNet.Matchers.Match.Type(1))
            .WithQuery("geography", PactNet.Matchers.Match.Type(expected.Geography))
            .WillRespond()
            .WithStatus(HttpStatusCode.OK)
            .WithJsonBody(new
            {
                items = new[]
                {
                    new
                    {
                        id = PactNet.Matchers.Match.Type(expected.Id),
                        accountId = PactNet.Matchers.Match.Type(expected.AccountId),
                        serviceId = PactNet.Matchers.Match.Type(expected.ServiceId),
                        instanceUniqueId = PactNet.Matchers.Match.Type(expected.InstanceUniqueId),
                        geography = PactNet.Matchers.Match.Type(expected.Geography),
                        region = PactNet.Matchers.Match.Type(expected.Region),
                        status = PactNet.Matchers.Match.Regex(expected.Status.ToString(), string.Join("|", Enum.GetNames<Status>())),
                        name = PactNet.Matchers.Match.Type(expected.Name),
                        statusReason = PactNet.Matchers.Match.Type(expected.StatusReason),
                        @namespace = PactNet.Matchers.Match.Type(expected.Namespace),
                        createdDate = PactNet.Matchers.Match.Type(_utcMinDateTime.ToString("O")),
                        modifiedDate = PactNet.Matchers.Match.Type(_utcMinDateTime.ToString("O")),
                    },
                },
            });

        await _fixture.PactBuilder.VerifyAsync(async ctx =>
        {
            using var wrapper = new ContractTestKiotaClientWrapper<InstanceMgmt.Client.Ops.Client>(ctx.MockServerUri, path);
            var kiotaClient = wrapper.Client()!;
            var client = new InstanceMgmtClient(kiotaClient);

            var actual = await client.GetInstancesAsync(new InstanceMgmt.Client.Ops.Ops.InstanceMgmt.V1.Instances.InstancesRequestBuilder.InstancesRequestBuilderGetQueryParameters
            {
                ServiceId = new[] { expected.ServiceId },
                Limit = 1,
                Geography = expected.Geography,
            }).ConfigureAwait(false);

            actual.ShouldNotBeNull();
            var item = actual.First();
            item.Id.ShouldBe(expected.Id);
            item.AccountId.ShouldBe(expected.AccountId);
            item.ServiceId.ShouldBe(expected.ServiceId);
            item.InstanceUniqueId.ShouldBe(expected.InstanceUniqueId);
            item.Geography.ShouldBe(expected.Geography);
            item.Region.ShouldBe(expected.Region);
            item.Status.ShouldBe(expected.Status);
            item.Name.ShouldBe(expected.Name);
            item.StatusReason.ShouldBe(expected.StatusReason);
            item.Namespace.ShouldBe(expected.Namespace);
            item.CreatedDate.ShouldBe(expected.CreatedDate);
            item.ModifiedDate.ShouldBe(expected.ModifiedDate);
        });
    }
}