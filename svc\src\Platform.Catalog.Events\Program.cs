﻿using System.Diagnostics.CodeAnalysis;
using Asp.Versioning.ApiExplorer;
using Aveva.Platform.Authentication.Sdk.Server.Extensions;
using Aveva.Platform.Catalog.Domain.Serialization;
using Aveva.Platform.Catalog.Events.Logging;
using Aveva.Platform.Common.Framework.Abstractions.Extensions;
using Aveva.Platform.Common.Framework.AspNetCore.Configuration;
using Aveva.Platform.Common.Framework.AspNetCore.Routing.Extensions;
using Aveva.Platform.Common.Framework.ExceptionHandling.Extensions;
using Microsoft.Extensions.Options;

// Register and configure dependencies.
WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

// Register dependencies defined in service registration modules.
builder.Services.AddServicesFromModules(builder.Configuration);

builder.Services.AddDaprClient(clientBuilder => clientBuilder.UseJsonSerializationOptions(JsonSerializationOptions.Options));

// Finalize dependency registrations and build application.
WebApplication app = builder.Build();

// At this point we can begin logging application start up progress.
ILogger<Program> logger = app.Services.GetRequiredService<ILogger<Program>>();

logger.CatalogEventServiceStartup();
ApplicationOptions applicationOptions = app.Services.GetRequiredService<IOptions<ApplicationOptions>>().Value;

// Register and configure middleware.
app.UseExceptionMiddleware(app.Environment.IsDevelopment(), new EventId((int)LoggerEvents.CatalogEventServiceGeneralException, nameof(LoggerEvents.CatalogEventServiceGeneralException)));

app.UseHttpsRedirection();
app.MapContainerHealthChecks();

// Configure API route endpoints.
app.AddRoutesFromModules();

app.UsePlatformAuthentication();
app.UseAuthorization();

app.UseSwagger();

if (applicationOptions.EnableSwaggerUI)
{
    app.UseSwaggerUI(options =>
    {
        IReadOnlyList<ApiVersionDescription> descriptions = app.DescribeApiVersions();

        foreach (ApiVersionDescription description in descriptions)
        {
            string url = $"/swagger/{description.GroupName}/swagger.json";
            string name = $"{applicationOptions.SwaggerServiceTitle} {description.GroupName}/{description.ApiVersion}";
            options.SwaggerEndpoint(url, name);
            options.EnableTryItOutByDefault();
            options.DisplayRequestDuration();
            options.RoutePrefix = string.Empty;
        }
    });
}

// Log start up of our application and run it.
logger.CatalogEventServiceRunning();
await app.RunAsync().ConfigureAwait(false);

/// <summary>
/// Entry point and composition root for the application.
/// </summary>
[ExcludeFromCodeCoverage]
public sealed partial class Program
{
    #region Private Methods
    private static void OnBeforeWebApplicationRun(WebApplication app)
    {
        ArgumentNullException.ThrowIfNull(app);
    }
    #endregion Private Methods
}