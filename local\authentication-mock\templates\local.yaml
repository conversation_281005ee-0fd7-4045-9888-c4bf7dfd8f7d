apiVersion: dapr.io/v1alpha1
kind: Configuration
metadata:
  name: tracing
spec:
  logging:
    apiLogging:
      obfuscateURLs: false
      omitHealthChecks: true
  metric:
    enabled: true
  metrics:
    enabled: true
  tracing:
    samplingRate: '1'
    stdout: true
    zipkin:
      endpointAddress: http://zipkin.zipkin.svc.cluster.local:9411/api/v2/spans
---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: imposters-role
rules:
  - verbs:
      - get
      - list
      - watch
    apiGroups:
      - ''
    resources:
      - configmaps
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: imposters-role-binding
subjects:
  - kind: ServiceAccount
    name: default
    namespace: platform-identity
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: imposters-role
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: imposters
spec:
  replicas: 1
  selector:
    matchLabels:
      pod-selector: imposters
  template:
    metadata:
      labels:
        pod-selector: imposters
      annotations:
        dapr.io/app-id: nc--authentication
        dapr.io/app-port: '4545'
        dapr.io/block-shutdown-duration: 10s
        dapr.io/config: tracing
        dapr.io/disable-builtin-k8s-secret-store: 'true'
        dapr.io/enable-api-logging: 'true'
        dapr.io/enable-metrics: 'true'
        dapr.io/enable-profiling: 'true'
        dapr.io/enabled: 'true'
        dapr.io/max-body-size: '4Mi'
        dapr.io/placement-host-address: " "
        dapr.io/read-buffer-size: "32Ki"
        dapr.io/log-level: debug
        dapr.io/sidecar-cpu-limit: 50m
        dapr.io/sidecar-cpu-request: 10m
        dapr.io/sidecar-memory-limit: 256Mi
        dapr.io/sidecar-memory-request: 32Mi
    spec:
      volumes:
        - name: imposters-volume
          configMap:
            name: imposters
      containers:
        - name: mountebank
          image: bbyars/mountebank:latest
          command:
            - /bin/sh
          args:
            - '-c'
            - mb start --configfile /imposters/imposters.json --allowInjection
          ports:
            - name: http-default
              containerPort: 2525
              protocol: TCP
            - name: http-custom
              containerPort: 4545
              protocol: TCP
          env:
            - name: Instrumentation__PodName
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: HelmChartRenderDate
              value: "2024-07-29 15:39:27.0477579 +0100 BST m=+0.*********"
          resources:
            limits:
              cpu: 500m
              memory: 1536Mi
            requests:
              cpu: 10m
              memory: 100Mi
          volumeMounts:
            - name: imposters-volume
              mountPath: /imposters/imposters.json
              subPath: imposters.json
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
          securityContext: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      automountServiceAccountToken: false
      securityContext: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: imposters
data:
  imposters.json: |-
    {
      "imposters": [
        {
          "port": 4545,
          "protocol": "http",
          "stubs": [
{{- .Files.Get "imposters/authentication-get-service-token.json" | indent 12 }},
{{- .Files.Get "imposters/404-catchall.json" | indent 12 }}
          ]
        }
      ]
    }
