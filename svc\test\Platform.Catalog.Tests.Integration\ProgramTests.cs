﻿using System;
using Aveva.CloudPlatform.Architecture.Abstractions.Mapping;
using Aveva.Platform.Catalog.Domain.Contracts;
using Dapr.Client;
using k8s;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Tests.Integration;

/// <summary>
/// <see cref="Program"/> integration test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "App")]
[Trait("Category", "Integration")]
[Trait("Category", "App.Integration")]
[Trait("Tag", "Program")]
[Trait("Tag", "Configuration")]
public class ProgramTests
{
    #region Test Cases

    [Fact]
    public void Program_WebApplicationBuilder_RegistersExpectedServices()
    {
        // Arrange & Act
        using WebApplicationFactory<Program> subject = new WebApplicationFactory<Program>();

        // Assert
        IServiceProvider serviceProvider = subject.Services;
        using IServiceScope scope = serviceProvider.CreateScope();
        serviceProvider.ShouldSatisfyAllConditions(
        () => AssertApplicationServiceRegistrations(serviceProvider),
        () => AssertDomainServiceRegistrations(serviceProvider, scope),
        () => AssertInfrastructureServiceRegistrations(serviceProvider, scope));
    }

    #endregion Test Cases

    #region Private Methods

    private static void AssertApplicationServiceRegistrations(IServiceProvider serviceProvider)
    {
        // APPLICATION
        serviceProvider.ShouldSatisfyAllConditions(
            "AssertApplicationServiceRegistrations",
            () => serviceProvider.GetService<ILogger<ProgramTests>>().ShouldNotBeNull(),
            () => serviceProvider.GetService<IKubernetes>().ShouldNotBeNull(),
            () => serviceProvider.GetService<ITypeMappingService>().ShouldNotBeNull());
    }

    private static void AssertApiServiceRegistrations(IServiceProvider serviceProvider)
    {
        // API LAYER
        // serviceProvider.ShouldSatisfyAllConditions(
        //    "AssertApiServiceRegistrations",
        //    () => serviceProvider.GetService<IValidator<CreateCatalogRequest>>().ShouldNotBeNull(),
        //    () => serviceProvider.GetService<IValidator<CreateOrUpdateCatalogRequest>>().ShouldNotBeNull());
    }

    private static void AssertDomainServiceRegistrations(IServiceProvider serviceProvider, IServiceScope scope)
    {
        // DOMAIN LAYER
        serviceProvider.ShouldSatisfyAllConditions();
    }

    private static void AssertInfrastructureServiceRegistrations(IServiceProvider serviceProvider, IServiceScope scope)
    {
        // INFRASTRUCTURE LAYER
        ICachedCatalogRepository repository = scope.ServiceProvider.GetService<ICachedCatalogRepository>();

        serviceProvider.ShouldSatisfyAllConditions(
            "AssertInfrastructureServiceRegistrations",
            () => serviceProvider.GetService<ITypeMappingService>().ShouldNotBeNull(),
            () => repository.ShouldNotBeNull(),
            () => serviceProvider.GetService<IMemoryCache>().ShouldNotBeNull());
    }

    #endregion Private Methods
}