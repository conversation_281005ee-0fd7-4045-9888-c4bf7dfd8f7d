import { K6TestBase } from "@platform/performance-libs";
import { opsGetAllBase } from "../../common/opsV2_getAll";

export class CatalogPerformanceTests extends K6TestBase {
    baseTestClass: opsGetAllBase;
    constructor() {
        super();
        this.baseTestClass = new opsGetAllBase();
    }

    defaultScenarioTestIteration(data: any): void {
        this.baseTestClass.defaultScenarioTestIteration(data);
    }

    sharedTestSetup(): any {
        return this.baseTestClass.sharedTestSetup();
    }

    sharedTestTeardown(data: any): void {
        this.baseTestClass.sharedTestTeardown(data);
    }
}
