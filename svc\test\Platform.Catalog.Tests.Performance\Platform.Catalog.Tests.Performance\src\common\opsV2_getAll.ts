import { OpsClient } from "@platform/performance-libs";
import { check, fail } from "k6";
import { Logger } from "@platform/performance-libs";
import { LogLevel } from "@platform/performance-libs";
import { Ops as v2CatalogOpsModels } from "@platform/aveva.platform.catalog.tsclient";
import { v2OpsGetAllServices } from "../utils/common/servicesHelper";

export class opsGetAllBase {
    opsClient: OpsClient;
    logger: Logger;

    constructor() {
        this.opsClient = new OpsClient();

        //URL information for tests, constructs base url for both routes and token end point url for api route
        this.opsClient.config.baseUrl = `https://${__ENV.ENVPREFIX}.${__ENV.DNS}`;
        this.opsClient.config.opsTokenEndpoint = `https://login.microsoftonline.com/${__ENV.TENANTID}/oauth2/v2.0/token`;        

        this.logger = new Logger(
            LogLevel[__ENV.LOG_LEVEL as keyof typeof LogLevel]
        );
    }

    sharedTestSetup(): any {
        this.logger.info("Setup of PerformanceServicesTest");
        this.logger.info(`Base URL is - ${this.opsClient.config.baseUrl}`);
        this.logger.info(`Token generation URL is - ${this.opsClient.config.opsTokenEndpoint}`);
        this.opsClient.config.clientId = __ENV.CLIENTID;
        this.opsClient.config.clientSecret = __ENV.CLIENTSECRET;

        this.opsClient.authenticate();

        var data = {
            clientId: this.opsClient.config.clientId,
            clientSecret: this.opsClient.config.clientSecret,
            vuOpsBearerObject: this.opsClient.bearerToken
        };

        return data;
    }

    defaultScenarioTestIteration(data: any): void {
        const lifecycle: v2CatalogOpsModels.Lifecycle = {
            trigger: "Account",
            instanceMode: "Shared",
        }
        var emptyArray: string[];
        const responseExpected: v2CatalogOpsModels.ServiceResponse = {
            id: "catalog",
            displayName: "Catalog services",
            hostingType: "Environment",
            iconUrl: "https://cdn.aveva.design/icons/svg/outlined/32/content/content--library.svg",
            description: "Catalog services",
            lifecycle: lifecycle,
            tags: emptyArray,
        };

        try {
            this.opsClient.config.clientId = data.clientId;
            this.opsClient.config.clientSecret = data.clientSecret;
            this.opsClient.bearerToken = data.vuOpsBearerObject;       

            const getResponse = v2OpsGetAllServices(this.opsClient, "catalog");

            if (!getResponse) {
                fail(`Attempt to retrieve services failed`);
            }

            for (let entry of getResponse.items) {
                if (entry.id == "catalog") {
                    check(entry, {
                        [`Catalog should have expected id`]: (c) => c.id === responseExpected.id,
                        [`Catalog should have expected displayName`]: (c) => c.displayName === responseExpected.displayName,
                        [`Catalog should have expected hostingType`]: (c) => c.hostingType === responseExpected.hostingType,
                        [`Catalog should have expected iconUrl`]: (c) => c.iconUrl === responseExpected.iconUrl,
                        [`Catalog should have expected description`]: (c) => c.description === responseExpected.description,
                        [`Catalog should have expected lifecycle trigger`]: (c) => c.lifecycle.trigger === responseExpected.lifecycle.trigger,
                        [`Catalog should have expected lifecycle instance mode`]: (c) => c.lifecycle.instanceMode === responseExpected.lifecycle.instanceMode,
                    });
                }
            }
        } catch (e: any) {
            this.logger.error(`There was some exception. ${e}`);
        }
    }

    sharedTestTeardown(_data: any): void {
        this.logger.info(`No necessary teardown for PerformanceServicesTest at ${new Date()}`);
    }
}