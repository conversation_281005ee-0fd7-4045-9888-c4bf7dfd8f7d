apiVersion: chaos-mesh.org/v1alpha1
kind: Schedule
metadata:
  name: network-partition
  namespace: chaos-mesh
spec:
  schedule: "@every 2m"
  type: "NetworkChaos"
  concurrencyPolicy: Allow
  networkChaos:
    action: partition
    mode: all  
    selector:
      namespaces:
        - platform-catalog
      labelSelectors:
        pod-selector: catalog-api
    duration: "2m"
    direction: to
    externalTargets:
    # connection to service bus:
      - "sb-eucliddev-gl-fffbx774rz6lo.servicebus.windows.net"