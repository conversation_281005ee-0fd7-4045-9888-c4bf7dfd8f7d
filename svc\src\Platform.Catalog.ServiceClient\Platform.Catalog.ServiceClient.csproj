<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>Aveva.Platform.Catalog.ServiceClient</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.ServiceClient</RootNamespace>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aveva.Platform.AccountMgmt.Client" />
    <PackageReference Include="Aveva.Platform.Authentication.Sdk.S2SCommon" />
    <PackageReference Include="Aveva.Platform.Authentication.Sdk.KiotaClient" />
    <PackageReference Include="Aveva.Platform.Authentication.Service.Handler" />
    <PackageReference Include="Aveva.Platform.Common.Framework.AspNetCore" />
    <PackageReference Include="Aveva.Platform.InstanceMgmt.Client" />
    <PackageReference Include="Dapr.Client" />
    <PackageReference Include="Dapr.AspNetCore" />
    <PackageReference Include="Dapr.Extensions.Configuration" />
    <PackageReference Include="Aveva.Platform.Common.Abstractions" />
    <PackageReference Include="Aveva.Platform.Common.Monitoring.Instrumentation" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\Platform.Catalog.Domain\Platform.Catalog.Domain.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
