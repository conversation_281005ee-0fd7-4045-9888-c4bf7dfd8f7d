﻿using Aveva.Platform.Catalog.Domain.Models;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Integration.Models;

/// <summary>
/// <see cref="V1ServiceEntry"/> integration test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Integration")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Integration")]
public static class CatalogTests
{
    #region Test Cases

    [Fact]
    public static void Catalog_UpdateFrom_UpdatesTask()
    {
        // Arrange
        V1ServiceEntry source = new V1ServiceEntry()
        {
            Id = "42",
            DisplayName = Guid.NewGuid().ToString("N"),
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Trigger = V1Trigger.None,
            },
        };

        V1ServiceEntry target = new V1ServiceEntry()
        {
            Id = "99",
            DisplayName = Guid.NewGuid().ToString("N"),
            HostingType = V1HostingType.Geography,
            Description = "Test Updated",
            Lifecycle = new V1Lifecycle()
            {
                InstanceMode = V1InstanceMode.Shared,
                Trigger = V1Trigger.Account,
            },
        };

        // Act
        V1ServiceEntry actual = source.UpdateFrom(target);

        // Assert
        actual.ShouldSatisfyAllConditions(
            () => actual.Id.ShouldBe(source.Id, "Should not have updated the identifier"),
            () => actual.DisplayName.ShouldBe(target.DisplayName),
            () => actual.Description.ShouldBe(target.Description),
            () => actual.Category.ShouldBe(target.Category),
            () => actual.HostingType.ShouldBe(target.HostingType));
    }

    #endregion Test Cases
}