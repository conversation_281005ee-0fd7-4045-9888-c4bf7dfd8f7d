﻿using AutoMapper;
using Aveva.Platform.Catalog.Domain.Queries;
using ApiModels = Aveva.Platform.Catalog.Api.DataTransferObjects.Api.v2;
using OpsModels = Aveva.Platform.Catalog.Api.DataTransferObjects.Ops.v2;

namespace Aveva.Platform.Catalog.Api.Mapping;

/// <summary>
/// AutoMapper type mapping configuration profile used to configure Data Transfer Object (DTO) and domain entity type mappings needed by the API layer.
/// </summary>
/// <seealso cref="Profile"/>
/// <seealso href="https://docs.automapper.org/en/stable/Configuration.html#profile-instances"/>
public class TypeMappingProfile : Profile
{
    #region Public Constructors

    /// <summary>
    /// Initializes a new instance of the <see cref="TypeMappingProfile"/> class.
    /// </summary>
    public TypeMappingProfile()
    {
        CreateMap<ApiModels.ServiceQueryRequest, ServiceEntryQuery>();
        CreateMap<OpsModels.ServiceQueryRequest, ServiceEntryQuery>();
    }

    #endregion Public Constructors
}