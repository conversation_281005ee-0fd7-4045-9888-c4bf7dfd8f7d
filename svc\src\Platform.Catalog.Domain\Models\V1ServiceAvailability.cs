﻿namespace Aveva.Platform.Catalog.Domain.Models;

/// <summary>
/// ServiceAvailability.
/// </summary>
public class V1ServiceAvailability
{
    private const bool EnabledDefaultValue = true;
    private const int LimitDefaultValue = 10;

    /// <summary>
    /// Gets or sets whether the service is enabled.
    /// </summary>
    public bool? Enabled { get; set; }

    /// <summary>
    /// Gets or sets the number of instances an account can have of this service.
    /// </summary>
    public int? Limit { get; set; }

    /// <summary>
    /// This is the default settings for service availability for all service entries.
    /// </summary>
    /// <returns>V1ServiceAvailability.</returns>
    public static V1ServiceAvailability GetDefault()
    {
        return new V1ServiceAvailability
        {
            Enabled = EnabledDefaultValue,
            Limit = LimitDefaultValue,
        };
    }

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        if (obj == null)
        {
            return false;
        }

        return obj is V1ServiceAvailability item
            && ((item.Enabled == null && Enabled == null) || (item.Enabled != null && item.Enabled.Equals(Enabled)))
            && ((item.Limit == null && Limit == null) || (item.Limit != null && item.Limit.Equals(Limit)));
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
}