﻿using System.Text.Json.Serialization;

namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2
{
    /// <summary>
    /// Specifies whether the dependent service must be available (`Required`) or not (`Optional`) when a catalog service is created.
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum DependencyType
    {
        /// <summary>
        /// The dependency is not required for the catalog service to function. The service can be provisioned and operate without this dependent service. Use this when the dependent service provides supplementary functionality that enhances but is not critical to the main service operation.
        /// </summary>
        Optional,

        /// <summary>
        /// The dependency is required for the catalog service to function. The service cannot be provisioned or operate correctly without this dependent service being available. Use this when the dependent service provides essential functionality that the main service cannot operate without.
        /// </summary>
        Required,
    }
}