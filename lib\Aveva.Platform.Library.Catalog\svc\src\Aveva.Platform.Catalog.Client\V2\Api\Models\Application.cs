// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Api.Models
{
    /// <summary>
    /// Defines service applications, where each additional property represents an application. A representation of an application that belongs to a service in the catalog, containing information like name and access URLs.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class Application : IParsable
    {
        /// <summary>The name of the application. The unique name that identifies this application within a service. This name is used for display and reference purposes.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
        /// <summary>Optional URLs keyed to launch for specific valid geographies and/or default URLs to launch if a geography is not identified. A dictionary of URLs where keys represent geography codes and values are the corresponding application launch URLs. If any URLs are provided, a `default` key must be included to specify the URL to use when no specific geography match is found.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::Aveva.Platform.Catalog.Client.V2.Api.Models.Application_urls? Urls { get; set; }
#nullable restore
#else
        public global::Aveva.Platform.Catalog.Client.V2.Api.Models.Application_urls Urls { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Models.Application"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::Aveva.Platform.Catalog.Client.V2.Api.Models.Application CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::Aveva.Platform.Catalog.Client.V2.Api.Models.Application();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "name", n => { Name = n.GetStringValue(); } },
                { "urls", n => { Urls = n.GetObjectValue<global::Aveva.Platform.Catalog.Client.V2.Api.Models.Application_urls>(global::Aveva.Platform.Catalog.Client.V2.Api.Models.Application_urls.CreateFromDiscriminatorValue); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("name", Name);
            writer.WriteObjectValue<global::Aveva.Platform.Catalog.Client.V2.Api.Models.Application_urls>("urls", Urls);
        }
    }
}
#pragma warning restore CS0618
