﻿using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Logging;

namespace Aveva.Platform.Catalog.Tests.Integration.Kube.Common;

[SuppressMessage("Performance", "CA1848:Use the LoggerMessage delegates", Justification = "Not applicable", Scope = "module")]
[SuppressMessage("Usage", "CA2254:Template should be a static expression", Justification = "Not applicable", Scope = "module")]
internal sealed class KubectlClient
{
    private readonly IntegrationTestOptions _options;
    private readonly ILogger _logger;

    public KubectlClient(IntegrationTestOptions options, ILogger logger)
    {
        _options = options;
        _logger = logger;
    }

    public async Task<bool> IsCliInstalledAsync() => (await ProcessExtensions.RunAsync(_options.KubectlExecutable).ConfigureAwait(false)).Success;

    public async Task<string> ExecuteAsync(string command)
    {
        var (success, output) = await ProcessExtensions.RunAsync(_options.KubectlExecutable, command).ConfigureAwait(false);
        if (!success)
        {
            _logger.LogWarning($"Failed to execute command: {command} {output}");
        }

        return output;
    }

    public Process? Start(string command)
    {
        var (success, process) = ProcessExtensions.Start(_options.KubectlExecutable, command);
        if (!success)
        {
            _logger.LogWarning($"Failed to execute command: {command}");
            return null;
        }

        return process;
    }
}