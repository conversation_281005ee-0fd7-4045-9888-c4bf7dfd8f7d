﻿using System.Diagnostics.CodeAnalysis;

namespace Aveva.Platform.Catalog.Logging;

[ExcludeFromCodeCoverage(Justification = "LoggerMessage logging pattern implementation")]
internal static partial class LoggerExtensions
{
    #region Internal Methods

    [LoggerMessage((int)LoggerEvents.ApplicationStartupApplyingConfiguration, LogLevel.Information, "The application is starting up and applying configuration...")]
    internal static partial void ApplicationStartupApplyingConfiguration(this ILogger logger);

    [LoggerMessage((int)LoggerEvents.ApplicationStartupCompletedApplyingConfiguration, LogLevel.Information, "The application has successfully applied configuration...")]
    internal static partial void ApplicationStartupCompletedApplyingConfiguration(this ILogger logger);

    [LoggerMessage((int)LoggerEvents.ApplicationFailedToStartup, LogLevel.Error, "The application failed to start up. There appears to be a configuration issue or a required resource is not available or was not initialized.")]
    internal static partial void ApplicationFailedToStartup(this ILogger logger);

    [LoggerMessage((int)LoggerEvents.ApplicationStartupConfigurationReport, LogLevel.Information, "{sectionName} settings {details}")]
    internal static partial void ApplicationStartupConfigurationReport(this ILogger logger, string sectionName, string? details);

    [LoggerMessage((int)LoggerEvents.ApplicationRunning, LogLevel.Information, "The application is running...")]
    internal static partial void ApplicationRunning(this ILogger logger);

    #endregion Internal Methods
}