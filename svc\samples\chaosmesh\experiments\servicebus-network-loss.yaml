apiVersion: chaos-mesh.org/v1alpha1
kind: NetworkChaos
metadata:
  name: servicebus-network-loss-experiment
  namespace: chaos-mesh
spec:
  action: loss                       # Action to simulate packet loss
  mode: all                          # Apply to all matching pods
  selector:
    namespaces:
      - platform-catalog             # Target namespace
    labelSelectors:
      pod-selector: catalog-events      # Label to identify specific pods
  loss:
    loss: "75"                       # 75% packet loss
    correlation: '50'                # Correlation rate
  direction: to                    # from for outgoing, to for incoming
  duration: "2m"                     # Total duration of the experiment (2 minutes)
  externalTargets:
    - "sb-eucliddev-gl-fffbx774rz6lo.servicebus.windows.net"  # Connection to service bus

# Key parameters for the NetworkChaos experiment:
# - action: Specifies the type of network chaos.
# - mode: Determines whether to target all pods or a specific set.
# - selector: Filters the pods based on namespace and labels.
# - loss: Defines the packet loss percentage and correlation.
# - direction: Specifies direction of traffic affected.
# - duration: The total time the chaos experiment will run.
