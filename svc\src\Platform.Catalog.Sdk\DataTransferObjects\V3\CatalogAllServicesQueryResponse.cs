﻿namespace Aveva.Platform.Catalog.Sdk.DataTransferObjects.V3;

/// <summary>
/// Catalog all services response.
/// </summary>
/// <typeparam name="T">The type of the service response.</typeparam>
public class CatalogAllServicesQueryResponse<T>
{
    /// <summary>
    /// Gets or sets services in catalog.
    /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
    public List<T>? Services
    {
        get
        {
            return Items;
        }

        set
        {
            Items = value;
        }
    }

    /// <summary>
    /// Gets service entries in catalog.
    /// </summary>
    public List<T>? Items { get; private set; }
#pragma warning restore CA2227 // Collection properties should be read only
}