﻿namespace Aveva.Platform.Catalog.Domain.Models;

/// <summary>
/// Defines the mapping for a dependency in the legacy protocol.
/// </summary>
public class V1LegacyProtocolDependencyMapping
{
    /// <summary>
    /// The integration definition name this corresponds to.
    /// </summary>
    public required string IntegrationDefinition { get; set; }

    /// <summary>
    /// The id/name of the config param for the dependency whose value should be provided as the sourceContext as part of the legacy protocol.
    /// </summary>
    public string? SourceContextConfig { get; set; }

    /// <summary>
    /// The id/name of the config param for the dependency whose value should be provided as the targetContext as part of the legacy protocol.
    /// </summary>
    public string? TargetContextConfig { get; set; }

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        if (obj == null)
        {
            return false;
        }

        return obj is V1LegacyProtocolDependencyMapping item
            && string.Equals(item.IntegrationDefinition, IntegrationDefinition, StringComparison.InvariantCultureIgnoreCase)
            && string.Equals(item.SourceContextConfig, SourceContextConfig, StringComparison.InvariantCultureIgnoreCase)
            && string.Equals(item.TargetContextConfig, TargetContextConfig, StringComparison.InvariantCultureIgnoreCase);
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
}