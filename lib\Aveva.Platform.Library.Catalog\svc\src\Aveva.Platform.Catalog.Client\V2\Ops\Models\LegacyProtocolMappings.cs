// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Ops.Models
{
    /// <summary>
    /// Defines the mapping between a service and corresponding legacy Solution and Capability Management (SCM) solution definition. This mapping enables backward compatibility with systems that use the older SCM protocol.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class LegacyProtocolMappings : IParsable
    {
        /// <summary>The mappings of applications to SCM capability definitions. Each entry defines how an application in the current system relates to a capability definition in the legacy SCM system.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolApplicationMapping>? Applications { get; set; }
#nullable restore
#else
        public List<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolApplicationMapping> Applications { get; set; }
#endif
        /// <summary>The mapping of the service dependencies to legacy SCM integration definitions. Keys represent dependency identifiers in the current system, and values contain the mapping details for connecting to the legacy SCM integration system.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings_dependencies? Dependencies { get; set; }
#nullable restore
#else
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings_dependencies Dependencies { get; set; }
#endif
        /// <summary>The mapping of geographies to the legacy declared SCM solution regions. Keys represent geography IDs in the current system, and values represent the corresponding region identifiers in the legacy SCM system.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings_geographies? Geographies { get; set; }
#nullable restore
#else
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings_geographies Geographies { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "applications", n => { Applications = n.GetCollectionOfObjectValues<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolApplicationMapping>(global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolApplicationMapping.CreateFromDiscriminatorValue)?.AsList(); } },
                { "dependencies", n => { Dependencies = n.GetObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings_dependencies>(global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings_dependencies.CreateFromDiscriminatorValue); } },
                { "geographies", n => { Geographies = n.GetObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings_geographies>(global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings_geographies.CreateFromDiscriminatorValue); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteCollectionOfObjectValues<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolApplicationMapping>("applications", Applications);
            writer.WriteObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings_dependencies>("dependencies", Dependencies);
            writer.WriteObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings_geographies>("geographies", Geographies);
        }
    }
}
#pragma warning restore CS0618
