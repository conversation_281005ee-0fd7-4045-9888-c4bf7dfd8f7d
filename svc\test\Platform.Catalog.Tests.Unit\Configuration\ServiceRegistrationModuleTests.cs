﻿using Asp.Versioning;
using Aveva.Platform.Catalog.Configuration;
using Aveva.Platform.Catalog.Domain;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Common.Framework.Abstractions.Extensions;
using Aveva.Platform.Common.Framework.Abstractions.Mapping;
using Aveva.Platform.Common.Framework.ExceptionHandling;
using Aveva.Platform.Common.Testing.Extensions;
using k8s;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Moq;
using Shouldly;
using Swashbuckle.AspNetCore.SwaggerGen;
using Xunit;

namespace Aveva.Platform.Catalog.Tests.Unit.Configuration;

/// <summary>
/// <see cref="ServiceRegistrationModule"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "App")]
[Trait("Category", "Unit")]
[Trait("Category", "App.Unit")]
[Trait("Tag", "Configuration")]
public class ServiceRegistrationModuleTests
{
    #region Test Cases

    [Fact]
    public void ServiceRegistrationModule_Null_ConstructorParameter_Throws()
    {
        // Arrange
        IConfiguration? configuration = null;

        // Act & Assert
        Should.Throw<ArgumentNullException>(() =>
        {
            ServiceRegistrationModule serviceRegistrationModule = new ServiceRegistrationModule(configuration!);
        });
    }

    [Fact]
    public void ServiceRegistrationModule_AddServicesFromModules_RegistersExpectedServices()
    {
        // Arrange
        ServiceCollection services = new();
        var inMemorySettings = new Dictionary<string, string?>()
        {
            { "Apis:AccountMgmtService:serviceIdentityId", "accountmgmt" },
            { "Apis:CatalogService:serviceIdentityId", CatalogConstants.ServiceId },
            { "Apis:CatalogEventsService:serviceIdentityId", CatalogConstants.ServiceId },
            { "Apis:CatalogService:BaseServiceUrl", "http://catalog.platform-catalog" },
            { "Apis:CatalogEventsService:BaseServiceUrl", "http://catalog.platform-catalog" },
            { "Apis:InstanceMgmtService:serviceIdentityId", "instancemgmt" },
            { "Authentication:IdentityDnsZoneName", "https://www.example.com" },
            { "Authentication:ServiceId", CatalogConstants.ServiceId },
            { "Instrumentation:ServiceName", "catalog" },
            { "Instrumentation:RoleName", "catalog" },
        };

        IConfiguration configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(inMemorySettings)
            .Build();

        services.AddSingleton(new Mock<IWebHostEnvironment>().Object);
        services.AddSingleton(new Mock<LinkGenerator>().Object);

        // Act
        services.AddServicesFromModules(configuration);

        // Assert
        services.ShouldSatisfyAllConditions(
            () => AssertApplicationServiceRegistrations(services),
            () => AssertApiServiceRegistrations(services),
            () => AssertDomainServiceRegistrations(services),
            () => AssertInfrastructureServiceRegistrations(services));
    }

    #endregion Test Cases

    #region Private Methods

    private static void AssertApplicationServiceRegistrations(ServiceCollection services)
    {
        // APPLICATION
        services.ShouldSatisfyAllConditions(
            "AssertApplicationServiceRegistrations",
            () => services.ShouldBeRegistered<IHttpContextAccessor, HttpContextAccessor>(ServiceLifetime.Singleton));

            // TODO - Implement the testing part along with monitoring library
            // () => services.ShouldBeRegistered<TelemetryClient>(ServiceLifetime.Singleton),
            // () => services.ShouldBeRegistered<ITelemetryModule, RequestTrackingTelemetryModule>(ServiceLifetime.Singleton),
            // () => services.ShouldBeRegistered<ILoggerFactory>(ServiceLifetime.Singleton));
    }

    private static void AssertApiServiceRegistrations(ServiceCollection services)
    {
        // API LAYER
        services.ShouldSatisfyAllConditions(
            "AssertApiServiceRegistrations",
            () => services.ShouldBeRegistered<IExceptionTypeMap, ExceptionTypeMap>(ServiceLifetime.Singleton),
            () => services.ShouldBeRegistered<IExceptionHandler, ExceptionHandler>(ServiceLifetime.Singleton),
            () => services.ShouldBeRegistered<IErrorResponseFormatter>(ServiceLifetime.Singleton),
            () => services.ShouldBeRegistered<IApiDescriptionGroupCollectionProvider, ApiDescriptionGroupCollectionProvider>(ServiceLifetime.Singleton),
            () => services.ShouldBeRegistered<SwaggerGenerator>(ServiceLifetime.Transient),
            () => services.ShouldBeRegistered<HealthCheckService>(ServiceLifetime.Singleton),
            () => AssertApiVersioningServiceRegistrations(services));
    }

    private static void AssertApiVersioningServiceRegistrations(ServiceCollection services)
    {
        // API LAYER
        services.ShouldSatisfyAllConditions(
            "AssertApiVersioningServiceRegistrations",
            () => services.ShouldBeRegistered<IApiVersionParser, ApiVersionParser>(ServiceLifetime.Singleton),
            () => services.ShouldBeRegistered<IApiDescriptionGroupCollectionProvider, ApiDescriptionGroupCollectionProvider>(ServiceLifetime.Singleton));
    }

    private static void AssertDomainServiceRegistrations(ServiceCollection services)
    {
        // DOMAIN LAYER
        services.ShouldSatisfyAllConditions(
            "AssertDomainServiceRegistrations",
            () => services.ShouldBeRegistered<IKubernetes>(ServiceLifetime.Singleton),
            () => services.ShouldBeRegistered<CatalogMetrics>(ServiceLifetime.Singleton));
    }

    private static void AssertInfrastructureServiceRegistrations(ServiceCollection services)
    {
        // INFRASTRUCTURE LAYER
        services.ShouldSatisfyAllConditions(
            "AssertInfrastructureServiceRegistrations",
            () => services.ShouldBeRegistered<ITypeMappingService>(ServiceLifetime.Singleton),
            () => services.ShouldBeRegistered<IServiceEntryRepository>(ServiceLifetime.Singleton),
            () => services.ShouldBeRegistered<IServiceAvailabilityRepository>(ServiceLifetime.Singleton),
            () => services.ShouldBeRegistered<IMemoryCache>(ServiceLifetime.Singleton));
    }

    #endregion Private Methods
}