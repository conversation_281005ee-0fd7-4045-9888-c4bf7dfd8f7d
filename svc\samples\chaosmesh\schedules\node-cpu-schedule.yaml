apiVersion: chaos-mesh.org/v1alpha1
kind: Schedule
metadata:
  name: stress-cpu
  namespace: chaos-mesh
spec:
  schedule: "@every 30s"
  type: StressChaos
  historyLimit: 5 
  concurrencyPolicy: Allow
  stressChaos:
    mode: all  
    selector:
      namespaces: 
        - platform-catalog
      labelSelectors:
        pod-selector: catalog-api
    stressors:
      cpu:
        workers: 8
        load: 95  # 95% CPU utilization
    duration: "25s"