apiVersion: chaos-mesh.org/v1alpha1
kind: NetworkChaos
metadata:
  name: servicebus-network-failure-experiment
  namespace: chaos-mesh
spec:
  action: partition                  # Action to partition network
  mode: all                          # Apply to all matching pods
  selector:
    namespaces:
      - platform-catalog             # Target namespace
    labelSelectors:
      pod-selector: catalog-events      # Label to identify specific pods
  duration: "10m"                    # Total duration of the experiment
  direction: both                    # Optional direction for network partition
  externalTargets:
    - "sb-eucliddev-gl-fffbx774rz6lo.servicebus.windows.net"  # Connection to service bus

# Key parameters for the NetworkChaos experiment:
# - action: Specifies the type of network chaos.
# - mode: Determines whether to target all pods or a specific set.
# - selector: Filters the pods based on namespace and labels.
# - duration: The total time the chaos experiment will run.
# - externalTargets: Specifies external services affected by network partition.
