﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Aveva.Platform.Catalog.Api.Routing
{
    internal class TagDescriptionDocumentFilter : IDocumentFilter
    {
        // Descriptions of each API Groups.
        private const string CatalogV2 = "Discover and retrieve catalog service entries. These endpoints provide access to service metadata and availability information, allowing you to explore which services can be provisioned to your account.";

        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            if (string.Equals(context.DocumentName, "api-v2", StringComparison.OrdinalIgnoreCase) || string.Equals(context.DocumentName, "operations-v2", StringComparison.OrdinalIgnoreCase))
            {
                swaggerDoc.Tags =
                [
                    new() { Name = nameof(CatalogV2), Description = CatalogV2 },
                ];
            }
        }
    }
}