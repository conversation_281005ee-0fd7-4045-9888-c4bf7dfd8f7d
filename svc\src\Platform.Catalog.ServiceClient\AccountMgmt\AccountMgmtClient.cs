﻿using System.Net;
using Aveva.Platform.AccountMgmt.Client.Ops.Models;
using Aveva.Platform.AccountMgmt.Client.Ops.Ops.AccountMgmt.V1.Accounts.Item.ServiceOverrides;
using Aveva.Platform.Catalog.Domain.Models;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Serialization;

namespace Aveva.Platform.Catalog.ServiceClient.AccountMgmt
{
    /// <summary>
    /// Account Mgmt class to communicate with Account Mgt API service.
    /// </summary>
    public class AccountMgmtClient(Platform.AccountMgmt.Client.Ops.Client client) : IAccountMgmtClient
    {
        /// <summary>
        /// Gets all Geographies.
        /// </summary>
        /// <returns>The Geography if found.</returns>
        public async Task<IEnumerable<GeographyResponse>> QueryGeographiesAsync(CancellationToken cancellationToken = default)
        {
            return await client.Ops.AccountMgmt.V1.Geographies.GetAsync(option => option.QueryParameters.IncludeExtensions = true, cancellationToken)
                .ConfigureAwait(false) ?? Enumerable.Empty<GeographyResponse>();
        }

        /// <summary>
        /// Gets the serviceAvailability of services for the account.
        /// </summary>
        /// <param name="accountId">The accountId.</param>
        /// <param name="cancellationToken">The cancellationToken.</param>
        /// <returns>The serviceAvailability of services for the account.</returns>
        /// <exception cref="NotImplementedException">NotImplementaedException.</exception>
        public async Task<Dictionary<string, V1ServiceAvailability>?> QueryAvailabilityAsync(string accountId, CancellationToken cancellationToken = default)
        {
            try
            {
                return PopulateServiceAvailability(await client.Ops.AccountMgmt.V1.Accounts[accountId].ServiceOverrides.GetAsync(null, cancellationToken).ConfigureAwait(false));
            }
            catch (ApiException ex) when (ex.ResponseStatusCode == (int)HttpStatusCode.NotFound)
            {
                return null;
            }
        }

        private static Dictionary<string, V1ServiceAvailability> PopulateServiceAvailability(ServiceOverridesGetResponse? result)
        {
            result ??= new ServiceOverridesGetResponse();

            return result.AdditionalData
                .Select(x => new KeyValuePair<string, object>(x.Key, ParseServiceAvailability(x.Value)))
                .Where(x => x.Value is V1ServiceAvailability)
                .ToDictionary(x => x.Key, x => (V1ServiceAvailability)x.Value);
        }

        private static V1ServiceAvailability ParseServiceAvailability(object obj)
        {
            if (obj is V1ServiceAvailability availability)
            {
                return availability;
            }

            if (obj is not UntypedObject ut)
            {
                return new V1ServiceAvailability();
            }

            var serviceAvailability = new V1ServiceAvailability();

            var value = ut.GetValue();
            foreach (var item in value)
            {
                if (string.Equals(item.Key, nameof(serviceAvailability.Enabled), StringComparison.OrdinalIgnoreCase) && item.Value is UntypedBoolean enabledBool)
                {
                    serviceAvailability.Enabled = enabledBool.GetValue();
                    continue;
                }

                if (string.Equals(item.Key, "Visible", StringComparison.OrdinalIgnoreCase) && item.Value is UntypedBoolean visibleBool)
                {
                    serviceAvailability.Enabled = serviceAvailability.Enabled ?? visibleBool.GetValue();
                    continue;
                }

                if (string.Equals(item.Key, nameof(serviceAvailability.Limit), StringComparison.OrdinalIgnoreCase) && item.Value is UntypedInteger limitInt)
                {
                    serviceAvailability.Limit = limitInt.GetValue();
                    continue;
                }
            }

            return serviceAvailability;
        }
    }
}