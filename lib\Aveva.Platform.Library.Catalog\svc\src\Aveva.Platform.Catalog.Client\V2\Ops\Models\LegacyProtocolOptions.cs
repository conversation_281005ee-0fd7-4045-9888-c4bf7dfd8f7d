// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Ops.Models
{
    /// <summary>
    /// Defines how the legacy Solution and Capability Management (SCM) protocol options correspond to a catalog service. These options enable backward compatibility with systems that use the older SCM protocol.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class LegacyProtocolOptions : IParsable
    {
        /// <summary>Defines the mapping between a service and corresponding legacy Solution and Capability Management (SCM) solution definition. This mapping enables backward compatibility with systems that use the older SCM protocol.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings? Mappings { get; set; }
#nullable restore
#else
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings Mappings { get; set; }
#endif
        /// <summary>The legacy SCM solution definition to which this service corresponds. This identifier links the catalog service to its equivalent representation in the legacy SCM system.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? SolutionDefinition { get; set; }
#nullable restore
#else
        public string SolutionDefinition { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolOptions"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolOptions CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolOptions();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "mappings", n => { Mappings = n.GetObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings>(global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings.CreateFromDiscriminatorValue); } },
                { "solutionDefinition", n => { SolutionDefinition = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolMappings>("mappings", Mappings);
            writer.WriteStringValue("solutionDefinition", SolutionDefinition);
        }
    }
}
#pragma warning restore CS0618
