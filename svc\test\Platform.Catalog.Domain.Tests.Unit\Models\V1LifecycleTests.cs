﻿using Aveva.Platform.Catalog.Domain.Models;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Models;

/// <summary>
/// <see cref="V1LifecycleTests"/> unit tests.
/// </summary>
[Trait("Category", "Unit")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Unit")]
public static class V1LifecycleTests
{
    #region Test Cases

    [Fact]
    public static void V1LifecycleTests_Equals_True()
    {
        // Arrange
        V1Lifecycle first = new V1Lifecycle()
        {
            InstanceMode = V1InstanceMode.Shared,
            Protocol = V1IntegrationProtocol.IntegrationEvent,
            ProviderId = "providerId",
            Trigger = V1Trigger.None,
        };

        V1Lifecycle second = new V1Lifecycle()
        {
            InstanceMode = V1InstanceMode.Shared,
            Protocol = V1IntegrationProtocol.IntegrationEvent,
            ProviderId = "PROVIDERID",
            Trigger = V1Trigger.None,
        };

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1LifecycleTests_Equals_False()
    {
        // Arrange
        V1Lifecycle first = new V1Lifecycle()
        {
            InstanceMode = V1InstanceMode.Isolated,
            Protocol = V1IntegrationProtocol.IntegrationEvent,
            ProviderId = "providerId",
            Trigger = V1Trigger.Catalog,
        };

        V1Lifecycle second = new V1Lifecycle()
        {
            InstanceMode = V1InstanceMode.Shared,
            Protocol = V1IntegrationProtocol.IntegrationEvent,
            ProviderId = "providerId",
            Trigger = V1Trigger.None,
        };

        // Assert
        first.Equals(second).ShouldBeFalse();
    }
    #endregion Test Cases
}