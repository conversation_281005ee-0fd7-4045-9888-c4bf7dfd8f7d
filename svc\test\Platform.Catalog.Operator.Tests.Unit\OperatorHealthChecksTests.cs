﻿using Microsoft.Extensions.DependencyInjection;
using Moq;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Operator.Tests.Unit;

/// <summary>
/// <see cref="OperatorHealthChecksTests"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Operator")]
[Trait("Category", "Unit")]
[Trait("Category", "Operator.Unit")]
[Trait("Tag", "OperatorMonitoringTests")]
public class OperatorHealthChecksTests
{
    #region Test Cases

    [Fact]
    public void AddOperatorHealthChecks_adds_HealthCheckEndpoints()
    {
        // Arrange
        Mock<IServiceCollection> servicesMock = new Mock<IServiceCollection>();

        // Act and Assert
        Should.NotThrow(() =>
        {
            OperatorHealthChecks.AddOperatorHealthChecks(servicesMock.Object);
        });
        servicesMock.Invocations.Count.ShouldBeGreaterThan(0);
    }

    #endregion Test Cases
}