// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Ops.Models
{
    /// <summary>
    /// Represents an external identity that can be associated with a service. External identities provide integration points with third-party authentication and authorization systems.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class ExternalIdentity : IParsable
    {
        /// <summary>The unique identifier for this external identity. This ID is used to reference this specific identity in other operations.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Id { get; set; }
#nullable restore
#else
        public string Id { get; set; }
#endif
        /// <summary>The authorization scopes associated with this external identity. Each scope represents a specific permission or access level granted to the identity.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentityScope?>? Scopes { get; set; }
#nullable restore
#else
        public List<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentityScope?> Scopes { get; set; }
#endif
        /// <summary>Defines the supported external identity providers that can be used for authentication and authorization with services.</summary>
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentityType? Type { get; set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentity"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentity CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentity();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "id", n => { Id = n.GetStringValue(); } },
                { "scopes", n => { Scopes = n.GetCollectionOfEnumValues<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentityScope>()?.AsList(); } },
                { "type", n => { Type = n.GetEnumValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentityType>(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("id", Id);
            writer.WriteCollectionOfEnumValues<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentityScope>("scopes", Scopes);
            writer.WriteEnumValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ExternalIdentityType>("type", Type);
        }
    }
}
#pragma warning restore CS0618
