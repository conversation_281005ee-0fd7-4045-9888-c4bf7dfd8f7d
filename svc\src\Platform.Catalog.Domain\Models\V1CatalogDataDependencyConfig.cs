﻿using System.ComponentModel.DataAnnotations;

namespace Aveva.Platform.Catalog.Domain.Models;

/// <summary>
/// CatalogItemDependencyConfig.
/// </summary>
public class V1CatalogDataDependencyConfig
{
#pragma warning disable CS8618
    /// <summary>
    /// Gets or sets label.
    /// </summary>
    [Required]
    public string Label { get; set; }

    /// <summary>
    /// Gets or sets help.
    /// </summary>
    [Required]
    public string Help { get; set; }

    /// <summary>
    /// Gets or sets required.
    /// </summary>
    public bool Required { get; set; }

    /// <summary>
    /// Gets or sets minimum.
    /// </summary>
    public int? Min { get; set; }

    /// <summary>
    /// Gets or sets maximum.
    /// </summary>
    public int? Max { get; set; }
#pragma warning restore CS8618

    /// <inheritdoc/>
    public override bool Equals(object? obj)
        => obj is V1CatalogDataDependencyConfig item && string.Equals(item.Label, Label, StringComparison.InvariantCultureIgnoreCase)
             && string.Equals(item.Help, Help, StringComparison.InvariantCultureIgnoreCase)
             && item.Required == Required
             && item.Min == Min
             && item.Max == Max;

    /// <inheritdoc/>
    public override int GetHashCode() => base.GetHashCode();
}