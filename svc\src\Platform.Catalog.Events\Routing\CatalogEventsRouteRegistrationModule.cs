﻿using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Asp.Versioning.Builder;
using Asp.Versioning.Conventions;
using Aveva.Platform.Authorization.Sdk;
using Aveva.Platform.Authorization.Sdk.Domain.Models;
using Aveva.Platform.Authorization.Sdk.Domain.Models.Target;
using Aveva.Platform.Catalog.Domain;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Events;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Events.Logging;
using Aveva.Platform.Common.Framework.Abstractions.Results;
using Aveva.Platform.Common.Framework.AspNetCore.Results.Http;
using Aveva.Platform.Common.Framework.AspNetCore.Routing;
using Aveva.Platform.Common.Framework.ExceptionHandling;
using FluentValidation;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

namespace Aveva.Platform.Catalog.Api.Routing;

/// <summary>
/// The route registration module for the Catalog API.
/// </summary>
[ExcludeFromCodeCoverage(Justification = "Not unit testable")]
public class CatalogEventsRouteRegistrationModule : IRouteRegistrationModule
{
    #region Constants
    private const string ServiceEntryCreateEvent = nameof(ServiceEntryCreateEvent);
    private const string ServiceEntryUpdateEvent = nameof(ServiceEntryUpdateEvent);
    private const string ServiceEntryDeleteEvent = nameof(ServiceEntryDeleteEvent);
    #endregion Constants

    #region IRouteRegistrationModule Implementation

    /// <inheritdoc/>
    [ExcludeFromCodeCoverage(Justification = "Not unit testable")]
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        ArgumentNullException.ThrowIfNull(app);

        IVersionedEndpointRouteBuilder catalogServiceEntry = app.NewVersionedApi("Catalog Events");

        // Internal API.
        RouteGroupBuilder catalogIntV1 = catalogServiceEntry
            .MapGroup("internal/v{version:apiVersion}/eventtype")
            .WithOpenApi()
            .WithGroupName("internal")
            .HasApiVersion(1.0);

        // Service entry event routes
        catalogIntV1.MapPost("/create", PostServiceEntryCreateEventAsync)
            .WithName(ServiceEntryCreateEvent)
            .WithSummary("Broadcasts service entry creation events.")
            .WithDescription("Publishes events for new service entries");

        catalogIntV1.MapPost("/update", PostServiceEntryUpdateEventAsync)
            .WithName(ServiceEntryUpdateEvent)
            .WithSummary("Broadcasts service entry update events.")
            .WithDescription("Publishes events for service entry updates, including both old and new states");

        catalogIntV1.MapPost("/delete", PostServiceEntryDeleteEventAsync)
            .WithName(ServiceEntryDeleteEvent)
            .WithSummary("Broadcasts service entry deletion events.")
            .WithDescription("Publishes events for service entry deletions");
    }

    #endregion IRouteRegistrationModule Implementation

    #region Explicitly Implemented Route Handlers

    internal static async Task<Results<AcceptedAtRoute, ForbidHttpResult, BadRequest<ValidationErrorResponse>, ProblemHttpResult>> PostServiceEntryCreateEventAsync(
        [FromBody] V1ServiceEntry catalogServiceEntry,
        ILogger<CatalogEventsRouteRegistrationModule> logger,
        IAuthorizationProvider authorizationProvider,
        IEventPublisherFactory catalogEventPublisherFactory,
        CancellationToken cancellationToken)
    {
        return await HandleServiceEntryEventAsync(
            eventtype: "create",
            newServiceEntry: catalogServiceEntry,
            oldServiceEntry: null,
            logger,
            authorizationProvider,
            catalogEventPublisherFactory,
            ServiceEntryCreateEvent,
            cancellationToken).ConfigureAwait(false);
    }

    internal static async Task<Results<AcceptedAtRoute, ForbidHttpResult, BadRequest<ValidationErrorResponse>, ProblemHttpResult>> PostServiceEntryUpdateEventAsync(
        [FromBody] ServiceEntryUpdateRequest updateRequest,
        ILogger<CatalogEventsRouteRegistrationModule> logger,
        IAuthorizationProvider authorizationProvider,
        IEventPublisherFactory catalogEventPublisherFactory,
        CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(catalogEventPublisherFactory);
        var errorInfo = new ErrorInfo("Invalid request.");
        var errors = new List<Common.Framework.Abstractions.Results.ValidationError>();

        if (updateRequest is null)
        {
            errors.Add(new Common.Framework.Abstractions.Results.ValidationError()
            {
                Detail = "Update request is invalid.",
                FieldName = nameof(updateRequest),
            });

            return Result.ValidationError(errorInfo, errors).ToBadRequest();
        }

        if (updateRequest.OldServiceEntry is null)
        {
            errors.Add(new Common.Framework.Abstractions.Results.ValidationError()
            {
                Detail = "Old service entry is required.",
                FieldName = nameof(updateRequest.OldServiceEntry),
            });
        }

        if (updateRequest.NewServiceEntry is null)
        {
            errors.Add(new Common.Framework.Abstractions.Results.ValidationError()
            {
                Detail = "New service entry is required.",
                FieldName = nameof(updateRequest.NewServiceEntry),
            });
        }

        if (errors.Count != 0)
        {
            return Result.ValidationError(errorInfo, errors!).ToBadRequest();
        }

        var oldServiceEntry = updateRequest.OldServiceEntry;
        var newServiceEntry = updateRequest.NewServiceEntry;

        // Guard against null even though we've validated (to satisfy static analysis)
        if (oldServiceEntry is null || newServiceEntry is null)
        {
            throw new InvalidOperationException("Service entries cannot be null at this point.");
        }

        return await HandleServiceEntryEventAsync(
            eventtype: "update",
            newServiceEntry: newServiceEntry,
            oldServiceEntry: oldServiceEntry,
            logger,
            authorizationProvider,
            catalogEventPublisherFactory,
            ServiceEntryUpdateEvent,
            cancellationToken).ConfigureAwait(false);
    }

    internal static async Task<Results<AcceptedAtRoute, ForbidHttpResult, BadRequest<ValidationErrorResponse>, ProblemHttpResult>> PostServiceEntryDeleteEventAsync(
        [FromBody] V1ServiceEntry catalogServiceEntry,
        ILogger<CatalogEventsRouteRegistrationModule> logger,
        IAuthorizationProvider authorizationProvider,
        IEventPublisherFactory catalogEventPublisherFactory,
        CancellationToken cancellationToken)
    {
        return await HandleServiceEntryEventAsync(
            eventtype: "delete",
            newServiceEntry: catalogServiceEntry,
            oldServiceEntry: null,
            logger,
            authorizationProvider,
            catalogEventPublisherFactory,
            ServiceEntryDeleteEvent,
            cancellationToken).ConfigureAwait(false);
    }

    private static async Task<Results<AcceptedAtRoute, ForbidHttpResult, BadRequest<ValidationErrorResponse>, ProblemHttpResult>> HandleServiceEntryEventAsync(
        string eventtype,
        V1ServiceEntry newServiceEntry,
        V1ServiceEntry? oldServiceEntry,
        ILogger<CatalogEventsRouteRegistrationModule> logger,
        IAuthorizationProvider authorizationProvider,
        IEventPublisherFactory catalogEventPublisherFactory,
        string routeName,
        CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(catalogEventPublisherFactory);
        var errorInfo = new ErrorInfo("Invalid request.");
        var errors = new List<Common.Framework.Abstractions.Results.ValidationError>();

        if (newServiceEntry == null)
        {
            errors.Add(new Common.Framework.Abstractions.Results.ValidationError()
            {
                Detail = "Catalog service entry is invalid.",
                FieldName = nameof(newServiceEntry),
            });
        }

        if (errors.Count != 0)
        {
            return Result.ValidationError(errorInfo, errors!).ToBadRequest();
        }

        using var activity = CatalogTraceSource.EventsTrace.StartActivity("catalogEvents.authorization", ActivityKind.Client);
        {
            var authResult = await authorizationProvider.AuthorizeResourceAsync(ResourcePath.Parse($"eventtype/*"), ResourceAction.Create, AuthorizationTarget.Ops(ServiceId.Parse(CatalogConstants.ServiceId)), new AuthorizationMetadata(), cancellationToken).ConfigureAwait(false);

            if (!authResult.IsSuccess)
            {
                activity?.SetStatus(ActivityStatusCode.Error, "Unauthorized Access");
                logger?.UnauthorizedAccess();
                return TypedResults.Forbid();
            }
        }

        IEventPublisher? catalogEventGenerator = await catalogEventPublisherFactory.GetEventPublisherAsync(eventtype).ConfigureAwait(false);

        if (catalogEventGenerator != null && newServiceEntry != null)
        {
            _ = catalogEventGenerator.PublishEventAsync(newServiceEntry: newServiceEntry, oldServiceEntry: oldServiceEntry);
        }
        else
        {
            return TypedResults.Problem("Unable to determine event generator type.", $"Invalid eventtype = {eventtype}", 400);
        }

        return TypedResults.AcceptedAtRoute(routeName, routeValues: null);
    }

    #endregion Explicitly Implemented Route Handlers
}