import { K6TestBase } from "@platform/performance-libs";
import { opsGetAllServiceBase } from "../../common/opsV2_get_service";

export class CatalogPerformanceTests extends K6TestBase {
    baseTestClass: opsGetAllServiceBase;
    constructor() {
        super();
        this.baseTestClass = new opsGetAllServiceBase();
    }

    defaultScenarioTestIteration(data: any): void {
        this.baseTestClass.defaultScenarioTestIteration(data);
    }

    sharedTestSetup(): any {
        return this.baseTestClass.sharedTestSetup();
    }

    sharedTestTeardown(data: any): void {
        this.baseTestClass.sharedTestTeardown(data);
    }
}
