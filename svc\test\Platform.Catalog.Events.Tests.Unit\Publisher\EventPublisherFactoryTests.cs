﻿using System.Data;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Events.Publisher;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.Common.Framework.Abstractions.Mapping;
using Aveva.Platform.Common.Messaging.EventBus.Abstractions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;

namespace Aveva.Platform.Catalog.Events.Tests.Unit.Publisher
{
    public class EventPublisherFactoryTests
    {
        #region Test Cases
        private Mock<IEventBus> _eventBus = new Mock<IEventBus>();
        private Mock<ICatalogClient> _catalogClient = new Mock<ICatalogClient>();
        private Mock<ILogger<IEventPublisherFactory>> _logger = new Mock<ILogger<IEventPublisherFactory>>();
        private IConfiguration? _configuration;
        private Mock<ITypeMappingService> _mapper = new Mock<ITypeMappingService>();
        private Mock<EventMetrics> _metrics = new Mock<EventMetrics>();

        [Fact]
        public void EventPublisherFactory_Initialization_NullRegion()
        {
            // Arrange
            Environment.SetEnvironmentVariable("region", null);
            Environment.SetEnvironmentVariable("geography", "us");
            _configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();

            // Act & Assert
            Should.Throw<NoNullAllowedException>(() =>
            {
                var eventPublisherFactory = new EventPublisherFactory(_eventBus.Object, _configuration, _catalogClient.Object, _logger.Object, _mapper.Object, _metrics.Object);
            });
        }

        [Fact]
        public void EventPublisherFactory_Initialization_NullGeography()
        {
            // Arrange
            Environment.SetEnvironmentVariable("geography", null);
            Environment.SetEnvironmentVariable("region", "us");
            _configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();

            // Act & Assert
            Should.Throw<NoNullAllowedException>(() =>
            {
                var eventPublisherFactory = new EventPublisherFactory(_eventBus.Object, _configuration, _catalogClient.Object, _logger.Object, _mapper.Object, _metrics.Object);
            });
        }

        [Fact]
        public void EventPublisherFactory_Initialization_Succeeds()
        {
            // Arrange
            Environment.SetEnvironmentVariable("geography", "us");
            Environment.SetEnvironmentVariable("region", "us");
            _configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();

            // Act & Assert
            Should.NotThrow(() =>
            {
                var eventPublisherFactory = new EventPublisherFactory(_eventBus.Object, _configuration, _catalogClient.Object, _logger.Object, _mapper.Object, _metrics.Object);
                eventPublisherFactory.ShouldNotBeNull();
            });
        }

        [Theory]
        [InlineData("create")]
        [InlineData("delete")]
        public async Task EventPublisherFactory_GetEventPublisher_Succeeds(string eventType)
        {
            // Arrange
            Environment.SetEnvironmentVariable("geography", "us");
            Environment.SetEnvironmentVariable("region", "us");
            _configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();
            EventPublisherFactory? eventPublisherFactory = null;

            // Act & Assert
            Should.NotThrow(() =>
            {
                eventPublisherFactory = new EventPublisherFactory(_eventBus.Object, _configuration, _catalogClient.Object, _logger.Object, _mapper.Object, _metrics.Object);
            });

            eventPublisherFactory.ShouldNotBeNull();
            var eventPublisher = await eventPublisherFactory!.GetEventPublisherAsync(eventType).ConfigureAwait(true);

            eventPublisher.ShouldNotBeNull();
        }

        [Theory]
        [InlineData("unknown")]
        public async Task EventPublisherFactory_GetEventPublisher_UnknownEvent_ReturnsNull(string eventType)
        {
            // Arrange
            Environment.SetEnvironmentVariable("geography", "us");
            Environment.SetEnvironmentVariable("region", "us");
            _configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();
            EventPublisherFactory? eventPublisherFactory = null;

            // Act & Assert
            Should.NotThrow(() =>
            {
                eventPublisherFactory = new EventPublisherFactory(_eventBus.Object, _configuration, _catalogClient.Object, _logger.Object, _mapper.Object, _metrics.Object);
            });

            eventPublisherFactory.ShouldNotBeNull();
            var unknownEventPublisher = await eventPublisherFactory.GetEventPublisherAsync(eventType).ConfigureAwait(true);

            unknownEventPublisher.ShouldBeNull();
        }
        #endregion
    }
}