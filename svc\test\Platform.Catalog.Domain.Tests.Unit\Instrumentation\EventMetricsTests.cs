﻿using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Common.Messaging.EventBus.Events.Catalog;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Instrumentation;

/// <summary>
/// <see cref="EventMetricsTests"/> unit tests class.
/// </summary>
[Trait("Category", "Unit")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Unit")]
public static class EventMetricsTests
{
    #region Test Cases

    [Fact]
    public static void EventMetricsTests_Init_Sucessfull()
    {
        // Act & Assert
        Should.NotThrow(() =>
        {
            var eventMetrics = new EventMetrics();
            eventMetrics.ShouldNotBeNull();
        });
    }

    [Fact]
    public static void EventMetricsTests_CatalogServiceAddV1_Sucessfull()
    {
        // Arrange
        var catalogEvent = new CatalogServiceAddV1()
        {
            Geography = "us",
            Id = "serviceId",
            Region = "eastus2",
        };

        // Act & Assert
        Should.NotThrow(() =>
        {
            var eventMetrics = new EventMetrics();
            eventMetrics.RecordEvents(Domain.Models.V1EventPublishStatus.Success, catalogEvent);
            eventMetrics.PublishEventProcessingTime(12, catalogEvent);
        });
    }

    [Fact]
    public static void EventMetricsTests_CatalogServiceUpdateV1_Sucessfull()
    {
        // Arrange
        var catalogEvent = new CatalogServiceUpdateV1()
        {
            Geography = "us",
            Id = "serviceId",
            Region = "eastus2",
        };

        // Act & Assert
        Should.NotThrow(() =>
        {
            var eventMetrics = new EventMetrics();
            eventMetrics.RecordEvents(Domain.Models.V1EventPublishStatus.Success, catalogEvent);
            eventMetrics.PublishEventProcessingTime(12, catalogEvent);
        });
    }

    [Fact]
    public static void EventMetricsTests_CatalogServiceDeleteV1_Sucessfull()
    {
        // Arrange
        var catalogEvent = new CatalogServiceDeleteV1()
        {
            Geography = "us",
            Id = "serviceId",
            Region = "eastus2",
        };

        // Act & Assert
        Should.NotThrow(() =>
        {
            var eventMetrics = new EventMetrics();
            eventMetrics.RecordEvents(Domain.Models.V1EventPublishStatus.Failure, catalogEvent);
            eventMetrics.PublishEventProcessingTime(12, catalogEvent);
        });
    }
    #endregion Test Cases
}