﻿namespace Aveva.Platform.Catalog.Tests.PerformanceSetup.Utilities;

public static class Constants
{
    public const string URI = "https://login.microsoftonline.com/";
    public const string CONNECTOPS = "api://dev-ConnectOps-App/.default";
    public const string AUTHVIRSION = "/oauth2/v2.0/token";
    public const string HTTPPROTOCAL = "https://";
    public const string APPSETTINGSFILE = "appsettings.json";
    public const string AUTHORIZATION = "Authorization";
    public const string BEARER = "Bearer ";
    public const string ACCOUNTNAME = "accountName";
    public const string ACCOUNTGUID = "accountGuid";
    public const string FORMAT = "{0},{1}\n";
    public const string OUTPUTPATH = "./performance_setup.csv";
    public const string DOT = ".";
    public const string CLIENTCREDENTIALS = "client_credentials";
    public const string GRANTTYPE = "grant_type";
    public const string CLIENTIDPARAM = "client_id";
    public const string CLIENTSECRETPARAM = "client_secret";
    public const string SCOPE = "scope";

    // HTTP Verbs
    public const string GET = "GET";
    public const string DELETE = "DELETE";
    public const string POST = "POST";

    // App settings constants
    public const string ENVIRONMENTSETTINGS = "EnvironmentSettings";
    public const string DNS = "EnvironmentSettings:DNS";
    public const string ENVPREFIX = "EnvironmentSettings:envPrefix";
    public const string TENANTID = "EnvironmentSettings:tenantId";
    public const string ACCOUNTSNAME = "AdditionalParams:Accounts";
    public const string CLIENTID = "EnvironmentSettings:clientId";
    public const string CLIENTSECRET = "EnvironmentSettings:clientSecret";

    // AccountStatus
    public const string ACTIVE = "Active";
    public const string DELETEFAILED = "DeleteFailed";
    public const string PURGEFAILED = "PurgeFailed";
    public const string CREATEFAILED = "CreateFailed";
    public const string DELETED = "Deleted";
    public const string PURGED = "Purged";

    // Account Settings
    public const string PERFORMANCESETUP = "PerformanceSetup";
    public const string PERFORMANCETEARDOWN = "PerformanceTeardown";
    public const string ACCOUNTDETAILS = "AccountDetails";

    // NOTIFICATIONS
    public const string HINT = "Invalid command! Use: dotnet run <PerformanceSetup OR PerformanceTeardown OR AccountDetails>";
    public const string ACTIVEACCOUNTS = "ONLY ACTIVE ACCOUNTS WILL BE CONSIDERED! ACCOUNT WITH ANY OTHER STATUS WILL NOT BE CONSIDERED IN THIS PROCESS.";
    public const string ACCOUNTCREATION = "Please hold on! The account {0} creation is in progress.";
    public const string ACCOUNTFETCHING = "Please hold on! The account {0} fetching is in progress!!";
    public const string ACCOUNTDELETING = "Please hold on! The account {0} deletion is in progress!!";
    public const string ACCOUNTCREATIONSUCCESS = "The account {0} created successfully, and it's Id is {1}";
    public const string ACCOUNTCREATIONFAILED = "The account {0} creation failed. Please try again";
    public const string ACCOUNTALREADYEXIST = "The account {0} exist, and it's current status is: {1}";
    public const string NOACCOUNTACTIVE = "No accounts are active. Please try different account.The process is failed with exit code -1";
    public const string ACCOUNTDOESNOTEXIST = "The account {0} doesn't exists.";
    public const string ACCOUNTCURRENTSTATUS = "The account {0} status is {1}.";
    public const string ACCOUNTSTATUSDETAILS = "Name:{0}  Status:{1}  CreatedDate:{2}  ModifiedDate:{3}";
    public const string ACCOUNTDETAILSDOESNOTEXIST = "The account {0} details doesn't exists.";
    public const string ACCOUNTSTATUSDOESNOTEXIST = "No accounts exist.";
    public const string ACCOUNTDELETEDSUCCESSFULLY = "The account {0} has been deleted successfully.";
    public const string NOTAVAILABLE = "Not available.";
    public const string OPERATIONTIMEDOUT = "Operation timed out.";
    public const string DELETEPROGRESS = "Please hold on! The account {0} deletion is in progress. The current account status is {1}.";
    public const string CREATEINPROGRESS = "Please hold on! The account {0} creation is in progress. The current account status is {1}.";
}