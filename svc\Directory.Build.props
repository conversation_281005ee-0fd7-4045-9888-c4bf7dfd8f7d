<Project>
  <!-- 
        Shared build properties file - reference: https://docs.microsoft.com/en-us/visualstudio/msbuild/customize-your-build
        Hierarchical if you add this to child Directory.Build.props files:
        <Import Project="$([MSBuild]::GetPathOfFileAbove($(MSBuildThisFile), $(MSBuildThisFileDirectory)..))" />
  -->

  <!-- https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/configure-language-version e.g., <LangVersion>latest</LangVersion> -->
  <!--
    <PropertyGroup>
        <LangVersion Condition="'$(Language)' == 'C#'">8</LangVersion>
        <LangVersion Condition="'$(Language)' == 'VB'">16</LangVersion>
    </PropertyGroup>
  -->
  <!-- General Metadata Properties -->
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <TargetLatestRuntimePatch>true</TargetLatestRuntimePatch>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
    <NeutralLanguage>en-US</NeutralLanguage>
    <DebugSymbols>true</DebugSymbols>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  
  <PropertyGroup>
    <UseArchitectureNuGets>false</UseArchitectureNuGets>
    <Configurations>Debug;Release;DebugNoCheck</Configurations>
  </PropertyGroup>

  <Import Project="CodeAnalysis.props" />
</Project>