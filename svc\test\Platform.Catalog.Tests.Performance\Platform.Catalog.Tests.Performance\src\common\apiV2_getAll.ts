import { OpsClient, ApiClient } from "@platform/performance-libs";
import { K6TestBase } from '@platform/performance-libs';
import { check, fail } from "k6";
import { Logger } from "@platform/performance-libs";
import { LogLevel } from "@platform/performance-libs";
import { Api as v2CatalogApiModels } from "@platform/aveva.platform.catalog.tsclient";
import { v2ApiGetAllServices } from "../utils/common/servicesHelper";
import { CreateApiClient } from "../utils/common/clientHelper";
import { clientCredentialClient } from "../utils/models/clientCredentialClient";
import { SharedArray } from "k6/data";
import * as Papa from "papaparse";

export class apiGetAllBase extends K6TestBase {
    opsClient: OpsClient;
    apiClient: ApiClient;
    logger: Logger;
    csvData: any;

    constructor() {
        super();
        this.opsClient = new OpsClient();
        this.apiClient = new ApiClient();

        //URL information for tests, constructs base url for both routes and token end point url for api route
        this.opsClient.config.baseUrl = `https://${__ENV.ENVPREFIX}.${__ENV.DNS}`;
        this.apiClient.config.baseUrl = `https://${__ENV.ENVPREFIX}.${__ENV.DNS}`;
        this.opsClient.config.opsTokenEndpoint = `https://login.microsoftonline.com/${__ENV.TENANTID}/oauth2/v2.0/token`;

        this.csvData = new SharedArray("PerfTestData", function () {
            // Load CSV file and parse it using Papa Parse
            //performance_setup.csv is generated from "PerformanceSetup" C# project and contains the seeding details
            //The below example is assuming the csv has data with headers accountName and accountGuid
            return Papa.parse(open(`../../performance_setup.csv`), {
                header: true,
                comments: "#"
            }).data;
        });

        //Using the first account guid received from performance_setup.csv file for api token url
        this.apiClient.config.apiTokenEndpoint = `https://identity.${__ENV.ENVPREFIX}.${__ENV.DNS}/account/${this.csvData[0].accountGuid}/authentication/connect/token`;

        this.logger = new Logger(
            LogLevel[__ENV.LOG_LEVEL as keyof typeof LogLevel]
        );
    }

    sharedTestSetup(): any {
        this.logger.info("Setup of PerformanceServicesTest");

        let clientDetailsObj: clientCredentialClient;

        const dataItems: string[] = [];

        for (let i = 0; i < this.csvData.length; i++) {
            //this.logger.info(`account name is - ${this.csvData[i].accountname} and account guid is - ${this.csvData[i].accountguid}`);
            dataItems.push(this.csvData[i].accountName);
            dataItems.push(this.csvData[i].accountGuid);
        }

        this.logger.info(`Base URL is - ${this.opsClient.config.baseUrl}`);
        this.logger.info(`Ops Token generation URL is - ${this.opsClient.config.opsTokenEndpoint}`);
        this.logger.info(`Api Token generation URL is - ${this.apiClient.config.apiTokenEndpoint}`);

        clientDetailsObj = CreateApiClient(dataItems[0], dataItems[1], this.opsClient, this.logger);
        
        this.apiClient.config.apiClientId = clientDetailsObj.apiClientId;
        this.apiClient.config.apiClientSecret = clientDetailsObj.apiClientSecret;
        this.apiClient.config.scope = "api";
        this.apiClient.authenticate();        

        var data = {
            accountName: dataItems[0],
            accountGuid: dataItems[1],
            clientCredentialClientId: clientDetailsObj.apiClientId,
            clientCredentialClientSecret: clientDetailsObj.apiClientSecret,
            vuOpsBearerObject: this.opsClient.bearerToken,
            vuApiBearerObject: this.apiClient.bearerToken,
            apiClient: this.apiClient,
            opsClient: this.opsClient,
            logger: this.logger
        };

        //getAssignmentId(data);
        //assignIdpToAccount(data);

        //Return object dataholder for default scenario and tear down to use
        return data;
    }
    defaultScenarioTestIteration(data: any): void {
        const lifecycle: v2CatalogApiModels.Lifecycle = {
            trigger: "Account",
            instanceMode: "Shared",
        }
        var emptyArray: string[];
        const responseExpected: v2CatalogApiModels.ServiceResponse = {
            id: "catalog",
            displayName: "Catalog services",
            hostingType: "Environment",
            iconUrl: "https://cdn.aveva.design/icons/svg/outlined/32/content/content--library.svg",
            description: "Catalog services",
            lifecycle: lifecycle,
            tags: emptyArray,
        };

        //construct token end point url for api route
        this.apiClient.config.apiTokenEndpoint = `https://identity.${__ENV.ENVPREFIX}.${__ENV.DNS}/account/${data.accountGuid}/authentication/connect/token`;
        this.apiClient.config.apiClientId = data.clientCredentialClientId;
        this.apiClient.config.apiClientSecret = data.clientCredentialClientSecret;

        try {
            //It is mandatory for this object to be reassigned with the data that is manipulated inside the sharedTestSetup, otherwise the bearer token object shall be undefined and token will not be reused
           this.opsClient.bearerToken = data.vuOpsBearerObject;
           this.apiClient.bearerToken = data.vuApiBearerObject;

           const getResponse = v2ApiGetAllServices(this.apiClient, data.accountGuid, "catalog");

            if (!getResponse) {
                fail(`Attempt to retrieve services failed`);
            }
            for (let entry of getResponse.items) {
                if (entry.id == "catalog") {
                    check(entry, {
                        [`Catalog should have expected id`]: (c) => c.id === responseExpected.id,
                        [`Catalog should have expected displayName`]: (c) => c.displayName === responseExpected.displayName,
                        [`Catalog should have expected hostingType`]: (c) => c.hostingType === responseExpected.hostingType,
                        [`Catalog should have expected iconUrl`]: (c) => c.iconUrl === responseExpected.iconUrl,
                        [`Catalog should have expected description`]: (c) => c.description === responseExpected.description,
                        [`Catalog should have expected lifecycle trigger`]: (c) => c.lifecycle.trigger === responseExpected.lifecycle.trigger,
                        [`Catalog should have expected lifecycle instance mode`]: (c) => c.lifecycle.instanceMode === responseExpected.lifecycle.instanceMode,
                    });
                }
            }
        } catch (e: any) {
            this.logger.error(`There was some exception. ${e}`);
        }
    }

    sharedTestTeardown(_data: any): void {
        this.logger.info(`No necessary teardown for PerformanceServicesTest at ${new Date()}`);
    }
}