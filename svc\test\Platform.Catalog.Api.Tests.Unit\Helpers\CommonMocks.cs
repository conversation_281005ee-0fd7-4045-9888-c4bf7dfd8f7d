﻿using Aveva.Platform.Authorization.Sdk;
using Aveva.Platform.Catalog.Api.Routing;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.ServiceClient.AccountMgmt;
using Aveva.Platform.Common.Framework.Abstractions.Mapping;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;

namespace Aveva.Platform.Catalog.Api.Tests.Unit.Helpers
{
    internal sealed class CommonMocks(
        Mock<IServiceEntryRepository> repositoryMock,
        Mock<ITypeMappingService> dtoTypeMapperMock,
        Mock<HttpContext> contextMock,
        Mock<IAuthorizationProvider> authorizationMock,
        Mock<IAccountMgmtClient> accountMgmtMock,
        Mock<ILogger<CatalogV2RouteRegistrationModule>> loggerMock,
        Mock<CatalogMetrics> metricsMock,
        Mock<IServiceAvailabilityRepository> mockServiceAvailabilityRepository)
    {
        public Mock<IServiceEntryRepository> RepositoryMock => repositoryMock;
        public Mock<ITypeMappingService> DtoTypeMapperMock => dtoTypeMapperMock;
        public Mock<HttpContext> ContextMock => contextMock;
        public Mock<IAuthorizationProvider> AuthorizationMock => authorizationMock;
        public Mock<IAccountMgmtClient> AccountMgmtMock => accountMgmtMock;
        public Mock<ILogger<CatalogV2RouteRegistrationModule>> LoggerMock => loggerMock;
        public Mock<CatalogMetrics> MetricsMock => metricsMock;
        public Mock<IServiceAvailabilityRepository> ServiceAvailabilityRepositoryMock => mockServiceAvailabilityRepository;
    }
}