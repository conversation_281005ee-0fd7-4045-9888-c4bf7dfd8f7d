apiVersion: gateway.aveva.com/v1
kind: ServiceApi
metadata:
  name: catalog-account-scoped-customer-v2
  namespace: '{{ .Release.Namespace }}'
spec:
  apiType: AccountServiceInstance
  scope: Api
  daprId: '{{ include "standard.daprId" (list .Values "api") }}'
  daprIdVersion: 1
  hostingType: Environment
  instanceMode: Shared
  pathPattern: /api/account/{accountId}/v2/{**catch-all}
  namespace: '{{ .Release.Namespace }}'
  serviceId: catalog
  version: v2