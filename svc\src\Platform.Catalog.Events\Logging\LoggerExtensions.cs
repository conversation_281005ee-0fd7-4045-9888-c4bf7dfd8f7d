﻿using System.Diagnostics.CodeAnalysis;

namespace Aveva.Platform.Catalog.Events.Logging;

[ExcludeFromCodeCoverage(Justification = "LoggerMessage logging pattern implementation")]
internal static partial class LoggerExtensions
{
    #region Information
    [LoggerMessage((int)LoggerEvents.CatalogEventServiceStartup, LogLevel.Information, "The catalog event service is starting.")]
    internal static partial void CatalogEventServiceStartup(this ILogger logger);

    [LoggerMessage((int)LoggerEvents.CatalogEventServiceRunning, LogLevel.Information, "The catalog event service is running...")]
    internal static partial void CatalogEventServiceRunning(this ILogger logger);
    [LoggerMessage((int)LoggerEvents.CatalogServicEntryDeleteEvent, LogLevel.Information, "The catalog service entry for serviceId = {serviceId} is DELETED in region {region}.")]
    internal static partial void CatalogServicEntryDeleteEvent(this ILogger logger, string serviceId, string region);

    [LoggerMessage((int)LoggerEvents.CatalogServicEntryUpdateEvent, LogLevel.Information, "The catalog service entry for serviceId = {serviceId} is UPDATED in region {region}.")]
    internal static partial void CatalogServicEntryUpdateEvent(this ILogger logger, string serviceId, string region);

    [LoggerMessage((int)LoggerEvents.CatalogServicEntryAddEvent, LogLevel.Information, "The catalog service entry for serviceId = {serviceId} is CREATED in region {region}.")]
    internal static partial void CatalogServicEntryAddEvent(this ILogger logger, string serviceId, string region);
    #endregion Information

    #region Error
    [LoggerMessage((int)LoggerEvents.UnAuthorizedAccess, LogLevel.Error, "The client is unauthorized")]
    internal static partial void UnauthorizedAccess(this ILogger logger);

    [LoggerMessage((int)LoggerEvents.CatalogServicEntryAddEventError, LogLevel.Critical, "Failed Add event trigger. for serviceId = {serviceId} in region {region}.")]
    internal static partial void CatalogServicEntryAddEventError(this ILogger logger, string serviceId, string region);

    [LoggerMessage((int)LoggerEvents.CatalogEventServiceFailedToStartup, LogLevel.Critical, "The catalog event service failed in startup.")]
    internal static partial void CatalogEventServiceFailedToStartup(this ILogger logger);

    [LoggerMessage((int)LoggerEvents.CatalogEventServiceGeneralException, LogLevel.Error, "Failure in catalog event service.")]
    internal static partial void CatalogEventServiceGeneralException(this ILogger logger);
    #endregion

    #region Warning
    [LoggerMessage((int)LoggerEvents.CatalogServicEntryUpdateEventError, LogLevel.Critical, "Failed Update event trigger. for serviceId = {serviceId} in region {region}.")]
    internal static partial void CatalogServicEntryUpdateEventError(this ILogger logger, string serviceId, string region);

    [LoggerMessage((int)LoggerEvents.CatalogServicEntryDeleteEventError, LogLevel.Critical, "Failed Delete event trigger. for serviceId = {serviceId} in region {region}.")]
    internal static partial void CatalogServicEntryDeleteEventError(this ILogger logger, string serviceId, string region);
    #endregion Warning
}