﻿using System.Net;
using System.Text.Json;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Microsoft.Extensions.Configuration;
using Moq;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.ServiceClient.Tests.Unit.Catalog;

/// <summary>
/// <see cref="CatalogClientTests"/> unit test class.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Unit")]
[Trait("Category", "ServiceClient")]
[Trait("Category", "ServiceClient.Unit")]
[Trait("Tag", "Catalog")]
public class CatalogClientTests : IClassFixture<ServiceClientTestFixture>
{
    private readonly ServiceClientTestFixture _serviceClientTestFixture;
    public CatalogClientTests(ServiceClientTestFixture serviceClientTestFixture)
    {
        _serviceClientTestFixture = serviceClientTestFixture;
    }

    #region Test Cases
    [Fact]
    public void ServiceEntryServiceClient_Create_Succeeds()
    {
        Mock<IConfiguration> configMock = new Mock<IConfiguration>();
        var mockConfSection = new Mock<IConfigurationSection>();
        mockConfSection.SetupGet(m => m[It.Is<string>(s => s.Equals("ServiceReferenceId"))]).Returns("mock value");
        configMock.Setup(x => x.GetSection(It.IsAny<string>())).Returns(mockConfSection.Object);

        using var responseMessage = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
        };

        _serviceClientTestFixture.SetupHttpClient(responseMessage);

        Should.NotThrow(() =>
        {
            CatalogClient catalogClient = new CatalogClient(configMock.Object, _serviceClientTestFixture.HttpClient);
        });
    }

    [Fact]
    public void ServiceEntryServiceClient_CreateNullConfig_Fails()
    {
        Should.Throw<ArgumentNullException>(() =>
        {
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            CatalogClient catalogClient = new CatalogClient(null, _serviceClientTestFixture.HttpClient);
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
        });
    }

    [Fact]
    public void ServiceEntryServiceClient_CreateNullHttpClient_Fails()
    {
        Mock<IConfiguration> configMock = new Mock<IConfiguration>();
        Should.Throw<ArgumentNullException>(() =>
        {
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            CatalogClient catalogClient = new CatalogClient(configMock.Object, null);
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
        });
    }

    [Fact]
    public async Task ServiceEntryServiceClient_GetAllAsync_Succeeds()
    {
        // Arrange
        ServiceResponse oldEntry = new ServiceResponse()
        {
            Id = "1",
            DisplayName = "oldEntry",
            Category = Category.Data,
            HostingType = "Environment",
            Lifecycle = new Lifecycle()
            {
                Trigger = V1Trigger.Account.ToString(),
                Protocol = V1IntegrationProtocol.IntegrationEvent.ToString(),
                ProviderId = "provider",
                InstanceMode = V1InstanceMode.Shared.ToString(),
            },
        };

        Mock<IConfiguration> configMock = new Mock<IConfiguration>();
        var mockConfSection = new Mock<IConfigurationSection>();

        mockConfSection.SetupGet(m => m[It.Is<string>(s => s.Equals("ServiceReferenceId"))]).Returns("mock value");
        configMock
            .Setup(x => x.GetSection(It.IsAny<string>())).Returns(mockConfSection.Object);

        var responseContent = new ServiceCollectionResponse()
        {
            Items = new List<ServiceResponse>() { oldEntry },
        };

        using var responseMessage = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(JsonSerializer.Serialize(responseContent)),
        };

        _serviceClientTestFixture.SetupHttpClient(responseMessage);

        CatalogClient catalogClient = new CatalogClient(configMock.Object, _serviceClientTestFixture.HttpClient);

        // Act
        var result = await catalogClient.GetAllAsync().ConfigureAwait(true);

        // Assert
        foreach (ServiceResponse entry in result!.Items!)
        {
            entry.Id.ShouldBe(oldEntry.Id);
            entry.DisplayName.ShouldBe(oldEntry.DisplayName);
            entry.Dependencies.ShouldBe(oldEntry.Dependencies);
            entry.HostingType.ShouldBe(oldEntry.HostingType);
            entry.Tags.ShouldBe(oldEntry.Tags);
            entry.Lifecycle!.Trigger.ShouldBe(oldEntry.Lifecycle.Trigger);
            entry.Lifecycle.Protocol.ShouldBe(oldEntry.Lifecycle.Protocol);
            entry.Lifecycle.ProviderId.ShouldBe(oldEntry.Lifecycle.ProviderId);
            entry.Lifecycle.InstanceMode.ShouldBe(oldEntry.Lifecycle.InstanceMode);
        }
    }

    [Fact]
    public async Task ServiceEntryServiceClient_GetAllAsync_ContentNull_ReturnsNull()
    {
        // Arrange
        Mock<IConfiguration> configMock = new Mock<IConfiguration>();
        var mockConfSection = new Mock<IConfigurationSection>();

        mockConfSection.SetupGet(m => m[It.Is<string>(s => s.Equals("ServiceReferenceId"))]).Returns("mock value");
        configMock
            .Setup(x => x.GetSection(It.IsAny<string>())).Returns(mockConfSection.Object);

        ServiceCollectionResponse? responseContent = null;

        using var responseMessage = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(JsonSerializer.Serialize(responseContent)),
        };

        _serviceClientTestFixture.SetupHttpClient(responseMessage);

        CatalogClient catalogClient = new CatalogClient(configMock.Object, _serviceClientTestFixture.HttpClient);

        // Act
        var result = await catalogClient.GetAllAsync().ConfigureAwait(true);

        // Assert
        result.ShouldBe(null);
    }

    [Fact]
    public async Task ServiceEntryServiceClient_GetAllAsync_ForNotFound_ReturnsNull()
    {
        // Arrange
        Mock<IConfiguration> configMock = new Mock<IConfiguration>();
        var mockConfSection = new Mock<IConfigurationSection>();

        mockConfSection.SetupGet(m => m[It.Is<string>(s => s.Equals("ServiceReferenceId"))]).Returns("mock value");
        configMock.Setup(x => x.GetSection(It.IsAny<string>())).Returns(mockConfSection.Object);

        using var responseMessage = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.NotFound,
        };

        _serviceClientTestFixture.SetupHttpClient(responseMessage);

        CatalogClient catalogClient = new CatalogClient(configMock.Object, _serviceClientTestFixture.HttpClient);

        // Act
        var result = await catalogClient.GetAllAsync().ConfigureAwait(true);

        // Assert
        result.ShouldBe(null);
    }
    #endregion Test Cases
}