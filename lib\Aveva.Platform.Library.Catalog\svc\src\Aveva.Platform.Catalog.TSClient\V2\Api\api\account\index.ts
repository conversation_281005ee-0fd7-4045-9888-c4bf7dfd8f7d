/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { type WithAccountItemRequestBuilder, WithAccountItemRequestBuilderNavigationMetadata } from './item/index.js';
// @ts-ignore
import { type BaseRequestBuilder, type KeysToExcludeForNavigationMetadata, type NavigationMetadata } from '@microsoft/kiota-abstractions';

/**
 * Builds and executes requests for operations under /api/account
 */
export interface AccountRequestBuilder extends BaseRequestBuilder<AccountRequestBuilder> {
    /**
     * Gets an item from the Aveva.Platform.Catalog.Client.V2.Api.api.account.item collection
     * @param accountId The unique identifier of the account. This ID represents the specific account that will access or own the resources being managed.
     * @returns {WithAccountItemRequestBuilder}
     */
     byAccountId(accountId: string) : WithAccountItemRequestBuilder;
}
/**
 * Uri template for the request builder.
 */
export const AccountRequestBuilderUriTemplate = "{+baseurl}/api/account";
/**
 * Metadata for all the navigation properties in the request builder.
 */
export const AccountRequestBuilderNavigationMetadata: Record<Exclude<keyof AccountRequestBuilder, KeysToExcludeForNavigationMetadata>, NavigationMetadata> = {
    byAccountId: {
        navigationMetadata: WithAccountItemRequestBuilderNavigationMetadata,
        pathParametersMappings: ["accountId"],
    },
};
/* tslint:enable */
/* eslint-enable */
