﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Aveva.Platform.Catalog.Domain.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Aveva.Platform.Catalog.Domain.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The catalog was not created.
        /// </summary>
        internal static string CreateCatalogFailedCreateError {
            get {
                return ResourceManager.GetString("CreateCatalogFailedCreateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An existing catalog with the specified identifier already exists.
        /// </summary>
        internal static string CreateCatalogFailedCreateReason {
            get {
                return ResourceManager.GetString("CreateCatalogFailedCreateReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specify an identifier that is not already in use and try your request again.
        /// </summary>
        internal static string CreateCatalogFailedCreateResolution {
            get {
                return ResourceManager.GetString("CreateCatalogFailedCreateResolution", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The catalog was not created.
        /// </summary>
        internal static string CreateOrUpdateCatalogFailedCreateError {
            get {
                return ResourceManager.GetString("CreateOrUpdateCatalogFailedCreateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A catalog with the specified identifier may have been created at the same time in another session or another conflict has occurred..
        /// </summary>
        internal static string CreateOrUpdateCatalogFailedCreateReason {
            get {
                return ResourceManager.GetString("CreateOrUpdateCatalogFailedCreateReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specify an identifier for an existing catalog if you wish to update it. Otherwise, specify an identifier that is not already in use and try your request again..
        /// </summary>
        internal static string CreateOrUpdateCatalogFailedCreateResolution {
            get {
                return ResourceManager.GetString("CreateOrUpdateCatalogFailedCreateResolution", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The catalog was not updated.
        /// </summary>
        internal static string CreateOrUpdateCatalogFailedUpdateError {
            get {
                return ResourceManager.GetString("CreateOrUpdateCatalogFailedUpdateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The specified catalog details may conflict with the existing catalog details, preventing update of the existing catalog..
        /// </summary>
        internal static string CreateOrUpdateCatalogFailedUpdateReason {
            get {
                return ResourceManager.GetString("CreateOrUpdateCatalogFailedUpdateReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verify that all required catalog details have been included and are in the correct format. Then try your request again.
        /// </summary>
        internal static string CreateOrUpdateCatalogFailedUpdateResolution {
            get {
                return ResourceManager.GetString("CreateOrUpdateCatalogFailedUpdateResolution", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorization failed for this request.
        /// </summary>
        internal static string RequestFailedAuthorizationError {
            get {
                return ResourceManager.GetString("RequestFailedAuthorizationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The identity making this request was not authorized to reach this resource.
        /// </summary>
        internal static string RequestFailedAuthorizationReason {
            get {
                return ResourceManager.GetString("RequestFailedAuthorizationReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make sure the token is authenticated and the caller is authorized to access this resource.
        /// </summary>
        internal static string RequestFailedAuthorizationResolution {
            get {
                return ResourceManager.GetString("RequestFailedAuthorizationResolution", resourceCulture);
            }
        }
    }
}
