﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;

namespace Aveva.Platform.Catalog.Domain.Queries;

/// <summary>
/// ServiceEntryQuery.
/// </summary>
public class ServiceEntryQuery
{
    /// <summary>
    /// Gets or sets accountId.
    /// </summary>
    public string? AccountId { get; set; }

    /// <summary>
    /// Gets or sets category.
    /// </summary>
    public Category? Category { get; set; }
}