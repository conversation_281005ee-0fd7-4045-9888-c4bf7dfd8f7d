﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;

/// <summary>
/// Defines service applications, where each additional property represents an application. A representation of an application that belongs to a service in the catalog, containing information like name and access URLs.
/// </summary>
public class Application
{
    /// <summary>
    /// Initializes a new instance of the <see cref="Application"/> class.
    /// </summary>
    public Application() // Required by EF
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="Application"/> class with a specific name.
    /// </summary>
    public Application(string name)
    {
        Name = name;
    }

    /// <summary>
    /// The name of the application. The unique name that identifies this application within a service. This name is used for display and reference purposes.
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Optional URLs keyed to launch for specific valid geographies and/or default URLs to launch if a geography is not identified. A dictionary of URLs where keys represent geography codes and values are the corresponding application launch URLs. If any URLs are provided, a `default` key must be included to specify the URL to use when no specific geography match is found.
    /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
    public IDictionary<string, string>? Urls { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        if (obj is not Application item)
        {
            return false;
        }

        var result = string.Equals(item.Name, Name, StringComparison.InvariantCultureIgnoreCase);

        if (Urls == null && item.Urls != null)
        {
            if (item.Urls.Count > 0)
            {
                result = result && false;
            }
        }

        if (Urls != null && item.Urls == null)
        {
            if (Urls.Count > 0)
            {
                result = result && false;
            }
        }

        if (Urls != null && item.Urls != null)
        {
            result = result
                     && (Urls.Count == item.Urls.Count)
                     && (Urls.Except(item.Urls).Any() == false)
                     && (item.Urls.Except(Urls).Any() == false);
        }

        return result;
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
}