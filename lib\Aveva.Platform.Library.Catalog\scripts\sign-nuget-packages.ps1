param(
    [Parameter(Mandatory=$true)]
    [string]
    $NugetPackagesLocation,

    [Parameter(Mandatory=$true)]
    [string]
    $SourcesDirectory
)

# Get Websign Location
$websignInclude = "WebSign.exe" 
$webSign = Get-ChildItem -Path "$SourcesDirectory/packages/OSIsoft.WebSign/" -Include $websignInclude -Recurse

# Get all the .nupkg files to sign
Get-ChildItem "$NugetPackagesLocation\OSIsoft*.nupkg" | ForEach-Object {
  & $websign[0].FullName /HOST:oakcwss-2.osisoft.int /Retry:3 /ST /R $_.FullName
}

# Verify all the .nupkg files are signed.
Get-ChildItem "$NugetPackagesLocation\OSIsoft*.nupkg" | ForEach-Object {
  $acs = nuget verify -all $_.FullName
  Write-Output $acs
}