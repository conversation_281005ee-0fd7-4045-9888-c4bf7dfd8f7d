// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Api.Models
{
    /// <summary>
    /// Defines how service instances are created, managed, and terminated throughout their lifecycle. This configuration determines the provisioning approach, resource allocation strategy, and integration patterns.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class Lifecycle : IParsable
    {
        /// <summary>Indicates whether the service requires manual input from AVEVA operators.</summary>
        public bool? FulfillmentRequired { get; set; }
        /// <summary>Defines the resource allocation strategy for service instances. Options include:            `Shared` (multiple customers share the same underlying resources with logical separation),            `Isolated` (dedicated resources are provisioned specifically for each customer instance).</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? InstanceMode { get; set; }
#nullable restore
#else
        public string InstanceMode { get; set; }
#endif
        /// <summary>Specifies the communication pattern used for lifecycle events. The default value is `IntegrationEvent`, which implements a publish-subscribe pattern where instance management publishes lifecycle events and waits for acknowledgment from the service provider.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Protocol { get; set; }
#nullable restore
#else
        public string Protocol { get; set; }
#endif
        /// <summary>Contains protocol-specific configuration options, including any mappings needed for backward compatibility with legacy systems and to provide support for webhooks.            These options control how the lifecycle protocol interacts with other systems during provisioning operations.            For Legacy protocol, this will be a &lt;see cref=&quot;T:Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2.LegacyProtocolOptions&quot; /&gt;.            For Webhook protocol, this will be a &lt;see cref=&quot;T:Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2.WebhookProtocolOptions&quot; /&gt;.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::Aveva.Platform.Catalog.Client.V2.Api.Models.ProtocolOptions? ProtocolOptions { get; set; }
#nullable restore
#else
        public global::Aveva.Platform.Catalog.Client.V2.Api.Models.ProtocolOptions ProtocolOptions { get; set; }
#endif
        /// <summary>Identifies the service provider responsible for handling lifecycle events. The provider implements the service-specific logic needed to create, configure, and manage service instances according to customer requirements.</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? ProviderId { get; set; }
#nullable restore
#else
        public string ProviderId { get; set; }
#endif
        /// <summary>Determines when and how service instances are provisioned. Valid values include:            `None` (service does not require provisioning),            `Account` (automatically provisioned when an account is created),            `Catalog` (provisioned on-demand by customer request).</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Trigger { get; set; }
#nullable restore
#else
        public string Trigger { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Models.Lifecycle"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::Aveva.Platform.Catalog.Client.V2.Api.Models.Lifecycle CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::Aveva.Platform.Catalog.Client.V2.Api.Models.Lifecycle();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "fulfillmentRequired", n => { FulfillmentRequired = n.GetBoolValue(); } },
                { "instanceMode", n => { InstanceMode = n.GetStringValue(); } },
                { "protocol", n => { Protocol = n.GetStringValue(); } },
                { "protocolOptions", n => { ProtocolOptions = n.GetObjectValue<global::Aveva.Platform.Catalog.Client.V2.Api.Models.ProtocolOptions>(global::Aveva.Platform.Catalog.Client.V2.Api.Models.ProtocolOptions.CreateFromDiscriminatorValue); } },
                { "providerId", n => { ProviderId = n.GetStringValue(); } },
                { "trigger", n => { Trigger = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteBoolValue("fulfillmentRequired", FulfillmentRequired);
            writer.WriteStringValue("instanceMode", InstanceMode);
            writer.WriteStringValue("protocol", Protocol);
            writer.WriteObjectValue<global::Aveva.Platform.Catalog.Client.V2.Api.Models.ProtocolOptions>("protocolOptions", ProtocolOptions);
            writer.WriteStringValue("providerId", ProviderId);
            writer.WriteStringValue("trigger", Trigger);
        }
    }
}
#pragma warning restore CS0618
