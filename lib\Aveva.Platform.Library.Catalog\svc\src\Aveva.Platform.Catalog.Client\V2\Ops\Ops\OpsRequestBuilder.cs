// <auto-generated/>
#pragma warning disable CS0618
using Aveva.Platform.Catalog.Client.V2.Ops.Ops.Catalog;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Ops.Ops
{
    /// <summary>
    /// Builds and executes requests for operations under \ops
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class OpsRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The catalog property</summary>
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Ops.Catalog.CatalogRequestBuilder Catalog
        {
            get => new global::Aveva.Platform.Catalog.Client.V2.Ops.Ops.Catalog.CatalogRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Ops.OpsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public OpsRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/ops", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Ops.OpsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public OpsRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/ops", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
