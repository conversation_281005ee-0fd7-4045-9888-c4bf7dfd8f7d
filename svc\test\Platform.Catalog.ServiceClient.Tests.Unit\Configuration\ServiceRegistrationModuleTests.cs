﻿using Aveva.Platform.Authentication.Sdk.Server.Extensions;
using Aveva.Platform.Catalog.Domain;
using Aveva.Platform.Catalog.ServiceClient.AccountMgmt;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.Catalog.ServiceClient.Configuration;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.ServiceClient.Tests.Unit.Configuration;

/// <summary>
/// <see cref="ServiceRegistrationModuleTests"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "ServiceClient")]
[Trait("Category", "Unit")]
[Trait("Category", "ServiceClient.Unit")]
[Trait("Tag", "Configuration")]
public class ServiceRegistrationModuleTests
{
    #region Test Cases

    [Fact]
    public void ServiceRegistrationModule_AddServices_Succeeds()
    {
        // Arrange
        var catalogSettings = new Dictionary<string, string>()
        {
            { "Apis:AccountMgmtService:serviceIdentityId", "accountmgmt" },
            { "Apis:CatalogService:serviceIdentityId", CatalogConstants.ServiceId },
            { "Apis:CatalogEventsService:serviceIdentityId", CatalogConstants.ServiceId },
            { "Apis:CatalogService:BaseServiceUrl", "http://catalog.platform-catalog" },
            { "Apis:CatalogEventsService:BaseServiceUrl", "http://catalog.platform-catalog" },
            { "Apis:InstanceMgmtService:serviceIdentityId", "instancemgmt" },
            { "Authentication:serviceId", CatalogConstants.ServiceId },
            { "Authentication:IdentityDnsZoneName", "test.aveva.connect.com" },
        };

        var configuration = new ConfigurationBuilder().AddInMemoryCollection(catalogSettings!).Build();

        ServiceRegistrationModule serviceRegistrationModule = new ServiceRegistrationModule(configuration);
        ServiceCollection services = new ServiceCollection();
        services.AddPlatformAuthentication(options =>
        {
            Assert.NotNull(options);
            options.IdentityDnsZoneName = "https://identityurl";
            options.ServiceId = CatalogConstants.ServiceId;
        });

        services.AddDaprClient();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddSingleton<IWebHostEnvironment>(new Mock<IWebHostEnvironment>().Object);

        // Act
        Should.NotThrow(() =>
        {
            serviceRegistrationModule.AddServices(services);
        });
        var provider = services.BuildServiceProvider();

        // Assert
        provider.GetRequiredService<IAccountMgmtClient>().ShouldNotBeNull();
        provider.GetRequiredService<ICatalogClient>().ShouldNotBeNull();
        provider.GetRequiredService<ICatalogEventClient>().ShouldNotBeNull();
        provider.GetRequiredService<IInstanceMgmtClient>().ShouldNotBeNull();
    }

    [Fact]
    public void ServiceRegistrationModule_Null_ConstructorParameter_Throws()
    {
        // Arrange
        IConfiguration? configuration = null;

        // Act & Assert
        Should.Throw<ArgumentNullException>(() =>
        {
            ServiceRegistrationModule serviceRegistrationModule = new ServiceRegistrationModule(configuration!);
        });
    }
    #endregion Test Cases
}