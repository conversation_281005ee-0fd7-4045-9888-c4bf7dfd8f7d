// <auto-generated/>
#pragma warning disable CS0618
using Aveva.Platform.Catalog.Client.V2.Api.Api.Account;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Api.Api
{
    /// <summary>
    /// Builds and executes requests for operations under \api
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class ApiRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The account property</summary>
        public global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.AccountRequestBuilder Account
        {
            get => new global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.AccountRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.ApiRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public ApiRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.ApiRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public ApiRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
