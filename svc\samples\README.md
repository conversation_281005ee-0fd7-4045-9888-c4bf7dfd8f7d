# cloud platform Catalog REST API Samples

TODO: Concise bi-line describing samples

## Introduction

TODO: Give a short introduction of the samples.

## Getting Started

TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:

1. Installation process
2. Software dependencies
3. Latest releases
4. API references

## Build and Test

TODO: Describe and show how to build your code and run the tests.
To build the cloud platform Catalog samples solution:
1. Clone the repository to your local computer
2. Ensure your development machine has the correct version of the .NET Core SDK installed. This can be accomplished by typing 'dotnet --info' at the command line without the quotes and confirming your SDK version is equal to or greater than the TargetFramework specified in the host, unit, and integration test .csproj project files, currently net8.0.  
3. Use an IDE, Visual Studio or Visual Studio Code to build, run and debug the samples codebase
4. Alternatively, users can build and run the applications directly using the .NET Core CLI toolset (e.g., dotnet build platformCatalog.sln, dotnet run, etc.)

To run tests:
1. Tests are visible in Visual Studio in the Test Explorer window. Access it in Visual Studio from Test > Windows > Test Explorer
2. Build the solution and view the tests in Test Explorer
3. Choose a test or set of tests to run or debug
4. Alternatively, users can run tests using the .NET Core CLI (e.g., dotnet test [--|\<PROJECT\>])

## Contribute

TODO: (Optional) Explain how other users and developers can contribute to make your code better.

If you want to learn more about creating good readme files then refer the following [guidelines](https://www.visualstudio.com/en-us/docs/git/create-a-readme). You can also seek inspiration from the below readme files:

- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)