﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;

/// <summary>
/// Defines provisioning availability and constraints for a service. This resource contains information about how a service can be discovered and provisioned to an account.
/// </summary>
public class ServiceAvailability
{
    /// <summary>
    /// Indicates whether the service is visible in the service catalog (`true`) or hidden (`false`).
    /// When set to `true`, the service appears in catalog listings and can be discovered by users.
    /// When set to `false`, the service is hidden from catalog views but may still be available through direct access.
    /// </summary>
    public bool? Enabled { get; set; }

    /// <summary>
    /// Indicates whether an approval workflow is required (`true`) or if the service can be provisioned immediately (`false`).
    /// When set to `true`, service provisioning must go through a request and approval process.
    /// When set to `false`, accounts can provision the service immediately without approval.
    /// </summary>
    [Obsolete("This property is deprecated and will be removed in a future version. Use 'Enabled' instead.")]
    public bool? Visible { get; set; }

    /// <summary>
    /// Specifies the maximum number of instances an account can have of this service.
    /// A value of `null` indicates the default limit (10) is applied.
    /// A value of `0` indicates the service cannot be provisioned.
    /// Any positive integer represents the maximum number of instances allowed.
    /// </summary>
    public int? Limit { get; set; }

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        if (obj == null)
        {
            return false;
        }

        return obj is ServiceAvailability item
            && ((item.Enabled == null && Enabled == null) || (item.Enabled != null && item.Enabled.Equals(Enabled)))
            && ((item.Limit == null && Limit == null) || (item.Limit != null && item.Limit.Equals(Limit)));
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
}