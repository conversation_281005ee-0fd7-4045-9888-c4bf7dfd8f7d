﻿using System.Diagnostics.CodeAnalysis;
using Asp.Versioning.ApiExplorer;
using Aveva.Platform.Authentication.Sdk.Server.Extensions;
using Aveva.Platform.Catalog.Domain.Serialization;
using Aveva.Platform.Catalog.Logging;
using Aveva.Platform.Common.Abstractions.Extensions;
using Aveva.Platform.Common.Framework.Abstractions.Extensions;
using Aveva.Platform.Common.Framework.AspNetCore.Configuration;
using Aveva.Platform.Common.Framework.AspNetCore.Routing.Extensions;
using Aveva.Platform.Common.Framework.ExceptionHandling.Extensions;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;

// Register and configure dependencies.
WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

// Register dependencies defined in service registration modules.
builder.Services.AddServicesFromModules(builder.Configuration);

builder.Services.AddDaprClient(clientBuilder => clientBuilder.UseJsonSerializationOptions(JsonSerializationOptions.Options));

builder.Services.AddControllers().AddJsonOptions(options => options.JsonSerializerOptions.ConfigureDefaults(enumsAsStrings: true));

builder.Services.AddHealthChecks()
    .AddCheck("startup", () => HealthCheckResult.Healthy());

// Finalize dependency registrations and build application.
OnBeforeWebApplicationBuilderBuild(builder);
WebApplication app = builder.Build();

// At this point we can begin logging application start up progress.
ILogger<Program> logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.ApplicationStartupApplyingConfiguration();
ApplicationOptions applicationOptions = app.Services.GetRequiredService<IOptions<ApplicationOptions>>().Value;
logger.ApplicationStartupConfigurationReport(ApplicationOptions.DefaultConfigurationSectionName, applicationOptions.ToString());

// Register and configure middleware.
app.UseExceptionMiddleware(app.Environment.IsDevelopment(), new EventId((int)LoggerEvents.ApplicationGeneralException, nameof(LoggerEvents.ApplicationGeneralException)));

app.UseHttpsRedirection();
app.MapContainerHealthChecks();

// Configure API route endpoints.
app.AddRoutesFromModules();

// Configure OpenAPI specifications.
// Catalog: Relocate this API/Swagger enablement logic to a library extension method to remove this boilerplate code.
app.UseSwagger();

app.UsePlatformAuthentication();
app.UseAuthorization();
if (applicationOptions.EnableSwaggerUI)
{
    app.UseSwaggerUI(options =>
    {
        IReadOnlyList<ApiVersionDescription> descriptions = app.DescribeApiVersions();

        foreach (ApiVersionDescription description in descriptions)
        {
            string url = $"/swagger/{description.GroupName}/swagger.json";
            string name = $"{applicationOptions.SwaggerServiceTitle} {description.GroupName}/{description.ApiVersion}";
            options.SwaggerEndpoint(url, name);
            options.EnableTryItOutByDefault();
            options.DisplayRequestDuration();
        }
    });
}

// Finalize web application configuration prior to running.
OnBeforeWebApplicationRun(app);
logger.ApplicationStartupCompletedApplyingConfiguration();

// Log start up of our application and run it.
logger.ApplicationRunning();
await app.RunAsync().ConfigureAwait(false);
return 0;

/// <summary>
/// Entry point and composition root for the application.
/// </summary>
[ExcludeFromCodeCoverage]
public sealed partial class Program
{
    #region Private Methods

    private static void OnBeforeWebApplicationBuilderBuild(WebApplicationBuilder builder)
    {
        ArgumentNullException.ThrowIfNull(builder);
    }

    private static void OnBeforeWebApplicationRun(WebApplication app)
    {
        ArgumentNullException.ThrowIfNull(app);
    }
    #endregion Private Methods
}