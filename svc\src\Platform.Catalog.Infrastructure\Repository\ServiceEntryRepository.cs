﻿using System.Diagnostics.CodeAnalysis;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Domain.Queries;
using Aveva.Platform.Catalog.Infrastructure.Facades;
using Microsoft.Extensions.Caching.Memory;

namespace Aveva.Platform.Catalog.Infrastructure.Repository
{
    /// <summary>
    /// Cached Repository.
    /// </summary>
    public class ServiceEntryRepository(
        IKubernetesFacade k8s,
        IMemoryCache memoryCache,
        CatalogMetrics metrics) : IServiceEntryRepository
    {
        private const string ServiceEntriesCacheKey = "ServiceEntries";
        private static readonly SemaphoreSlim _lock = new(1, 1);

        /// <summary>
        /// Gets all the ServiceEntries from Catalog memory cache.
        /// </summary>
        /// <returns>List of catalog entries.</returns>
        public async Task<IEnumerable<V1ServiceEntry>> QueryAsync(ServiceEntryQuery query)
        {
            ArgumentNullException.ThrowIfNull(query);

            var serviceEntries = await GetAllServiceEntriesAsync().ConfigureAwait(false);
            return serviceEntries
                .Where(x => query.Category == null || string.Equals(x.Category.ToString(), query.Category.ToString(), StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Gets the ServiceEntry for a microservice.
        /// </summary>
        /// <returns>Service entry of a microservice.</returns>
        public async Task<V1ServiceEntry?> GetByIdAsync(string id)
        {
            var serviceEntries = await GetAllServiceEntriesAsync().ConfigureAwait(false);
            var serviceEntry = serviceEntries.ToList().Find(x => x.Id.Equals(id, StringComparison.OrdinalIgnoreCase));
            return serviceEntry;
        }

        /// <summary>
        /// Clears the cache from Catalog service memory.
        /// </summary>
        public void ClearCache()
        {
            memoryCache.Remove(ServiceEntriesCacheKey);
            if (memoryCache is MemoryCache impl)
            {
                impl.Compact(1);
            }

            Task.Run(GetAllServiceEntriesAsync);
        }

        /// <summary>
        /// Memory cache expiration callback function to rebuild the cache.
        /// </summary>
        /// <param name="key">Expired cache key.</param>
        /// <param name="value">Cache key value.</param>
        /// <param name="reason">Reason for evicting the cache from memory.</param>
        /// <param name="state">Current state.</param>
        private void RecreateMemoryCache(object key, object? value, EvictionReason reason, object? state)
        {
            Task.Run(GetAllServiceEntriesAsync);
        }

        private async Task<List<V1ServiceEntry>> GetAllServiceEntriesAsync()
        {
            var serviceEntries = memoryCache.Get<List<V1ServiceEntry>>(ServiceEntriesCacheKey);
            if (serviceEntries == null)
            {
                await _lock.WaitAsync().ConfigureAwait(false);
                try
                {
                    serviceEntries = memoryCache.Get<List<V1ServiceEntry>>(ServiceEntriesCacheKey);
                    if (serviceEntries == null)
                    {
                        serviceEntries = await GetServiceEntriesFromK8sAsync().ConfigureAwait(false);
                        if (serviceEntries != null)
                        {
                            memoryCache.Set(
                                ServiceEntriesCacheKey,
                                serviceEntries,
                                new MemoryCacheEntryOptions()
                                    .SetPriority(CacheItemPriority.Normal)
                                    .SetAbsoluteExpiration(TimeSpan.FromMinutes(120))
                                    .RegisterPostEvictionCallback(callback: RecreateMemoryCache));
                        }
                    }
                }
                finally
                {
                    _lock.Release();
                }
            }

            metrics.RecordServiceEntryCount(serviceEntries?.Count ?? 0);
            return serviceEntries ?? [];
        }

        [SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "Cached Catalog repository should just logs the error while building the cache.", Scope = "member", Target = "~M:Aveva.Platform.Catalog.Infrastructure.Repository.CachedCatalogRepository.BuildServiceEntriesCacheFromK8sAsync()~System.Threading.Tasks.Task{System.Collections.Generic.IEnumerable{Aveva.Platform.Catalog.Domain.Models.V1ServiceEntry}}")]
        private async Task<List<V1ServiceEntry>?> GetServiceEntriesFromK8sAsync()
        {
            using var activity = CatalogTraceSource.ApiTrace.StartActivity("catalog.serviceentry.build_cache", System.Diagnostics.ActivityKind.Internal);

            try
            {
                var serviceEntryList = await k8s.ListServiceEntriesAsync().ConfigureAwait(false);
                return serviceEntryList.Items != null
                    ? serviceEntryList.Items.Select(s => s.Spec!).ToList()
                    : null;
            }
            catch (Exception ex)
            {
                activity?.AddException(ex);
            }

            return null;
        }
    }
}