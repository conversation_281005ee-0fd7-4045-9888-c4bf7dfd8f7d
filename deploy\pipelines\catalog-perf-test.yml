trigger:
  none

# Currently scheduled perf tests runs are not supported by the perf testing environment
# schedules:
#     - cron: "0 0-4/2,12-22/2 * * 1-5"
#       displayName: Performance Testing Bihourly Run against Eucliddev
#       branches:
#         include:
#         - main
#       always: true

resources:
  repositories:
    - repository: 'platform-deployment'
      type: git
      name: 'platform-deployment'
      ref: refs/heads/main
    - repository: 'platform-deployment.perftest'
      type: git
      name: 'platform-deployment.perftest'
      ref: refs/heads/main
    - repository: 'platform-performance'
      type: git
      name: 'platform-performance'
      ref: refs/heads/main

parameters:
- name: artifactName 
  displayName: 'Artifact where performance tests are transpiled and added through CI build'
  type: string
  default: 'platform-catalog'
- name: artifactVersion
  displayName: 'Artifact Version (specify * for latest or version number)'
  type: string
  default: '*'
- name: performanceTestName
  displayName: 'Performance Test Name (as defined in the CI build performanceTests parameter)'
  type: string
  default: 'CatalogPerformanceTests'
- name: serviceFilter
  displayName: 'Filter for service directory to use (relative to the "tests" directory)'
  type: string
  default: 'V2Tests1X'
  values:
  - 'V2Tests1X'
  - 'V2Tests10X'
  - 'V2Services'
  - 'CatalogTests1X'
  - 'CatalogTests10X'
  - 'Services'
- name: testFilter
  displayName: 'Filter for tests to run (relative to the service filter, specify /** to run all tests in the serviceFilter ordered by .order file)'
  type: string
  default: '/**'
- name: envPrefix
  displayName: 'Environment Prefix/ Cluster Environment Name'
  type: string
  default: 'perfdev'
- name: DNS
  displayName: 'DNS'
  type: string
  default: 'platform.capdev-connect.aveva.com'  
- name: numberOfConcurrentK6Instances
  displayName: 'Number of Test Engine Instances'
  type: number
  default: 1
- name: useK6CommandLineArgs
  displayName: 'Use Command Line Args for K6'
  type: boolean
  default: false
- name: k6CommandLineArgs
  displayName: 'Command line args for k6'
  type: string
  default: ' '
- name: useAdditionalEnvironmentVariables
  displayName: Use the additional environment variables
  type: boolean
  default: false
- name: additionalEnvironmentVariables
  displayName: 'Set any additional environment variables'
  type: object
  default:
    example: 'value'
- name: publishResultsToFile
  displayName: 'Publish report'
  type: boolean
  default: true
- name: enableHTTPDebugLogging
  displayName: 'Enable HTTP Debug Logging'
  type: boolean
  default: false
- name: disableK6Thresholds
  displayName: 'Disable All K6 Thresholds'
  type: boolean
  default: false
- name: testExecutionTimeout
  displayName: 'Test Execution Timeout (minutes)'
  type: number
  default: 240
- name: pipelineType
  type: string
  values:
    - 'singleTest'
  default: 'singleTest'
- name: usePerformanceSetupAdditionalParams
  type: boolean
  default: true
- name: performanceSetupAdditionalParams
  type: object
  default:
    Accounts:
    - name: 'catalogperfsetup03'
      geography: 'us'

variables:
- template: variables/k6TestFiles.yml
- name: performanceSetupAssemblyName
  value: 'Aveva.Platform.Catalog.Tests.PerformanceSetup'
- name: performanceSetupDotNetVersion
  value: '8.x'
- name: performanceSetupPath
  value: 'svc/test/Platform.Catalog.Tests.Performance/Platform.Catalog.Tests.PerformanceSetup'

stages:
- ${{ if eq(parameters.pipelineType, 'singleTest') }}:
  - template: pipelines/v2/<EMAIL>
    parameters: 
      artifactVersion: ${{ parameters.artifactVersion }}
      artifactName: ${{ parameters.artifactName }}
      performanceTestName: ${{ parameters.performanceTestName }}
      serviceFilter: ${{ parameters.serviceFilter }}
      testFilter: ${{ parameters.testFilter }}
      envPrefix: ${{ parameters.envPrefix }}
      DNS: ${{ parameters.DNS }}
      numberOfConcurrentK6Instances: ${{ parameters.numberOfConcurrentK6Instances }}
      useK6CommandLineArgs: ${{ parameters.useK6CommandLineArgs }}
      k6CommandLineArgs: '${{ parameters.k6CommandLineArgs }}'
      publishResultsToFile: ${{ parameters.publishResultsToFile }}
      additionalEnvironmentVariables: ${{ parameters.additionalEnvironmentVariables }}
      useAdditionalEnvironmentVariables: ${{ parameters.useAdditionalEnvironmentVariables }}
      enableHTTPDebugLogging: ${{ parameters.enableHTTPDebugLogging }}
      disableK6Thresholds: ${{ parameters.disableK6Thresholds }}
      testExecutionTimeout: ${{ parameters.testExecutionTimeout }}
      testFileDefinitions: ${{ variables[format('{0}{1}', parameters.serviceFilter, 'TestFileDefinitions')] }}
      performanceSetupAssemblyName: ${{ variables.performanceSetupAssemblyName }}
      performanceSetupDotNetVersion: ${{ variables.performanceSetupDotNetVersion }}
      performanceSetupAdditionalParams: ${{ parameters.performanceSetupAdditionalParams }}
      usePerformanceSetupAdditionalParams: ${{ parameters.usePerformanceSetupAdditionalParams }}