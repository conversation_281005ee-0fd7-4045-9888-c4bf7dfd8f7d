/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { ServicesRequestBuilderNavigationMetadata, ServicesRequestBuilderRequestsMetadata, type ServicesRequestBuilder } from './services/index.js';
// @ts-ignore
import { type BaseRequestBuilder, type KeysToExcludeForNavigationMetadata, type NavigationMetadata } from '@microsoft/kiota-abstractions';

/**
 * Builds and executes requests for operations under /ops/catalog/v2
 */
export interface V2RequestBuilder extends BaseRequestBuilder<V2RequestBuilder> {
    /**
     * The services property
     */
    get services(): ServicesRequestBuilder;
}
/**
 * Uri template for the request builder.
 */
export const V2RequestBuilderUriTemplate = "{+baseurl}/ops/catalog/v2";
/**
 * Metadata for all the navigation properties in the request builder.
 */
export const V2RequestBuilderNavigationMetadata: Record<Exclude<keyof V2RequestBuilder, KeysToExcludeForNavigationMetadata>, NavigationMetadata> = {
    services: {
        requestsMetadata: ServicesRequestBuilderRequestsMetadata,
        navigationMetadata: ServicesRequestBuilderNavigationMetadata,
    },
};
/* tslint:enable */
/* eslint-enable */
