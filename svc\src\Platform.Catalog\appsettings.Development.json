{
  "Logging": {
    "LogLevel": {
      "Default": "Trace",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Application": {
    "EnableSwaggerUI": "true",
    "SwaggerServiceTitle": "Catalog Service",
    "SwaggerServiceDescription": "The Catalog service provides a central repository of services available within the CONNECT platform. It enables you to discover and access information about services that can be provisioned for your organization.\n\nThis API specifically returns services that have been made available to your account. It provides detailed information about each service including its capabilities, requirements, and provisioning details. Use this account-scoped API when you need to see exactly which services your account can access and provision.\n\nThe catalog works closely with Instance Management, which handles the actual provisioning of services to accounts. While the catalog identifies which services are available to you, Instance Management is responsible for creating and managing instances of those services within your account."
  },
  "Authentication": {
    "ServiceId": "catalog",
    "IdentityDnsZoneName": "https://devonebox.platform.capdev-connect.aveva.com",
    "TurnOffHttpsRequiredForTokenValidation": true,
    "OverrideAuthenticationForLocalTesting": true
  },
  "Instrumentation": {
    "DebugEnabled": "false", //Should always be true for local development.
    //"OtelExporterEndpoint":  "http://localhost:4317",
    "RoleName": "catalog-api",
    "ServiceName": "catalog",
    "SamplingProbability": 1.0 //custom traces needs this value for tracing.
  }
}