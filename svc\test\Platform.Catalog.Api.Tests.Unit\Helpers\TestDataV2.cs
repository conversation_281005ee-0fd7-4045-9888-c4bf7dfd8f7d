﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.Models;
using ApiResponse = Aveva.Platform.Catalog.Domain.DataTransferObjects.Api.v2;
using OpsResponse = Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;

namespace Aveva.Platform.Catalog.Api.Tests.Unit.Helpers
{
    internal static class TestDataV2
    {
        #region Internal Fields

        /// <summary>
        /// The expected multiple result instance count.
        /// </summary>
        internal const int ExpectedMultiResultCount = 3;

        #endregion Internal Fields

        #region Test Data Creation

        /// <summary>
        /// Creates the test ServiceEntries.
        /// </summary>
        /// <param name="count">The count.</param>
        /// <returns>Enumeration of entities.</returns>
        internal static IEnumerable<V1ServiceEntry> CreateServiceEntries(int count = ExpectedMultiResultCount, V1ServiceAvailability? availability = null)
        {
            for (int i = 1; i <= count; i++)
            {
                yield return new V1ServiceEntry
                {
                    Id = count.ToString(),
                    DisplayName = $"Name {i}",
                    HostingType = V1HostingType.Geography,
                    Availability = availability,
                    Lifecycle = new V1Lifecycle()
                    {
                        Trigger = V1Trigger.Catalog,
                    },
                };
            }
        }

        /// <summary>
        /// Creates the test Data Transfer Object instance.
        /// </summary>
        /// <param name="fromEntity">From entity.</param>
        /// <returns>Data Transfer Object instance.</returns>
        internal static OpsResponse.ServiceResponse CreateServiceEntryTestDtoOps(V1ServiceEntry fromEntity)
        {
            return new OpsResponse.ServiceResponse()
            {
                Id = fromEntity.Id,
                DisplayName = fromEntity.DisplayName,
                HostingType = fromEntity.HostingType.ToString(),
                Availability = new OpsResponse.ServiceAvailability()
                {
                    Enabled = fromEntity.Availability?.Enabled,
                    Limit = fromEntity.Availability?.Limit,
                },
                Lifecycle = new Lifecycle()
                {
                    Trigger = "Catalog",
                },
            };
        }

        /// <summary>
        /// Creates the test Data Transfer Objects.
        /// </summary>
        /// <param name="fromEntities">From entities.</param>
        /// <returns>Enumeration of DTOs.</returns>
        internal static IEnumerable<OpsResponse.ServiceResponse> CreateServiceEntryTestDtosOps(IEnumerable<V1ServiceEntry> fromEntities)
        {
            foreach (V1ServiceEntry entity in fromEntities)
            {
                yield return CreateServiceEntryTestDtoOps(entity);
            }
        }

        /// <summary>
        /// Creates the test Data Transfer Object instance.
        /// </summary>
        /// <param name="fromEntity">From entity.</param>
        /// <returns>Data Transfer Object instance.</returns>
        internal static ApiResponse.ServiceResponse CreateServiceEntryTestDtoApi(V1ServiceEntry fromEntity)
        {
            return new ApiResponse.ServiceResponse()
            {
                Id = fromEntity.Id,
                DisplayName = fromEntity.DisplayName,
                HostingType = fromEntity.HostingType.ToString(),
                Availability = new ApiResponse.ServiceAvailability()
                {
                    Limit = fromEntity.Availability?.Limit,
                },
                Lifecycle = new Lifecycle()
                {
                    Trigger = "Catalog",
                },
            };
        }

        /// <summary>
        /// Creates the test Data Transfer Objects.
        /// </summary>
        /// <param name="fromEntities">From entities.</param>
        /// <returns>Enumeration of DTOs.</returns>
        internal static IEnumerable<ApiResponse.ServiceResponse> CreateServiceEntryTestDtosApi(IEnumerable<V1ServiceEntry> fromEntities)
        {
            foreach (V1ServiceEntry entity in fromEntities)
            {
                yield return CreateServiceEntryTestDtoApi(entity);
            }
        }
        #endregion Test Data Creation
    }
}