trigger: none

schedules:
    - cron: "0 0 * * *"
      displayName: Nightly Catalog Test
      branches:
        include:
        - main
      always: True

resources:
  repositories:
    # Platform-deployment resources
    - repository: 'platform-deployment'
      type: git
      name: 'platform-deployment'
      ref: refs/heads/main

parameters:
- name: tests
  type: object
  default:
    - filePath: '**/Platform.Catalog.Tests.Deployment/Platform.Catalog.Tests.Deployment.csproj' # The file path to the csproj files for the unit tests.
      filter: 'Category=Functional' # This is the string filter used to filter which unit tests should run. To see how to structure this filter, see this xUnit documentation on formatting the expression.
- name: environments
  type: object
  default:
  - envBaseUrl: "https://infradev.platform.capdev-connect.aveva.com/"
    environmentName: "infradev"
  - envBaseUrl: "https://datadev.platform.capdev-connect.aveva.com/"
    environmentName: "datadev"
  - envBaseUrl: "https://appdev.platform.capdev-connect.aveva.com/"
    environmentName: "appdev"
  - envBaseUrl: "https://int.platform.capdev-connect.aveva.com/"
    environmentName: "int"
  - envBaseUrl: "https://platform.test-connect.aveva.com/"
    environmentName: "preprod"
  #- envBaseUrl: "https://platform.connect.aveva.com/"
  #  environmentName: "prod"

variables:
# The file path to the sln file for the main project.
# This is a file path relative to the top level of the current repo.
- name: buildFilePath
  value: '.'
- name: buildFileName
  value: 'platform-catalog.sln'
- name: nugetConfigFilePath
  value: 'nuget.config'
- name: dotNetVersion
  value: '8.x'

stages:
- ${{ each environment in parameters.environments }}:
  - template: pipelines/v2/test-run.yml@platform-deployment
    parameters:      
      tests: ${{ parameters.tests }}
      nugetConfigFilePath: ${{ variables.nugetConfigFilePath }}
      dotNetVersion: ${{ variables.dotNetVersion }}
      buildFilePath: ${{ variables.buildFilePath }}
      buildFileName: ${{ variables.buildFileName }}     
      envBaseUrl: ${{ environment.envBaseUrl }}
      environmentName: ${{ environment.environmentName }}
      treatWarningsAsErrors: false