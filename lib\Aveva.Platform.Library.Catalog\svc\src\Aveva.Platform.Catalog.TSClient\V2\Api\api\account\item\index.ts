/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { CatalogRequestBuilderNavigationMetadata, type CatalogRequestBuilder } from './catalog/index.js';
// @ts-ignore
import { type BaseRequestBuilder, type KeysToExcludeForNavigationMetadata, type NavigationMetadata } from '@microsoft/kiota-abstractions';

/**
 * Builds and executes requests for operations under /api/account/{accountId}
 */
export interface WithAccountItemRequestBuilder extends BaseRequestBuilder<WithAccountItemRequestBuilder> {
    /**
     * The catalog property
     */
    get catalog(): CatalogRequestBuilder;
}
/**
 * Uri template for the request builder.
 */
export const WithAccountItemRequestBuilderUriTemplate = "{+baseurl}/api/account/{accountId}";
/**
 * Metadata for all the navigation properties in the request builder.
 */
export const WithAccountItemRequestBuilderNavigationMetadata: Record<Exclude<keyof WithAccountItemRequestBuilder, KeysToExcludeForNavigationMetadata>, NavigationMetadata> = {
    catalog: {
        navigationMetadata: CatalogRequestBuilderNavigationMetadata,
    },
};
/* tslint:enable */
/* eslint-enable */
