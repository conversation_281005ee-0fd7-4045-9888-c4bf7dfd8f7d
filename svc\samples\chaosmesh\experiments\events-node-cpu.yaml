apiVersion: chaos-mesh.org/v1alpha1
kind: StressChaos
metadata:
  name: events-stress-cpu-experiment
  namespace: chaos-mesh
spec:
  mode: all                          # Apply to all matching pods
  selector:
    namespaces: 
      - platform-catalog             # Target namespace
    labelSelectors:
      pod-selector: catalog-events      # Label to identify specific pods
  stressors:
    cpu:
      workers: 8                     # Number of CPU workers
      load: 95                       # Target CPU load (95%)
  duration: "2m"                    # Total duration of the experiment (25 seconds)

# Key parameters for the StressChaos experiment:
# - mode: Determines whether to target all pods or a specific set.
# - selector: Filters the pods based on namespace and labels.
# - stressors: Specifies resource stress configurations.
# - duration: The total time the stress experiment will run.
