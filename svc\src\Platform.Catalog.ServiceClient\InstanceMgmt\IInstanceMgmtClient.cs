﻿using Aveva.Platform.InstanceMgmt.Client.Ops.Models;
using Aveva.Platform.InstanceMgmt.Client.Ops.Ops.InstanceMgmt.V1.Instances;

namespace Aveva.Platform.Catalog.ServiceClient;

/// <summary>
/// Interface for InstanceMgmt API service client.
/// </summary>
public interface IInstanceMgmtClient
{
    /// <summary>
    /// Get instances based on a filter.
    /// </summary>
    Task<IEnumerable<InstanceResponse>> GetInstancesAsync(InstancesRequestBuilder.InstancesRequestBuilderGetQueryParameters request, CancellationToken cancellationToken = default);
}