﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;

/// <summary>
/// Defines configuration options for service dependencies. Each configuration represents a setting needed to properly integrate with a dependent service.
/// </summary>
public class DependencyConfig
{
    /// <summary>
    /// The user-friendly display name for the configuration setting. This label appears in user interfaces when configuring service dependencies.
    /// </summary>
    public string? Label { get; init; }

    /// <summary>
    /// Additional explanatory text that provides guidance on how to use this configuration setting. This help text gives users context about what information should be provided.
    /// </summary>
    public string? Help { get; init; }

    /// <summary>
    /// Indicates whether this configuration value must be provided (`true`) or can be omitted (`false`) when establishing the dependency between services.
    /// </summary>
    public bool Required { get; init; }

    /// <summary>
    /// The minimum length constraint for the configuration value. When specified, the provided configuration must contain at least this many characters. If not set, there is no minimum length restriction.
    /// </summary>
    public int? Min { get; init; }

    /// <summary>
    /// The maximum length constraint for the configuration value. When specified, the provided configuration must not exceed this number of characters. If not set, there is no maximum length restriction.
    /// </summary>
    public int? Max { get; init; }

    /// <inheritdoc/>
    public override bool Equals(object? obj)
        => obj is DependencyConfig item
             && string.Equals(item.Label, Label, StringComparison.InvariantCultureIgnoreCase)
             && string.Equals(item.Help, Help, StringComparison.InvariantCultureIgnoreCase)
             && item.Required == Required
             && item.Min == Min
             && item.Max == Max;

    /// <inheritdoc/>
    public override int GetHashCode() => base.GetHashCode();
}