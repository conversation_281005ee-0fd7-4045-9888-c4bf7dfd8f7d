﻿namespace Aveva.Platform.Catalog.Sdk.DataTransferObjects.V3
{
    /// <summary>
    /// LegacyProtocolMappings.
    /// </summary>
    public class LegacyProtocolMappings
    {
        /// <summary>
        /// The mapping of geographies to the regions declared for the solution.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public Dictionary<string, string>? Geographies { get; init; } = new Dictionary<string, string>();
#pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// The mapping of dependencies to solution integrations.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public Dictionary<string, string>? Dependencies { get; init; } = new Dictionary<string, string>();
#pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// The mappings of applications to capability definitions.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public List<LegacyProtocolApplicationMapping>? Applications { get; init; } = new List<LegacyProtocolApplicationMapping>();
#pragma warning restore CA2227 // Collection properties should be read only
    }
}