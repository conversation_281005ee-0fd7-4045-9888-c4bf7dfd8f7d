﻿﻿ namespace Aveva.Platform.Catalog.Sdk.DataTransferObjects.V3;

/// <summary>
/// CatalogItemDependency.
/// </summary>
public class CatalogDataDependency
{
    /// <summary>
    /// Initializes a new instance of the <see cref="CatalogDataDependency"/> class.
    /// </summary>
    public CatalogDataDependency() // Required by EF
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="CatalogDataDependency"/> class.
    /// </summary>
    public CatalogDataDependency(CatalogDataDependencyType type, CatalogDataDependencyCardinality cardinality, bool colocated = false, Dictionary<string, CatalogDataDependencyConfig>? config = null)
    {
        Type = type;
        Cardinality = cardinality;
        Colocated = colocated;
        Config = config;
    }

    /// <summary>
    /// Gets or sets type.
    /// </summary>
    public CatalogDataDependencyType Type { get; set; }

    /// <summary>
    /// Gets or sets cardinality.
    /// </summary>
    public CatalogDataDependencyCardinality Cardinality { get; set; }

    /// <summary>
    /// Gets or sets colocated.
    /// </summary>
    public bool Colocated { get; set; }

    /// <summary>
    /// Gets or sets config.
    /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
    public Dictionary<string, CatalogDataDependencyConfig>? Config { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only
}