﻿using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Infrastructure.Entities;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.K8sCustomResources;

/// <summary>
/// <see cref="V1K8sServiceEntry"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Domain")]
[Trait("Category", "Unit")]
[Trait("Category", "Domain.Unit")]
[Trait("Tag", "V1K8sSeviceEntryTests")]
public class V1K8sSeviceEntryTests
{
    #region Test Cases

    [Fact]
    public void V1K8sServiceEntry_Create_Fails()
    {
        // Act and Assert
        Should.Throw<ArgumentNullException>(() =>
        {
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            V1K8sServiceEntry.Create(null, "test");
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
        });
    }

    [Fact]
    public void V1K8sServiceEntry_InitializesSpecStatusMetadata_Succeeds()
    {
        // Arrange
        V1K8sServiceEntry serviceEntry = new V1K8sServiceEntry();
        var spec = serviceEntry.Spec;
        var status = serviceEntry.Status;
        var metadata = serviceEntry.Metadata;

        // Act and Assert
        spec.ShouldBeNull();
        status.ShouldBeNull();
        metadata.ShouldBeNull();
    }

    [Fact]
    public void V1K8sServiceEntry_CreateWithParameters_Succeeds()
    {
        // Arrange & Act
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Availability = new V1ServiceAvailability(),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        V1K8sServiceEntry serviceEntry = V1K8sServiceEntry.Create(newEntry, "test");

        // Act and Assert
        serviceEntry.Spec.ShouldBe(newEntry);
        serviceEntry.Metadata.NamespaceProperty.ShouldBe("test");
        serviceEntry.Metadata.NamespaceProperty.ShouldBe(serviceEntry.Metadata.Name);
        serviceEntry.Kind.ShouldNotBeNull();
        serviceEntry.ApiVersion.ShouldNotBeNull();
    }

    [Fact]
    public void V1K8sServiceEntry_ToString()
    {
        // Arrange & Act
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        V1K8sServiceEntry serviceEntry = V1K8sServiceEntry.Create(newEntry, "test");

        // Act
        var k8sServiceEntry = serviceEntry.ToString();

        // Assert
        k8sServiceEntry.ShouldNotBeNullOrEmpty();
        k8sServiceEntry.ShouldContain("test");
    }
    #endregion Test Cases
}