﻿namespace Aveva.Platform.Catalog.Operator.Logging
{
    internal static partial class LoggerExtensions
    {
        [LoggerMessage((int)LoggerEvents.CreateV1ServiceEntry, LogLevel.Information, "Create V1ServiceEntry. ServiceEntryName: '{ServiceEntryName}' Version: '{version}' K8sNameSpace: '{k8sNamespace}' dryRun: '{dryRun}'")]
        internal static partial void CreateV1ServiceEntry(this ILogger logger, string serviceEntryName, string version, string k8sNamespace, bool dryRun);

        [LoggerMessage((int)LoggerEvents.UpdateV1ServiceEntry, LogLevel.Information, "Update V1ServiceEntry. ServiceEntryName: '{ServiceEntryName}' Version: '{version}' K8sNameSpace: '{k8sNamespace}' dryRun: '{dryRun}'")]
        internal static partial void UpdateV1ServiceEntry(this ILogger logger, string serviceEntryName, string version, string k8sNamespace, bool dryRun);

        [LoggerMessage((int)LoggerEvents.DeleteV1ServiceEntry, LogLevel.Information, "Update V1ServiceEntry. ServiceEntryName: '{ServiceEntryName}' Version: '{version}' K8sNameSpace: '{k8sNamespace}' dryRun: '{dryRun}'")]
        internal static partial void DeleteV1ServiceEntry(this ILogger logger, string serviceEntryName, string version, string k8sNamespace, bool dryRun);

        [LoggerMessage((int)LoggerEvents.CreateV1ServiceEntryFailed, LogLevel.Error, "Create V1ServiceEntry failed. ServiceEntryName: '{ServiceEntryName}' Version: '{version}' K8sNameSpace: '{k8sNamespace}' dryRun: '{dryRun}' Message '{message}'")]
        internal static partial void CreateV1ServiceEntryFailed(this ILogger logger, string serviceEntryName, string version, string k8sNamespace, string message, bool dryRun);

        [LoggerMessage((int)LoggerEvents.UpdateV1ServiceEntryFailed, LogLevel.Error, "Update V1ServiceEntry failed. ServiceEntryName: '{ServiceEntryName}' Version: '{version}' K8sNameSpace: '{k8sNamespace}' dryRun: '{dryRun}' Message '{message}'")]
        internal static partial void UpdateV1ServiceEntryFailed(this ILogger logger, string serviceEntryName, string version, string k8sNamespace, string message, bool dryRun);

        [LoggerMessage((int)LoggerEvents.DeleteV1ServiceEntryFailed, LogLevel.Error, "Create V1ServiceEntry failed. ServiceEntryName: '{ServiceEntryName}' Version: '{version}' K8sNameSpace: '{k8sNamespace}' dryRun: '{dryRun}' Message '{message}'")]
        internal static partial void DeleteV1ServiceEntryFailed(this ILogger logger, string serviceEntryName, string version, string k8sNamespace, string message, bool dryRun);
    }
}