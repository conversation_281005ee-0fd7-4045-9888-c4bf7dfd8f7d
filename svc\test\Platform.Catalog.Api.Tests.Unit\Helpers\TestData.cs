﻿using Aveva.Platform.Catalog.Domain.Models;
using ApiResponse = Aveva.Platform.Catalog.Domain.DataTransferObjects.Api.v2;
using OpsResponse = Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;

namespace Aveva.Platform.Catalog.Api.Tests.Unit.Helpers;

/// <summary>
/// Test data generation methods for use in test cases.
/// </summary>
internal static class TestData
{
    #region Internal Fields

    /// <summary>
    /// The expected multiple result instance count.
    /// </summary>
    internal const int ExpectedMultiResultCount = 3;

    #endregion Internal Fields

    #region Test Data Creation

    /// <summary>
    /// Creates the test ServiceEntries.
    /// </summary>
    /// <param name="count">The count.</param>
    /// <returns>Enumeration of entities.</returns>
    internal static IEnumerable<V1ServiceEntry> CreateServiceEntriesOps(int count = ExpectedMultiResultCount, OpsResponse.ServiceAvailability? expectedAvailability = null)
    {
        for (int i = 1; i <= count; i++)
        {
            yield return new V1ServiceEntry
            {
                Id = count.ToString(),
                DisplayName = $"Name {i}",
                HostingType = V1HostingType.Geography,
                Availability = new V1ServiceAvailability
                {
                    Enabled = expectedAvailability?.Enabled,
                    Limit = expectedAvailability?.Limit,
                },
            };
        }
    }

    /// <summary>
    /// Creates the test Data Transfer Object instance.
    /// </summary>
    /// <param name="fromEntity">From entity.</param>
    /// <returns>Data Transfer Object instance.</returns>
    internal static OpsResponse.ServiceResponse CreateServiceEntryTestDtoOps(V1ServiceEntry fromEntity, OpsResponse.ServiceAvailability? expectedAvailability)
    {
        return new OpsResponse.ServiceResponse()
        {
            Id = fromEntity.Id,
            DisplayName = fromEntity.DisplayName,
            HostingType = fromEntity.HostingType.ToString(),
            Availability = expectedAvailability,
        };
    }

    /// <summary>
    /// Creates the test Data Transfer Objects.
    /// </summary>
    /// <param name="fromEntities">From entities.</param>
    /// <returns>Enumeration of DTOs.</returns>
    internal static IEnumerable<OpsResponse.ServiceResponse> CreateServiceEntryTestDtosOps(IEnumerable<V1ServiceEntry> fromEntities)
    {
        foreach (V1ServiceEntry entity in fromEntities)
        {
            yield return CreateServiceEntryTestDtoOps(entity, null);
        }
    }

    /// <summary>
    /// Creates the test ServiceEntries.
    /// </summary>
    /// <param name="count">The count.</param>
    /// <returns>Enumeration of entities.</returns>
    internal static IEnumerable<V1ServiceEntry> CreateServiceEntriesApi(int count = ExpectedMultiResultCount, ApiResponse.ServiceAvailability? expectedAvailability = null)
    {
        for (int i = 1; i <= count; i++)
        {
            yield return new V1ServiceEntry
            {
                Id = count.ToString(),
                DisplayName = $"Name {i}",
                HostingType = V1HostingType.Geography,
                Availability = new V1ServiceAvailability
                {
                    Limit = expectedAvailability?.Limit,
                },
            };
        }
    }

    /// <summary>
    /// Creates the test Data Transfer Object instance.
    /// </summary>
    /// <param name="fromEntity">From entity.</param>
    /// <returns>Data Transfer Object instance.</returns>
    internal static ApiResponse.ServiceResponse CreateServiceEntryTestDtoApi(V1ServiceEntry fromEntity, ApiResponse.ServiceAvailability? expectedAvailability)
    {
        return new ApiResponse.ServiceResponse()
        {
            Id = fromEntity.Id,
            DisplayName = fromEntity.DisplayName,
            HostingType = fromEntity.HostingType.ToString(),
            Availability = expectedAvailability,
        };
    }

    /// <summary>
    /// Creates the test Data Transfer Objects.
    /// </summary>
    /// <param name="fromEntities">From entities.</param>
    /// <returns>Enumeration of DTOs.</returns>
    internal static IEnumerable<ApiResponse.ServiceResponse> CreateServiceEntryTestDtosApi(IEnumerable<V1ServiceEntry> fromEntities)
    {
        foreach (V1ServiceEntry entity in fromEntities)
        {
            yield return CreateServiceEntryTestDtoApi(entity, null);
        }
    }
    #endregion Test Data Creation
}