﻿using System.Globalization;
using System.Text.Json;
using Asp.Versioning;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Aveva.Platform.Catalog.Api.Configuration;

/// <summary>
/// Represents the OpenAPI/Swashbuckle operation filter used to document information provided, but not used.
/// </summary>
/// <remarks>This <see cref="IOperationFilter"/> is only required due to bugs in the <see cref="SwaggerGenerator"/>.
/// Once they are fixed and published, this class can be removed.</remarks>
/// Taken from: https://github.com/dotnet/aspnet-api-versioning/blob/3857a332057d970ad11bac0edfdbff8a559a215d/Catalogs/AspNetCore/WebApi/MinimalOpenApiCatalog/SwaggerDefaultValues.cs
public class SwaggerDefaultValues : IOperationFilter
{
    /// <inheritdoc />
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        ArgumentNullException.ThrowIfNull(operation);
        ArgumentNullException.ThrowIfNull(context);
        ApiDescription apiDescription = context.ApiDescription;

        operation.Deprecated |= IsDeprecated(apiDescription);

        // REF: https://github.com/domaindrivendev/Swashbuckle.AspNetCore/issues/1752#issue-663991077
        foreach (ApiResponseType responseType in context.ApiDescription.SupportedResponseTypes)
        {
            // REF: https://github.com/domaindrivendev/Swashbuckle.AspNetCore/blob/b7cf75e7905050305b115dd96640ddd6e74c7ac9/src/Swashbuckle.AspNetCore.SwaggerGen/SwaggerGenerator/SwaggerGenerator.cs#L383-L387
            string responseKey = responseType.IsDefaultResponse ? "default" : responseType.StatusCode.ToString("G", CultureInfo.InvariantCulture);
            OpenApiResponse response = operation.Responses[responseKey];

            // Remove unsupported types
            foreach (string? contentType in response.Content.Keys)
            {
                if (!responseType.ApiResponseFormats.Any(x => string.Equals(x.MediaType, contentType, StringComparison.OrdinalIgnoreCase)))
                {
                    response.Content.Remove(contentType);
                }
            }
        }

        if (operation.Parameters == null)
        {
            return;
        }

        // REF: https://github.com/domaindrivendev/Swashbuckle.AspNetCore/issues/412
        // REF: https://github.com/domaindrivendev/Swashbuckle.AspNetCore/pull/413
        foreach (OpenApiParameter? parameter in operation.Parameters)
        {
            ApiParameterDescription description = apiDescription.ParameterDescriptions.First(p => string.Equals(p.Name, parameter.Name, StringComparison.OrdinalIgnoreCase));

            parameter.Description ??= description.ModelMetadata?.Description;

            if (parameter.Schema.Default == null &&
                 description.DefaultValue != null &&
                 description.DefaultValue is not DBNull &&
                 description.ModelMetadata is ModelMetadata modelMetadata)
            {
                // REF: https://github.com/Microsoft/aspnet-api-versioning/issues/429#issuecomment-605402330
                string json = JsonSerializer.Serialize(description.DefaultValue, modelMetadata.ModelType);
                parameter.Schema.Default = OpenApiAnyFactory.CreateFromJson(json);
            }

            parameter.Required |= description.IsRequired;
        }
    }

    // Due to ambiguous call to ApiVersionExtensions.IsDeprecated(), the implementation has been copied here
    private static bool IsDeprecated(ApiDescription apiDescription)
    {
        ArgumentNullException.ThrowIfNull(apiDescription, nameof(apiDescription));

        ApiVersionMetadata metatadata = apiDescription.ActionDescriptor.GetApiVersionMetadata();

        if (metatadata.IsApiVersionNeutral)
        {
            return false;
        }

        bool hasApiVersion = apiDescription.Properties.TryGetValue(typeof(ApiVersion), out object? apiVersion);
        if (!hasApiVersion || apiVersion == null)
        {
            return false;
        }

        ApiVersionModel model = metatadata.Map(ApiVersionMapping.Explicit | ApiVersionMapping.Implicit);

        return model.DeprecatedApiVersions.Contains((ApiVersion)apiVersion);
    }
}