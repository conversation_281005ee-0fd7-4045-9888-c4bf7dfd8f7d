<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <!-- Assembly properties -->
  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.Api</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.Api</RootNamespace>
    <Nullable>enable</Nullable>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>  
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aveva.Platform.Common.Abstractions" />
    <PackageReference Include="Aveva.Platform.Common.Framework.Abstractions" />
    <PackageReference Include="Aveva.Platform.Common.Framework.AspNetCore" />
    <PackageReference Include="Dapr.Client" />
    <PackageReference Include="Dapr.AspNetCore" />
    <PackageReference Include="Dapr.Extensions.Configuration" />
    <PackageReference Include="Asp.Versioning.Http" />
    <PackageReference Include="Asp.Versioning.Mvc" />
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" />
    <PackageReference Include="Aveva.Platform.Common.Framework.ExceptionHandling" />
    <PackageReference Include="Aveva.Platform.Common.Framework.Mapping.AutoMapper" />
    <PackageReference Include="Aveva.Platform.Common.Monitoring.HealthChecks" />
    <PackageReference Include="Aveva.Platform.Common.Monitoring.HealthChecks.Dapr" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" />
    <PackageReference Include="Aveva.Platform.Common.Monitoring.Instrumentation" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Platform.Catalog.Domain\Platform.Catalog.Domain.csproj" />
    <ProjectReference Include="..\Platform.Catalog.ServiceClient\Platform.Catalog.ServiceClient.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>