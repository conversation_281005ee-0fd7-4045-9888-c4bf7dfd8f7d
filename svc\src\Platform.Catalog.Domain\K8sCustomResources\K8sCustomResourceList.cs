﻿using System.Text.Json.Serialization;
using k8s;
using k8s.Models;

namespace Aveva.Platform.Catalog.Domain.K8sCustomResources
{
    /// <summary>
    /// A class to retrieve list of Kubernetes custom resource.
    /// </summary>
    /// <typeparam name="T">A Kubernetes custom resource object.</typeparam>
    public class K8sCustomResourceList<T> : KubernetesObject, IMetadata<V1ListMeta>
    {
        /// <summary>
        /// Gets or Sets k8s metadata.
        /// </summary>
        [JsonPropertyName("metadata")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public V1ListMeta Metadata { get; set; }

        /// <summary>
        /// Gets or Sets IEnumerable of custom kubernetes object.
        /// </summary>
        [JsonPropertyName("items")]
        public IEnumerable<T> Items { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}