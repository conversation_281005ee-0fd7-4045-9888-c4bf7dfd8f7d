apiVersion: chaos-mesh.org/v1alpha1
kind: NetworkChaos
metadata:
  name: network-loss-experiment
  namespace: chaos-mesh
spec:
  action: loss                       # Action to simulate packet loss
  mode: all                          # Apply to all matching pods
  selector:
    namespaces:
      - platform-catalog             # Target namespace
    labelSelectors:
      pod-selector: catalog-api      # Label to identify specific pods
  loss:
    loss: "75"                       # 75% packet loss
    correlation: '50'                # Correlation rate
  direction: both                    # from for outgoing, to for incoming, both for outgoing and incoming
  duration: "2m"                     # Total duration of the experiment (2 minutes)
  target:
    selector:
      namespaces:
        - platform-gateway
      labelSelectors:
        app: gateway-controller
    mode: all

# Key parameters for the NetworkChaos experiment:
# - action: Specifies the type of network chaos.
# - mode: Determines whether to target all pods or a specific set.
# - selector: Filters the pods based on namespace and labels.
# - loss: Defines the packet loss percentage and correlation.
# - direction: Specifies direction of traffic affected.
# - duration: The total time the chaos experiment will run.
