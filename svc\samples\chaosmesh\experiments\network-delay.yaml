apiVersion: chaos-mesh.org/v1alpha1
kind: NetworkChaos
metadata:
  name: network-delay-experiment
  namespace: chaos-mesh
spec:
  action: delay                         # Action to introduce a delay
  mode: all                             # Apply to all matching pods
  selector:
    namespaces:
      - platform-catalog               # Target namespace
    labelSelectors:
      pod-selector: catalog-api         # Label to identify specific pods
  delay:
    latency: '2s'                      # Delay duration (2 seconds)
    correlation: '50'                  # Correlation rate
  duration: "2m"                       # Total duration of the experiment (2 minutes)

# Key parameters for the NetworkChaos experiment:
# - action: Specifies the type of network chaos.
# - mode: Determines whether to target all pods or a specific set.
# - selector: Filters the pods based on namespace and labels.
# - delay: Defines the parameters of the network delay.
# - duration: The total time the chaos experiment will run.
