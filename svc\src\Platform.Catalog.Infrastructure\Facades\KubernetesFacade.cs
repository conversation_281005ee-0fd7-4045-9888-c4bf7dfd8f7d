﻿using System.Diagnostics.CodeAnalysis;
using Aveva.Platform.Catalog.Domain;
using Aveva.Platform.Catalog.Infrastructure.Entities;
using k8s;

namespace Aveva.Platform.Catalog.Infrastructure.Facades
{
    [ExcludeFromCodeCoverage(Justification = "Facade pattern to enable testing.")]
    internal sealed class KubernetesFacade : IKubernetesFacade, IDisposable
    {
        private readonly GenericClient _genericClient;
        public KubernetesFacade(IKubernetes k8s)
        {
            _genericClient = new GenericClient(k8s, CatalogConstants.Group, CatalogConstants.V1, CatalogConstants.ServiceEntry.Plural);
        }

        public void Dispose()
        {
            _genericClient?.Dispose();
        }

        public Task<V1K8sServiceEntryList> ListServiceEntriesAsync() =>
            _genericClient.ListAsync<V1K8sServiceEntryList>();
    }
}