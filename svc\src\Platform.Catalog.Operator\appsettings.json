{"Apis": {"AccountMgmtService": {"serviceIdentityId": "accountmgmt"}, "CatalogService": {"serviceIdentityId": "catalog", "BaseServiceUrl": "http://ni--catalog--api.platform-catalog", "GetAllServiceEntries": "ops/v2/services"}, "CatalogEventsService": {"serviceIdentityId": "catalog", "BaseServiceUrl": "http://ni--catalog--events.platform-catalog", "PostEvents": "internal/v1/eventtype/"}, "InstanceMgmtService": {"serviceIdentityId": "instancemgmt"}}, "Instrumentation": {"DebugEnabled": "false", "RoleName": "catalog-operator", "ServiceName": "catalog"}}