{"compilerOptions": {"target": "ES2017", "module": "ES6", "moduleResolution": "Node", "lib": ["ES2015", "DOM"], "rootDir": "./src", "baseUrl": "./", "outDir": "./dist", "removeComments": true, "resolveJsonModule": true, "esModuleInterop": true, "allowJs": true, "strict": false, "noImplicitAny": false, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "plugins": [{"transform": "typescript-transform-paths"}, {"transform": "./src/transforms/k6_test_transformer.ts", "type": "raw"}], "paths": {"@utils/*": ["./src/utils/*"], "@catalog_dtos/*": ["./src/models/*"]}}, "tsc-alias": {"resolveFullPaths": true, "verbose": false}, "include": ["./src/**/*.ts", "./src/**/*.json"], "exclude": ["./src/transforms"]}