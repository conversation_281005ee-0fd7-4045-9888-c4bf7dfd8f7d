import { K6TestBase } from "@platform/performance-libs";
import { apiGetAllServiceBase } from "../../common/apiV2_get_service";

export class CatalogPerformanceTests extends K6TestBase {
    baseTestClass: apiGetAllServiceBase;
    constructor() {
        super();
        this.baseTestClass = new apiGetAllServiceBase();
    }

    defaultScenarioTestIteration(data: any): void {
        this.baseTestClass.defaultScenarioTestIteration(data);
    }

    sharedTestSetup(): any {
        return this.baseTestClass.sharedTestSetup();
    }

    sharedTestTeardown(data: any): void {
        this.baseTestClass.sharedTestTeardown(data);
    }
}
