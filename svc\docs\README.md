# Cloud Platform Catalog REST API

This document provides information about the Catalog service.

[[_TOC_]]

## Introduction

To read more about Catalog service you can refer [Platform > Architecture > Catalog Wiki page.](https://dev.azure.com/AVEVA-VSTS/Cloud%20Platform/_wiki/wikis/Platform/47902/Catalog)

## Artifacts

Catalog service generates three types of artifacts i.e. Universal package, the container images and Catalog DTO sdk nuget package.
The universal package is published  to the [deployment artifact feed.](https://dev.azure.com/AVEVA-VSTS/Cloud%20Platform/_artifacts/feed/Cloud-Platform-Deployment)
The container images are published to Azure container registry.
The nuget package is published to Cloud Platform nuget feed.

## Getting Started

_TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:_

1. Installation process
2. Software dependencies
   KubeOps version 7.6.1
   Kubernetes.Client version 10.0.31
   CloudFlare SSL 
3. Latest releases
4. API references

## Build and Test

To build the cloud platform Catalog solution:
1. Clone the repository to your local computer.
2. Ensure your development machine is setup for Platform service development. If not, follow the steps detailed in [Platform > Getting Started wiki.](https://dev.azure.com/AVEVA-VSTS/Cloud%20Platform/_wiki/wikis/Platform/30718/Setup-Development-Environment)
3. Ensure your development machine has the correct version of the .NET Core SDK installed.
   This can be accomplished by typing 'dotnet --info' at the command line without the quotes and confirming your SDK version is equal to or greater than the TargetFramework specified in the host, unit, and integration test .csproj project files, currently net8.0.
4. Use an IDE, Visual Studio or Visual Studio Code to build, run and debug the cloud platform Catalog codebase.
5. Alternatively, you can build and run the applications directly using the .NET Core CLI toolset (e.g., dotnet build platformCatalog.sln, dotnet run, etc.).
6. If you want to deploy Catalog service & operator locally, you may need to install cert-manager in your minikube.
   Follow the steps mentioned in [Cert-manager helm chart](https://cert-manager.io/docs/installation/helm/) page for install the cert manager.
   Note: The cert-manager requires CRDs to be installed manually. The cert manager helm chart doesn't installs the CRDs.

## Generate ServiceEntry CRD Locally
The ServiceEntry CRD can be generated using Kubeops.Cli tool. This tool is already part of "Platform.Catalog.Operator" project.
From Developer powershell window, navigate to "Platform.Catalog.Operator" project window and run the below command.
```
dotnet kubeops gen op platform-catalog-operator --out kubeops
```

### Debug Catalog Operator Service
The Catalog operator service is used to implement the required Kubernetes webhooks for ServiceEntry CRD.
Currently the catalog service supports an admission webhook  for Catalog CRD.
In this section, you should be able to read the steps required to debug the Kubernetes webhook.

Catalog Operator service internally calls Catalog API HTTP endpoints through Dapr client. So you may need to follow below steps to setup dapr for local debugging.
1. Install and start Docker desktop in your localmachine
2. Install dapr cli (current version 1.12.4) to "C:\dapr" path.
3. Configure dapr components by running the below command. It should create ".dapr" folder in user profile directory and installs the required component. 
```
    dapr init 
```
4. Now you should be able to use "dapr" debugging profile to run Catalog API service from Visual studio OR you can run the below command in powershell to run Catalog API service.
Make sure to run this command within in Platform.Catalog project folder. 
```
dapr run --app-id ni--catalog--api --dapr-http-port 3500 --app-port 5045 -- dotnet run --project .
```

### Debug ServiceEntry Validator
After setting up your local minikube environment and Dapr local setup, you should be able to debug ServiceEntry validation webhook by running from Visual studio.
When we run the catalog operator service, KubeOps will load the latest profile from kubeconfig and listens to the service entry events.
Before starting a debugging session, make sure to delete the webhook from minikube. 

OR

You can follow below steps to manually configure a webhook local tunnel to reuse the same tunnel name.
To debug admission webhook, we need a local tunnel and couple of manual steps to do the debugging.
1. Create a local tunnel in your Visual studio 2022 by following the steps detailed in [Microsoft docs.](https://learn.microsoft.com/en-us/aspnet/core/test/dev-tunnels?view=aspnetcore-8.0)
   Make sure that you create a persistent and a public local tunnel to make everything work. 
2. When Valiating webhook is registered in the cluster, KubeOps defaults a tunnel url for the webhook.
   You need to update the webhook url to use Microsoft's local tunnel which we created in Step 1.
   Use below commands to edit the "clientConfig.url" property in the validation webhook.
```
kubectl edit validatingwebhookconfigurations "validators.platform-catalog-operator"
```
4. Now you should be able to route all the requests from the kubernetes cluster to your local machine for debugging.

_Note:_
_1. If you stop the debugging session, the Visual studio IDE does not automatically removes webhooks from the cluster. So you may need to remove the webhooks manually by running the below commands._
    ```
    kubectl delete validatingwebhookconfigurations validators.platform-catalog-operator
    ```
    _If the existing webhooks are not deleted, you may see the below error when you start the debugging session next time._
    ![image.png](WebhookRegistrationError.jpg)

_2. KubeOps allows the developers to override the local tunnel name, port etc using AddDevelopmentTunnel() method._

To run tests:
1. Tests are visible in Visual Studio in the Test Explorer window. Access it in Visual Studio from Test > Windows > Test Explorer.
2. Build the solution and view the tests in Test Explorer.
3. Choose a test or set of tests to run or debug.
4. Alternatively, you can run tests using the .NET Core CLI (e.g., dotnet test [--|\<PROJECT\>]).



## Contribute

_TODO: (Optional) Explain how other users and developers can contribute to make your code better._

If you want to learn more about creating good readme files then refer the following [guidelines](https://www.visualstudio.com/en-us/docs/git/create-a-readme). You can also seek inspiration from the below readme files:

- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)