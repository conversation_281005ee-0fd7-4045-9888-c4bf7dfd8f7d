import { ApiClient, OpsClient, checkApiResponse, RootRequest, LogLevel, HttpVerb, Logger } from "@platform/performance-libs";
import { Ops as OpsAccountMgmt } from "@platform/aveva.platform.accountmgmt.tsclient";
import { Ops as OpsIdentity } from "@platform/aveva.platform.identity.tsclient";
import { clientCredentialClient } from "../models/clientCredentialClient"

export function CreateApiClient(accountName: string, accountGuid: string, opsClient: OpsClient, logger: Logger): clientCredentialClient {
    const clientDetails = new clientCredentialClient();

    logger.info(`Checking account status of ${accountGuid}`);
    const request = new RootRequest(HttpVerb.GET, `/accountmgmt/v1/accounts?&Id=${accountGuid}`);
    const response = opsClient.request(request);
    checkApiResponse(response, 200, request, "Get accounts by Id");

    if (response.status !== 200) {
        logger.error(`Get Accounts by Id error: '${response.body}' (${response.status})`);
        return null;
    }

    const responseBody: OpsAccountMgmt.AccountCollectionResponse = JSON.parse(response.body);
    const itemsArray = responseBody.items;
    const targetGuid = accountGuid;
    const targetItem: OpsAccountMgmt.AccountResponse = itemsArray.find(item => item.id === targetGuid);

    if (targetItem.status == 'Active') {
        logger.debug(`Found item and is in active state: ${JSON.stringify(targetItem)}`);
        logger.info(`Creating new API client for account, ${accountGuid}`);

        const currentDate = new Date();
        const secretExpirationDate = new Date(currentDate);
        secretExpirationDate.setDate(currentDate.getDate() + 3); // 3 day expiration
        const formattedFutureDate = secretExpirationDate.toISOString();
        const typedFutureDate = new Date(formattedFutureDate);

        const newApiClientBody: OpsIdentity.ClientCredentialClientCreateRequest = {
            name: accountName + "_client",
            description: "API Client for account " + accountName + " used for performance testing.",
            secretDescription: "API client secret for account " + accountName,
            secretExpiration: typedFutureDate,
            accessTokenLifetimeSeconds: 3600,
            allowedScopes: ["api"]
        };

        const request = new RootRequest(HttpVerb.POST, `/account/${accountGuid}/identity/v1/clients/accountowners`, newApiClientBody);
        const response = opsClient.request(request);
        checkApiResponse(response, 201, request, "Add New Client to Account");

        if (response.status !== 201) {
            logger.error(`Add new Client to account error: '${response.body}' (${response.status})`);
            return null;
        }

        const newApiClientResponseBody: OpsIdentity.ClientCredentialClientCreateResponse = JSON.parse(response.body);

        if (newApiClientResponseBody.id != null) {
            logger.info(`new API Client created, Id is ${newApiClientResponseBody.id}`);
            clientDetails.apiClientId = newApiClientResponseBody.id;
            clientDetails.apiClientSecret = newApiClientResponseBody.createdSecret.value;

            logger.info(`Api client credentials created - client id is: ${clientDetails.apiClientId} and client secret is: ${clientDetails.apiClientSecret}`);
        }
        else {
            logger.error(`error creating API client`);
            return null;
        }


        // Need to wait to give time for the clients permissions to become active
        const startTime = new Date().getTime();

        // 10 Second wait
        while ((new Date().getTime() - startTime) < 10000) {
        }

        return clientDetails;
    }
    else {
        logger.error(`Item with guid ${targetGuid} not found`);
        return null;
    }
}

export function opsGetResponse(client: OpsClient, request: RootRequest, expectedStatus: number, checkName: string): any | undefined {

    const response = client.request(request);

    if (request.relativeUrl != undefined && request.relativeUrl.length > 200) {
        const requestShortUrl = new RootRequest(request.httpVerb, `${request.relativeUrl.substring(0, 197)}...`, request.payload, request.payloadType, request.authenticate, request.previewVersion);
        if (!checkApiResponse(response, expectedStatus, requestShortUrl, `${checkName} should have response code ${expectedStatus}`, LogLevel.Error)) {
            return undefined;
        }
    }
    else if (!checkApiResponse(response, expectedStatus, request, `${checkName} should have expected response code ${expectedStatus}`, LogLevel.Error)) {
        return undefined;
    }

    return response;
}

export function opsGetTypedResponse<ResponseType>(client: OpsClient, request: RootRequest, expectedStatus: number, checkName: string): ResponseType | undefined {
    const response = opsGetResponse(client, request, expectedStatus, checkName);
    return response != undefined ? JSON.parse(response.body) as ResponseType : undefined;
}

export function apiGetResponse(client: ApiClient, request: RootRequest, expectedStatus: number, checkName: string): any | undefined {

    const response = client.request(request);

    if (request.relativeUrl != undefined && request.relativeUrl.length > 200) {
        const requestShortUrl = new RootRequest(request.httpVerb, `${request.relativeUrl.substring(0, 197)}...`, request.payload, request.payloadType, request.authenticate, request.previewVersion);
        if (!checkApiResponse(response, expectedStatus, requestShortUrl, `${checkName} should have response code ${expectedStatus}`, LogLevel.Error)) {
            return undefined;
        }
    }
    else if (!checkApiResponse(response, expectedStatus, request, `${checkName} should have expected response code ${expectedStatus}`, LogLevel.Error)) {
        return undefined;
    }

    return response;
}

export function apiGetTypedResponse<ResponseType>(client: ApiClient, request: RootRequest, expectedStatus: number, checkName: string): ResponseType | undefined {
    const response = apiGetResponse(client, request, expectedStatus, checkName);
    return response != undefined ? JSON.parse(response.body) as ResponseType : undefined;
}