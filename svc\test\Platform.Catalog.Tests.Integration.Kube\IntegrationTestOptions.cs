﻿using System.Text.Json.Serialization;
using Microsoft.Extensions.Configuration;

namespace Aveva.Platform.Catalog.Tests.Integration.Kube;

internal sealed class IntegrationTestOptions
{
    public IntegrationTestOptions()
    {
        var builder = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .AddUserSecrets<IntegrationTestOptions>();
        var configuration = builder.Build();
        configuration.Bind(this);
    }

    /// <summary>
    /// The file name and path of the helm exe.
    /// </summary>
    public string HelmExecutable { get; init; } = "C:\\ProgramData\\chocolatey\\bin\\helm.exe";

    /// <summary>
    /// The file name and path of the helmfile exe.
    /// </summary>
    public string HelmfileExecutable { get; init; } = "C:\\ProgramData\\chocolatey\\bin\\helmfile.exe";

    /// <summary>
    /// The file name and path of the kubectl exe.
    /// </summary>
    public string KubectlExecutable { get; init; } = "C:\\ProgramData\\chocolatey\\bin\\kubectl.exe";

    /// <summary>
    /// The kube context to use if null use current context.
    /// </summary>
    public string? KubeContext { get; init; } = "minikube";

    public string DaprReleaseName { get; init; } = "dapr";
    public string DaprNamespace { get; init; } = "dapr-system";
    public string DaprRepoName { get; set; } = "dapr";
    public Uri DaprRepoUrl { get; set; } = new Uri("https://dapr.github.io/helm-charts/");
    public string CatalogReleaseName { get; init; } = "platform-catalog";
    public string CatalogReleaseNamespace { get; init; } = "platform-catalog";
    public string CatalogHelmfilePath { get; init; } = "../../../../../../local/helmfile.yaml";
    public string? ImageRegistry { get; init; }
    public string ImageTag { get; init; } = "latest";
    public string? DockerUsername { get; init; }
    [JsonIgnore]
    public string? DockerPassword { get; init; }

    public string TestDeploymentReleaseName { get; init; } = "catalog-int-test";
    public string TestDeploymentNamespace { get; init; } = "catalog-int-test";
    public int CatalogLocalPort { get; internal set; } = 8000;

    public string RabbitmqNamespace { get; } = "rabbitmq";

    public string RabbitmqPodName { get; } = "rabbitmq-0";

    public int RabbitmqAmqPort { get; } = 5672;

    public string RabbitmqHostName { get; } = "localhost";
    public string RabbitmqAdminUser { get; } = "fish";

    public string RabbitmqAdminSecret { get; } = "bowl";

    public List<string> CatalogEvents { get; } = new List<string>() { "CatalogServiceAddV1", "CatalogServiceUpdateV1", "CatalogServiceDeleteV1" };
}