﻿using System.Diagnostics.CodeAnalysis;
using Aveva.Platform.Authentication.Sdk.Server.Extensions;
using Aveva.Platform.Catalog.Domain.Serialization;
using Aveva.Platform.Catalog.Operator;
using Aveva.Platform.Common.Abstractions.Extensions;
using Aveva.Platform.Common.Framework.Abstractions.Extensions;
using KubeOps.Operator;

var builder = WebApplication.CreateBuilder(args);

// Register dependencies defined in service registration modules.
builder.Services
    .AddKubernetesOperator()
    .RegisterComponents();
builder.Services.AddOptions<OperatorSettings>()
    .Bind(builder.Configuration);
builder.Services.AddLogging();
builder.Services.AddOperatorHealthChecks();
builder.Services.AddDaprClient(clientBuilder => clientBuilder.UseJsonSerializationOptions(JsonSerializationOptions.Options));
builder.Services.AddControllers().AddJsonOptions(options => options.JsonSerializerOptions.ConfigureDefaults(enumsAsStrings: true));
builder.Services.AddServicesFromModules(builder.Configuration);

var app = builder.Build();
app.UseRouting();
app.MapControllers();
app.MapOperatorHealthChecks();

app.UsePlatformAuthentication();

await app.RunAsync().ConfigureAwait(false);

/// <summary>
/// Entry point and composition root for the application.
/// </summary>
[ExcludeFromCodeCoverage]
public sealed partial class Program
{
}