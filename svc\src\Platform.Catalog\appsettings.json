{"Application": {"EnableSwaggerUI": "false", "SwaggerServiceTitle": "Catalog Service", "SwaggerServiceDescription": "The Catalog service provides a comprehensive registry of available services within the CONNECT platform. It enables you to discover, explore, and understand the capabilities of services you can provision to your account.\n\nThis API gives you access to detailed information about each service, including its functionality, requirements, configuration options, and availability. You can browse the service catalog, view specific service details, and determine which services best meet your business needs.\n\nThe Catalog service works closely with Instance Management, which handles the actual provisioning of services to your account. While the Catalog provides information about available services, Instance Management is responsible for creating and managing instances of those services. Services with a lifecycle trigger of `Catalog` can be provisioned through Instance Management based on the information stored in the catalog.\n\nUse this API to explore the platform's capabilities, plan your service architecture, and make informed decisions about which services to implement in your solutions."}, "Authentication": {"ServiceId": "catalog", "IdentityDnsZoneName": "IdentityDnsZoneName"}, "Apis": {"AccountMgmtService": {"serviceIdentityId": "accountmgmt"}, "CatalogService": {"serviceIdentityId": "catalog", "BaseServiceUrl": "http://ni--catalog--api.platform-catalog", "GetAllServiceEntries": "ops/v2/services"}, "CatalogEventsService": {"serviceIdentityId": "catalog", "BaseServiceUrl": "http://ni--catalog--events.platform-catalog", "PostEvents": "internal/v1/eventtype/"}, "InstanceMgmtService": {"serviceIdentityId": "instancemgmt"}}, "Instrumentation": {"DebugEnabled": "false", "RoleName": "catalog-api", "ServiceName": "catalog"}}