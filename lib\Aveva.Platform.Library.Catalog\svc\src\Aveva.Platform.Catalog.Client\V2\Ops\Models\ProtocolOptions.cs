// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Ops.Models
{
    /// <summary>
    /// Composed type wrapper for classes <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolOptions"/>, <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Models.WebhookProtocolOptions"/>
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class ProtocolOptions : IComposedTypeWrapper, IParsable
    {
        /// <summary>Composed type representation for type <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolOptions"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolOptions? LegacyProtocolOptions { get; set; }
#nullable restore
#else
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolOptions LegacyProtocolOptions { get; set; }
#endif
        /// <summary>Composed type representation for type <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Models.WebhookProtocolOptions"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.WebhookProtocolOptions? WebhookProtocolOptions { get; set; }
#nullable restore
#else
        public global::Aveva.Platform.Catalog.Client.V2.Ops.Models.WebhookProtocolOptions WebhookProtocolOptions { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ProtocolOptions"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ProtocolOptions CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            var mappingValue = parseNode.GetChildNode("")?.GetStringValue();
            var result = new global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ProtocolOptions();
            if("LegacyProtocolOptions".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
            {
                result.LegacyProtocolOptions = new global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolOptions();
            }
            else if("WebhookProtocolOptions".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
            {
                result.WebhookProtocolOptions = new global::Aveva.Platform.Catalog.Client.V2.Ops.Models.WebhookProtocolOptions();
            }
            return result;
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            if(LegacyProtocolOptions != null)
            {
                return LegacyProtocolOptions.GetFieldDeserializers();
            }
            else if(WebhookProtocolOptions != null)
            {
                return WebhookProtocolOptions.GetFieldDeserializers();
            }
            return new Dictionary<string, Action<IParseNode>>();
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            if(LegacyProtocolOptions != null)
            {
                writer.WriteObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.LegacyProtocolOptions>(null, LegacyProtocolOptions);
            }
            else if(WebhookProtocolOptions != null)
            {
                writer.WriteObjectValue<global::Aveva.Platform.Catalog.Client.V2.Ops.Models.WebhookProtocolOptions>(null, WebhookProtocolOptions);
            }
        }
    }
}
#pragma warning restore CS0618
