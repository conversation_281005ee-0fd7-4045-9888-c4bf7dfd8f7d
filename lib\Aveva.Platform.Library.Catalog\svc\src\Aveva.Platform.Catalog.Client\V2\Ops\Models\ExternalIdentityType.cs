// <auto-generated/>
using System.Runtime.Serialization;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Ops.Models
{
    /// <summary>Defines the supported external identity providers that can be used for authentication and authorization with services.</summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public enum ExternalIdentityType
    {
        [EnumMember(Value = "AvevaRnDEntraID")]
        #pragma warning disable CS1591
        AvevaRnDEntraID,
        #pragma warning restore CS1591
    }
}
