﻿using System.Diagnostics;

namespace Aveva.Platform.Catalog.Domain.Instrumentation
{
    /// <summary>
    /// A class to create the different activity sources for Catalog service.
    /// </summary>
    public static class CatalogTraceSource
    {
        /// <summary>
        /// Name of Catalog API Activity Source.
        /// </summary>
        public const string ApiTraceName = "Aveva.Platform.Catalog.Api";

        /// <summary>
        /// Name of Catalog API K8s custom Watcher Activity Source.
        /// </summary>
        public const string WatcherTraceName = "Aveva.Platform.Catalog.Watcher";

        /// <summary>
        /// Name of Catalog Operator Activity Source.
        /// </summary>
        public const string OperatorTraceName = "Aveva.Platform.Catalog.Operator";

        /// <summary>
        /// Name of Catalog Events Activity Source.
        /// </summary>
        public const string EventsTraceName = "Aveva.Platform.Catalog.Events";

        /// <summary>
        /// ActivitySource to be used by Catalog API.
        /// </summary>
        public static readonly ActivitySource ApiTrace = new ActivitySource(ApiTraceName, "1.0.0");

        /// <summary>
        /// ActivitySource to be used by Catalog API K8s custom Watcher.
        /// </summary>
        public static readonly ActivitySource WatcherTrace = new ActivitySource(WatcherTraceName, "1.0.0");

        /// <summary>
        /// ActivitySource to be used by Catalog K8s Operator service.
        /// </summary>
        public static readonly ActivitySource OperatorTrace = new ActivitySource(OperatorTraceName, "1.0.0");

        /// <summary>
        /// ActivitySource to be used by Catalog Events service.
        /// </summary>
        public static readonly ActivitySource EventsTrace = new ActivitySource(EventsTraceName, "1.0.0");
    }
}