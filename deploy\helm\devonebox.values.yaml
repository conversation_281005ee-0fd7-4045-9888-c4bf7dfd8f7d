deploymentMode: devonebox

# -- Dapr Configuration
dapr:
  # -- Enables dapr which is required to talk to services in the cluster for example InstMgmt and Accounts
  enabled: true
  cacheStateStore:
    # -- Enables the dapr cache state store.
    # This needs to be enabled when using any Resolver and Cache that requires a dapr state store.
    # Useful to disable locally and test independently of dapr.
    enabled: false
  envPubSub:
    # -- Enables the environment pubsub
    # This needs to be enabled when events.enabled true otherwise the component it requires will not be available.
    enabled: true
    namespaceName: "helloworld.servicebus.windows.net"
    consumerId: "catalog"
    name: environment-pubsub

# -- Catalog Api service Configuration
api:
  runAsNonRoot: false
  image:
    pullPolicy: IfNotPresent
  scaling:
    maxReplicas: 1
  readinessProbe:
    periodSeconds: 120
  env:
    Instrumentation__DebugEnabled: "false"
    Instrumentation__OtelExporterEndpoint: "http://jaeger-all-in-one.jaeger.svc.cluster.local:4317"

# -- Catalog events service Configuration
events:
  runAsNonRoot: false
  image:
    pullPolicy: IfNotPresent
  scaling:
    maxReplicas: 1
  env:
    Instrumentation__DebugEnabled: "false"
    Instrumentation__OtelExporterEndpoint: "http://jaeger-all-in-one.jaeger.svc.cluster.local:4317"

# -- Catalog Operator Configuration
operator:
  timeoutSeconds: 30
  runAsNonRoot: false
  dapr:
    sidecarCpuRequest: "10m"
    sidecarCpuLimit: "150m"
    sidecarMemoryRequest: "16Mi"
    sidecarMemoryLimit: "256Mi"
  image:
    tag: latest
    pullPolicy: IfNotPresent
  env:
    # -- The port to use for the local service routing.
    LocalServicePort: "80"
    Authentication__SkipAuthenticationIfNotSupplied: true
    Authentication__OverrideAuthenticationForLocalTesting: true
    Instrumentation__DebugEnabled: "false"
    Instrumentation__OtelExporterEndpoint: "http://jaeger-all-in-one.jaeger.svc.cluster.local:4317"
  resources:
    limits:
      cpu: 75m
      memory: 256Mi
    requests:
      cpu: 25m
      memory: 64Mi

buildVariables:
  tag: latest

environment:
  identityDnsZoneName: "https://devonebox.platform.capdev-connect.aveva.com"
  environmentType: "Development"

scaling:
  cpuRequested: 25m
  cpuLimit: 75m
  memoryRequested: 64Mi
  memoryLimit: 256Mi

# -- EDA pipeline configuration
# -- Certain variable are expected as part of the EDA pipeline.
# -- The following values are the same as instance management so events generated by catalog are corretly processed
geography:
  name: "eu"
region:
  geography: "eu"
  region: "northeurope"
  regioncode: "euno"

instance:
  workloadIdentity: workloadIdentityClientId