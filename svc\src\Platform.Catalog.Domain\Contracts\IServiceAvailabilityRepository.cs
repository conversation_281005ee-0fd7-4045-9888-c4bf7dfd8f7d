﻿using Aveva.Platform.Catalog.Domain.Models;

namespace Aveva.Platform.Catalog.Domain.Contracts;

/// <summary>
/// Interface to implement ServiceAvailabilityRepository.
/// </summary>
public interface IServiceAvailabilityRepository
{
    /// <summary>
    /// Retrieves the availability for a service entry for a given account.
    /// </summary>
    /// <param name="service">The serviceEntry.</param>
    /// <param name="accountAvailabilities">The service availability overrides from the account.</param>
    /// <returns>The availability of the serviceEntry for the given account.</returns>
    V1ServiceAvailability? GetForEntry(V1ServiceEntry service, Dictionary<string, V1ServiceAvailability> accountAvailabilities);

    /// <summary>
    /// Retrieves the availability for a list of service entries for a given account.
    /// </summary>
    /// <param name="services">The serviceEntry.</param>
    /// <param name="accountAvailabilities">The service availability overrides from the account.</param>
    /// <returns>A dictionary of the availability of the serviceEntries for the given account.</returns>
    Dictionary<V1ServiceEntry, V1ServiceAvailability?> GetForEntries(IEnumerable<V1ServiceEntry> services, Dictionary<string, V1ServiceAvailability> accountAvailabilities);
}