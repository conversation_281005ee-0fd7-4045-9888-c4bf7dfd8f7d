﻿apiVersion: servicecatalog.aveva.com/v1
kind: ServiceEntry
metadata:
  name: account-shared
  namespace: '{{ .Release.Namespace }}'
spec:
  id: account-shared
  displayName: Access Management
  hostingType: Geography
  iconUrl: https://cdn.aveva.design/icons/svg/outlined/32/content/content--library.svg
  description: Description
  lifecycle:
    trigger: Account
    instanceMode: Shared
    protocol: IntegrationEvent
---
apiVersion: servicecatalog.aveva.com/v1
kind: ServiceEntry
metadata:
  name: lifecycle-none
  namespace: '{{ .Release.Namespace }}'
spec:
  id: lifecycle-none
  displayName: Connect V2 Accounts Mapping Service
  hostingType: Environment
  iconUrl: https://cdn.aveva.design/icons/svg/outlined/32/content/content--library.svg
  lifecycle:
    trigger: None
---
apiVersion: servicecatalog.aveva.com/v1
kind: ServiceEntry
metadata:
  name: catalog-isolated
  namespace: '{{ .Release.Namespace }}'
spec:
  id: catalog-isolated
  displayName: Data Views
  hostingType: Regional
  description: Here
  lifecycle:
    trigger: Catalog
    protocol: IntegrationEvent
    providerId: Kubernetes
    instanceMode: Isolated
  dependencies:
    lifecycle-none:
      type: Required
      cardinality: One
      colocated: True
      config:
        context:
          label: Context
          help: 'Context Identifier'
          min: 0
          max: 100
---
apiVersion: servicecatalog.aveva.com/v1
kind: ServiceEntry
metadata:
  name: service-with-apps
  namespace: '{{ .Release.Namespace }}'
spec:
  id: service-with-apps
  displayName: Service With Apps
  hostingType: Regional
  description: Description
  lifecycle:
    trigger: Catalog
    instanceMode: Shared
  applications:
  - name: Manager
  - name: Monitoring
    urls:
      default: 'https://online.wonderware.com/authentication/dispatch?sid={{"{{"}} instance.uniqueId {{"}}"}}'
      us: 'https://online.wonderware.com/authentication/dispatch?sid={{"{{"}} instance.uniqueId {{"}}"}}'
      eu: 'https://online.wonderware.eu/authentication/dispatch?sid={{"{{"}} instance.uniqueId {{"}}"}}'
      au: 'https://online.wonderware.net.au/authentication/dispatch?sid={{"{{"}} instance.uniqueId {{"}}"}}'