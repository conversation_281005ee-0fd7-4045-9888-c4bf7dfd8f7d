﻿using AutoMapper;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.Models;

namespace Aveva.Platform.Catalog.Domain.Mapping
{
    internal class V1ProtocolOptionsResolver : IValueResolver<Lifecycle, V1Lifecycle, V1ProtocolOptions?>
    {
        public V1ProtocolOptions? Resolve(Lifecycle source, V1Lifecycle destination, V1ProtocolOptions? destMember, ResolutionContext context)
        {
            if (source.ProtocolOptions == null)
            {
                return null;
            }

            return source.Protocol?.ToUpperInvariant() switch
            {
                "WEBHOOK" => new V1ProtocolOptions
                {
                    WebhookUri = ((WebhookProtocolOptions)source.ProtocolOptions).WebhookUri!,
                },
                "LEGACY" => new V1ProtocolOptions
                {
                    SolutionDefinition = ((LegacyProtocolOptions)source.ProtocolOptions).SolutionDefinition,
                    Mappings = ((LegacyProtocolOptions)source.ProtocolOptions).Mappings != null
                        ? context.Mapper.Map<V1LegacyProtocolMappings>(((LegacyProtocolOptions)source.ProtocolOptions).Mappings)
                        : null,
                },
                _ => null,
            };
        }
    }
}