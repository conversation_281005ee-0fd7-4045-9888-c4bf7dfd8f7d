#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.
FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS final

RUN echo "Removing non-essential libraries" \
    && apk --purge del \
    libc-utils \
    scanelf \
    apk-tools

# Create group and user with a specific numeric UID
RUN addgroup -g 1001 k8s-operator && adduser -u 1001 --disabled-password -G k8s-operator operator-user

WORKDIR /operator
COPY svc/src/Platform.Catalog.Operator/publish .
RUN chown operator-user:k8s-operator -R .

# Switch to the user by UID
USER 1001

EXPOSE 8080
EXPOSE 8443
ENTRYPOINT ["dotnet", "Aveva.Platform.Catalog.Operator.dll"]