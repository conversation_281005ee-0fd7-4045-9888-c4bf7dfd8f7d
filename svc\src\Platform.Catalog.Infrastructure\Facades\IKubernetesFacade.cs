﻿using Aveva.Platform.Catalog.Infrastructure.Entities;

namespace Aveva.Platform.Catalog.Infrastructure.Facades
{
    /// <summary>
    /// Facade interface for Kubernetes operations.
    /// </summary>
    public interface IKubernetesFacade
    {
        /// <summary>
        /// Asynchronously lists all service entries in the Kubernetes cluster.
        /// </summary>
        /// <returns>V1K8sServiceEntryList.</returns>
        Task<V1K8sServiceEntryList> ListServiceEntriesAsync();
    }
}