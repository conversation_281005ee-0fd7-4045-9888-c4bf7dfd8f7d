﻿using AutoMapper;

namespace Aveva.Platform.Catalog.Domain.Mapping
{
    /// <summary>
    /// Enum to string converter.
    /// </summary>
    /// <typeparam name="T">An enum type.</typeparam>
    public class EnumToStringConverter<T> : ITypeConverter<T, string> where T : System.Enum
    {
        /// <summary>
        /// Convert.
        /// </summary>
        /// <returns>String value of enum.</returns>
        public string Convert(T source, string destination, ResolutionContext context)
        {
            return source.ToString();
        }
    }
}