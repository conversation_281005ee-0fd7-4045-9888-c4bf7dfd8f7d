﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2
{
    /// <summary>
    /// Contains protocol-specific configuration options, including any mappings needed for backward compatibility with legacy systems and to provide support for webhooks.
    /// These options control how the lifecycle protocol interacts with other systems during provisioning operations.
    /// For Legacy protocol, this will be a <see cref="LegacyProtocolOptions"/>.
    /// For Webhook protocol, this will be a <see cref="WebhookProtocolOptions"/>.
    /// </summary>
    public abstract class ProtocolOptions
    {
    }
}