apiVersion: chaos-mesh.org/v1alpha1
kind: Schedule
metadata:
  name: schedule-network-delay
  namespace: chaos-mesh
spec:
  schedule: "@every 2m"
  type: "NetworkChaos"
  historyLimit: 5          
  concurrencyPolicy: Forbid
  networkChaos:
    action: delay
    mode: all
    selector:
      namespaces:
        - platform-catalog
      labelSelectors:
        pod-selector: catalog-api
    delay:
      latency: '2s'
      correlation: '50'
    duration: "2m"
