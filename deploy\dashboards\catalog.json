{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 106, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 23, "panels": [], "title": "Catalog custom metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 44, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "avg by(region) (catalog_serviceentries_count{aveva_platform_service_name=\"catalog\"})", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{region}}", "range": true, "refId": "A", "useBackend": false}], "title": "ServiceEntries Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "red", "mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "avg by(aveva_platform_service_name) (catalog_k8swatcher_status_on_off)", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Catalog Watcher Status", "transformations": [{"id": "renameByRegex", "options": {"regex": "account(.*)failed", "renamePattern": "account $1 failed"}}], "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "series", "axisGridShow": true, "axisLabel": "Time (ms)", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "avg by(catalog_event) (rate(catalog_serviceentries_eventsprocessingtime_time_ms_total{aveva_platform_service_name=\"catalog\", job=\"catalog-events\"}[$__rate_interval]))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "ServiceEntry Event ProcessingTime (ms)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "series", "axisGridShow": true, "axisLabel": "Event Count", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 19, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum by(catalog_event, catalog_eventstatus, region) (changes(catalog_serviceentries_events_count_total{service_name=\"catalog-events\"}[$__rate_interval]))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{catalog_eventstatus}}-{{catalog_event}}", "range": true, "refId": "A", "useBackend": false}], "title": "ServiceEntry Events", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 15, "panels": [], "title": "API", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 11, "x": 0, "y": 26}, "id": 12, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum(increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-api\", http_route!~\"/liveness|/readiness|/startup\", http_route != \"\", http_response_status_code=~\"2..\"}[5m]))", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "API Request Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": 900000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 13, "x": 11, "y": 26}, "id": 14, "options": {"legend": {"calcs": ["mean"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum(increase(http_server_request_duration_seconds_sum{aveva_platform_service_name=\"catalog\", service_name=\"catalog-api\", http_route!~\"/liveness|/readiness|/startup\", http_response_status_code=\"200\"}[$__rate_interval])) / sum(increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-api\", http_route!~\"/liveness|/readiness|/startup\", http_response_status_code=\"200\"}[$__rate_interval]))", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "Avg Request Duration", "range": true, "refId": "A", "useBackend": false}], "title": "API Average Request Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 21, "x": 0, "y": 34}, "id": 46, "interval": "30s", "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum by (http_response_status_code,http_route)(increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-api\",http_route!~\"/liveness|/readiness|/startup\", http_route != \"\", http_response_status_code !~\"2..\"}[$__rate_interval]))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "legendFormat": "{{http_response_status_code}}-{{http_route}}", "range": true, "refId": "A", "useBackend": false}], "title": "API Requests Failures", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 42}, "id": 48, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.4.11", "targets": [{"datasource": {"type": "prometheus", "uid": "amws-infradev-eu"}, "editorMode": "code", "expr": "sum(kube_horizontalpodautoscaler_status_current_replicas{namespace=\"platform-catalog\", horizontalpodautoscaler=\"catalog-api-hpa\"})", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "API Pods Count", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "palette-classic", "seriesBy": "last"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"from": 200, "result": {"color": "green", "index": 0}, "to": 299}, "type": "range"}, {"options": {"from": 300, "result": {"color": "yellow", "index": 1}, "to": 499}, "type": "range"}, {"options": {"from": 500, "result": {"color": "red", "index": 2}, "to": 599}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 3, "y": 42}, "id": 5, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(http_response_status_code) (increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-api\", http_route=\"/liveness\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{http_response_status_code}}", "range": true, "refId": "A"}], "title": "API Liveness", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "palette-classic", "seriesBy": "last"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"from": 200, "result": {"color": "green", "index": 0}, "to": 299}, "type": "range"}, {"options": {"from": 300, "result": {"color": "yellow", "index": 1}, "to": 499}, "type": "range"}, {"options": {"from": 500, "result": {"color": "red", "index": 2}, "to": 599}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 11, "y": 42}, "id": 6, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(http_response_status_code) (increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-api\", http_route=\"/readiness\"}[$__rate_interval]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "API Readiness", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "palette-classic", "seriesBy": "last"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"from": 200, "result": {"color": "green", "index": 0}, "to": 299}, "type": "range"}, {"options": {"from": 300, "result": {"color": "yellow", "index": 1}, "to": 499}, "type": "range"}, {"options": {"from": 500, "result": {"color": "red", "index": 2}, "to": 599}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 42}, "id": 7, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(http_response_status_code) (increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-api\", http_route=\"/startup\"}[$__rate_interval]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "API Startup", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 30, "panels": [], "title": "Events API", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 51}, "id": 52, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "amws-eucliddev-us"}, "editorMode": "code", "expr": "sum(increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-events\", http_route!~\"/liveness|/readiness|/startup\", http_route != \"\", http_response_status_code=~\"2..\"}[5m]))", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Events API Request Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": 900000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 51}, "id": 32, "options": {"legend": {"calcs": ["mean"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum(increase(http_server_request_duration_seconds_sum{aveva_platform_service_name=\"catalog\", service_name=\"catalog-events\", http_route!~\"/liveness|/readiness|/startup\", http_response_status_code=\"202\"}[$__rate_interval])) / sum(increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-events\", http_route!~\"/liveness|/readiness|/startup\", http_response_status_code=\"202\"}[$__rate_interval]))", "legendFormat": "Avg Request Duration", "range": true, "refId": "A"}], "title": "Events API Average Request Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 18, "x": 0, "y": 59}, "id": 53, "options": {"legend": {"calcs": ["sum"], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "amws-eucliddev-us"}, "editorMode": "code", "expr": "sum by (http_response_status_code,http_route)(increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-events\",http_route!~\"/liveness|/readiness|/startup\", http_route != \"\", http_response_status_code !~\"2..\"}[$__rate_interval]))", "instant": false, "legendFormat": "{{http_response_status_code}}-{{http_route}}", "range": true, "refId": "A"}], "title": "Events API Requests Failures", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "green", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 65}, "id": 49, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.4.11", "targets": [{"datasource": {"type": "prometheus", "uid": "amws-infradev-eu"}, "editorMode": "code", "expr": "sum(kube_horizontalpodautoscaler_status_current_replicas{namespace=\"platform-catalog\", horizontalpodautoscaler=\"catalog-events-hpa\"})", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Events API Pods Count", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "palette-classic", "seriesBy": "last"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"from": 200, "result": {"color": "green", "index": 0}, "to": 299}, "type": "range"}, {"options": {"from": 300, "result": {"color": "yellow", "index": 1}, "to": 499}, "type": "range"}, {"options": {"from": 500, "result": {"color": "red", "index": 2}, "to": 599}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 3, "y": 65}, "id": 34, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(http_response_status_code) (increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-events\", http_route=\"/liveness\"}[$__rate_interval]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Events Liveness", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "palette-classic", "seriesBy": "last"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"from": 200, "result": {"color": "green", "index": 0}, "to": 299}, "type": "range"}, {"options": {"from": 300, "result": {"color": "yellow", "index": 1}, "to": 499}, "type": "range"}, {"options": {"from": 500, "result": {"color": "red", "index": 2}, "to": 599}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 9, "y": 65}, "id": 43, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(http_response_status_code) (increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-events\", http_route=\"/readiness\"}[$__rate_interval]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Events Readiness", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "palette-classic", "seriesBy": "last"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"from": 200, "result": {"color": "green", "index": 0}, "to": 299}, "type": "range"}, {"options": {"from": 300, "result": {"color": "yellow", "index": 1}, "to": 499}, "type": "range"}, {"options": {"from": 500, "result": {"color": "red", "index": 2}, "to": 599}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 7, "x": 17, "y": 65}, "id": 36, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(http_response_status_code) (increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-events\", http_route=\"/startup\"}[$__rate_interval]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Events Startup", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 72}, "id": 37, "panels": [], "title": "Operator ", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 73}, "id": 38, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum(increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-operator\", http_route!~\"/liveness|/readiness|/startup\", http_route != \"\", http_response_status_code=~\"2..\"}[5m]))", "legendFormat": "{{http_response_status_code}}-{{http_route}}", "range": true, "refId": "A"}], "title": "Operator Request Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": 900000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 73}, "id": 41, "options": {"legend": {"calcs": ["mean"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum(increase(http_server_request_duration_seconds_sum{aveva_platform_service_name=\"catalog\", service_name=\"catalog-operator\", http_route!~\"/liveness|/readiness|/startup\", http_response_status_code=\"200\"}[$__rate_interval])) / sum(increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-operator\", http_route!~\"/liveness|/readiness|/startup\", http_response_status_code=\"200\"}[$__rate_interval]))", "legendFormat": "Avg Request Duration", "range": true, "refId": "A"}], "title": "Operator Average Request Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 18, "x": 0, "y": 81}, "id": 54, "options": {"legend": {"calcs": ["sum"], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "amws-eucliddev-us"}, "editorMode": "code", "expr": "sum by (http_response_status_code,http_route)(increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-operator\",http_route!~\"/liveness|/readiness|/startup\", http_route != \"\", http_response_status_code!~\"2..\"}[$__rate_interval]))", "instant": false, "legendFormat": "{{http_response_status_code}}-{{http_route}}", "range": true, "refId": "A"}], "title": "Operator Request Failures", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "palette-classic", "seriesBy": "last"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"from": 200, "result": {"color": "green", "index": 0}, "to": 299}, "type": "range"}, {"options": {"from": 300, "result": {"color": "yellow", "index": 1}, "to": 499}, "type": "range"}, {"options": {"from": 500, "result": {"color": "red", "index": 2}, "to": 599}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 89}, "id": 35, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(http_response_status_code) (increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-operator\", http_route=\"/readiness\"}[$__rate_interval]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Readiness", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "palette-classic", "seriesBy": "last"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [{"options": {"from": 200, "result": {"color": "green", "index": 0}, "to": 299}, "type": "range"}, {"options": {"from": 300, "result": {"color": "yellow", "index": 1}, "to": 499}, "type": "range"}, {"options": {"from": 500, "result": {"color": "red", "index": 2}, "to": 599}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 89}, "id": 39, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum by(http_response_status_code) (increase(http_server_request_duration_seconds_count{aveva_platform_service_name=\"catalog\", service_name=\"catalog-operator\", http_route=\"/liveness\"}[$__rate_interval]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Liveness", "type": "timeseries"}], "refresh": "", "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "Managed_Prometheus_amws-eucliddev-us", "value": "amws-eucliddev-us"}, "hide": 2, "includeAll": false, "multi": false, "name": "all_datasources", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": false, "text": "us", "value": "us"}, "datasource": {"type": "prometheus", "uid": "${all_datasources}"}, "definition": "query_result(count(aveva_environment_info) by (environment_geography))", "hide": 0, "includeAll": false, "label": "Geography", "multi": false, "name": "geography", "options": [], "query": {"qryType": 3, "query": "query_result(count(aveva_environment_info) by (environment_geography))", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "/environment_geography=\"(?<text>[^\"]+)\"/g", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "Managed_Prometheus_amws-eucliddev-us", "value": "amws-eucliddev-us"}, "hide": 2, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "/.*-${geography}/", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "utc", "title": "catalog", "uid": "25c2f812-bb6a-44ef-8160-9dce2801e63f", "version": 35, "weekStart": ""}