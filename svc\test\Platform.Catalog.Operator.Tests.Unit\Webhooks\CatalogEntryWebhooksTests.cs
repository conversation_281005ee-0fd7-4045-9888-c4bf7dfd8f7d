﻿using System.Diagnostics.CodeAnalysis;
using Aveva.Platform.AccountMgmt.Client.Ops.Models;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Operator.Webhooks;
using Aveva.Platform.Catalog.ServiceClient;
using Aveva.Platform.Catalog.ServiceClient.AccountMgmt;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.InstanceMgmt.Client.Ops.Models;
using Aveva.Platform.InstanceMgmt.Client.Ops.Ops.InstanceMgmt.V1.Instances;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Shouldly;
using Xunit;

[assembly: SuppressMessage("Naming", "CA1716:Identifiers should not match keywords", Justification = "Its an operator and this is not a library used cross language", Scope = "module")]
namespace Aveva.Platform.Catalog.Operator.Tests.Unit.Webhooks;

/// <summary>
/// <see cref="V1ServiceEntryValidator"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Unit")]
[Trait("Category", "Operator")]
[Trait("Category", "Operator.Unit")]
[Trait("Tag", "Webhooks")]
public static class CatalogEntryWebhooksTests
{
    #region Test Cases

    [Fact]
    public static void ServiceEntryValidator_CreateNull_Throws()
    {
        // Arrange
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
        var subject = new V1ServiceEntryValidator(null, null, null, null, null, null);
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
        Entities.V1ServiceEntry? newEntry = null;

        // Act and Assert
        subject.CreateAsync(newEntry!, false, CancellationToken.None).ShouldThrow<ArgumentNullException>();
    }

    [Fact]
    public static void ServiceEntryValidator_UpdateNull_Throws()
    {
        // Arrange
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
        var subject = new V1ServiceEntryValidator(null, null, null, null, null, null);
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
        Entities.V1ServiceEntry? oldEntry = null;
        Entities.V1ServiceEntry? newEntry = null;

        // Act and Assert
        subject.UpdateAsync(oldEntry!, newEntry!, false, CancellationToken.None).ShouldThrow<ArgumentNullException>();
    }

    [Fact]
    public static async Task ServiceEntryValidator_Create_Succeeds()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, true),
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.CreateAsync(newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Warnings.Count.ShouldBe(0);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Create_WithGeographies_Succeeds()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.External,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, true),
            Geographies = [new V1Geography { Id = "eu" }],
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        accountMgmtClientMock.Setup(x => x.QueryGeographiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([new GeographyResponse { Id = "eu" }]);
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.CreateAsync(newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Warnings.Count.ShouldBe(0);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Create_Throws()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Lifecycle = new V1Lifecycle()
            {
                Trigger = V1Trigger.Account,
                ProviderId = "ShouldFail",
            },
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).Throws<NullReferenceException>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.CreateAsync(newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Status!.Code.ShouldBe(500);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Create_WithInvalidGeographies_Fails()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.External,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, true),
            Geographies = [new V1Geography { Id = "us" }],
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        accountMgmtClientMock.Setup(x => x.QueryGeographiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.CreateAsync(newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Status!.Code.ShouldBe(400);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Create_WithGeographies_WithWrongHostingType_Fails()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, true),
            Geographies = [new V1Geography { Id = "us" }],
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.CreateAsync(newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Status!.Code.ShouldBe(400);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_Succeeds()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };

        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "oldEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Warnings.Count.ShouldBe(0);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_AddGeography_Succeeds()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Geographies = [new V1Geography { Id = "eu" }, new V1Geography { Id = "us" }],
        };

        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "oldEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Geographies = [new V1Geography { Id = "eu" }],
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        accountMgmtClientMock.Setup(x => x.QueryGeographiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([new GeographyResponse { Id = "eu" }, new GeographyResponse { Id = "us" }]);
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Warnings.Count.ShouldBe(0);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_RemoveGeography_Succeeds()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Geographies = [new V1Geography { Id = "eu" }],
        };

        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "oldEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Geographies = [new V1Geography { Id = "eu" }, new V1Geography { Id = "us" }],
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        accountMgmtClientMock.Setup(x => x.QueryGeographiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([new GeographyResponse { Id = "eu" }, new GeographyResponse { Id = "us" }]);
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        instanceMgmtClientMock.Setup(x => x.GetInstancesAsync(It.IsAny<InstancesRequestBuilder.InstancesRequestBuilderGetQueryParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Warnings.Count.ShouldBe(0);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_Throws()
    {
        // Arrange
        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Lifecycle = new V1Lifecycle()
            {
                Trigger = V1Trigger.Account,
            },
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).Throws<NullReferenceException>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Status!.Code.ShouldBe(500);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_EnabledFalseWithExistingInstance_Fails()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Availability = new V1ServiceAvailability
            {
                Enabled = false,
            },
        };

        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "oldEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        instanceMgmtClientMock
            .Setup(x => x.GetInstancesAsync(It.IsAny<InstancesRequestBuilder.InstancesRequestBuilderGetQueryParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([new InstanceResponse
                {
                    Id = "instance-1",
                    ServiceId = "1",
                }
            ]);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Status!.Code.ShouldBe(400);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_AddInvalidGeography_Fails()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Geographies = [new V1Geography { Id = "eu" }, new V1Geography { Id = "us" }],
        };

        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "oldEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Geographies = [new V1Geography { Id = "eu" }],
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        accountMgmtClientMock.Setup(x => x.QueryGeographiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([new GeographyResponse { Id = "eu" }]);
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Status!.Code.ShouldBe(400);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_RemoveGeography_Fails()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Geographies = [new V1Geography { Id = "eu" }],
        };

        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "oldEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Geographies = [new V1Geography { Id = "eu" }, new V1Geography { Id = "us" }],
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        accountMgmtClientMock.Setup(x => x.QueryGeographiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([new GeographyResponse { Id = "eu" }, new GeographyResponse { Id = "us" }]);
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        instanceMgmtClientMock.Setup(x => x.GetInstancesAsync(It.IsAny<InstancesRequestBuilder.InstancesRequestBuilderGetQueryParameters>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([new InstanceResponse()]);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Status!.Code.ShouldBe(400);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WithInvalidGeographies_Fails()
    {
        // Arrange
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Geographies = [new V1Geography { Id = "eu" }, new V1Geography { Id = "us" }],
        };

        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "oldEntry",
            HostingType = V1HostingType.External,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Geographies = [new V1Geography { Id = "eu" }],
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        accountMgmtClientMock.Setup(x => x.QueryGeographiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([new GeographyResponse { Id = "eu" }]);
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Warnings.Count.ShouldBe(0);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenLegacyProtocolOptionsModified_Fails()
    {
        // Arrange
        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dep1",
                    new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            {
                                "sourcecontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Source Context",
                                    Help = "The source context configuration for the dependency",
                                }
                            },
                            {
                                "targetcontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Target Context",
                                    Help = "The target context configuration for the dependency",
                                }
                            },
                        },
                    }
                },
            },
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition1",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Applications =
                        [
                            new V1LegacyProtocolMappingsApplication
                            {
                                Name = "app1",
                                CapabilityDefinition = "capdef1",
                            },
                        ],
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>()
                        {
                            {
                                    "dep1",
                                    new V1LegacyProtocolDependencyMapping
                                    {
                                        IntegrationDefinition = "int1",
                                        SourceContextConfig = "sourcecontext",
                                        TargetContextConfig = "targetcontext",
                                    }
                            },
                        },
                        Geographies = new Dictionary<string, string>()
                        {
                            { "default", "geo1" },
                        },
                    },
                },
                false),
        };
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dep1",
                    new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            {
                                "sourcecontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Source Context",
                                    Help = "The source context configuration for the dependency",
                                }
                            },
                            {
                                "targetcontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Target Context",
                                    Help = "The target context configuration for the dependency",
                                }
                            },
                        },
                    }
                },
            },
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition1",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Applications =
                        [
                            new V1LegacyProtocolMappingsApplication
                            {
                                Name = "app1",
                                CapabilityDefinition = "capdef2",
                            },
                        ],
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>()
                        {
                            {
                                    "dep1",
                                    new V1LegacyProtocolDependencyMapping
                                    {
                                        IntegrationDefinition = "int1",
                                        SourceContextConfig = "sourcecontext",
                                        TargetContextConfig = "targetcontext",
                                    }
                            },
                        },
                        Geographies = new Dictionary<string, string>()
                        {
                            { "default", "geo2" },
                        },
                    },
                },
                false),
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(new ServiceCollectionResponse
        {
            Items = [new ServiceResponse { Id = "dep1" }],
        });
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Status!.Code.ShouldBe(400);
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenChangedToLegacyProtocol_Succeeds()
    {
        // Arrange
        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dep1",
                    new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            {
                                "sourcecontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Source Context",
                                    Help = "The source context configuration for the dependency",
                                }
                            },
                            {
                                "targetcontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Target Context",
                                    Help = "The target context configuration for the dependency",
                                }
                            },
                        },
                    }
                },
            },
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.IntegrationEvent,
                "provider",
                V1InstanceMode.Shared,
                null,
                false),
        };
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dep1",
                    new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            {
                                "sourcecontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Source Context",
                                    Help = "The source context configuration for the dependency",
                                }
                            },
                            {
                                "targetcontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Target Context",
                                    Help = "The target context configuration for the dependency",
                                }
                            },
                        },
                    }
                },
            },
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition1",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Applications =
                        [
                            new V1LegacyProtocolMappingsApplication
                            {
                                Name = "app1",
                                CapabilityDefinition = "capdef1",
                            },
                        ],
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>()
                        {
                            {
                                    "dep1",
                                    new V1LegacyProtocolDependencyMapping
                                    {
                                        IntegrationDefinition = "int1",
                                        SourceContextConfig = "sourcecontext",
                                        TargetContextConfig = "targetcontext",
                                    }
                            },
                        },
                        Geographies = new Dictionary<string, string>()
                        {
                            { "default", "geo1" },
                        },
                    },
                },
                false),
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(new ServiceCollectionResponse
        {
            Items = [new ServiceResponse { Id = "dep1" }],
        });
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Valid.ShouldBeTrue();
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenLegacyProtocolOptionsNotModified_Succeeds()
    {
        // Arrange
        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dep1",
                    new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            {
                                "sourcecontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Source Context",
                                    Help = "The source context configuration for the dependency",
                                }
                            },
                            {
                                "targetcontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Target Context",
                                    Help = "The target context configuration for the dependency",
                                }
                            },
                        },
                    }
                },
            },
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition1",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Applications =
                        [
                            new V1LegacyProtocolMappingsApplication
                            {
                                Name = "app1",
                                CapabilityDefinition = "capdef1",
                            },
                        ],
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>()
                        {
                            {
                                    "dep1",
                                    new V1LegacyProtocolDependencyMapping
                                    {
                                        IntegrationDefinition = "int1",
                                        SourceContextConfig = "sourcecontext",
                                        TargetContextConfig = "targetcontext",
                                    }
                            },
                        },
                        Geographies = new Dictionary<string, string>()
                        {
                            { "default", "geo1" },
                        },
                    },
                },
                false),
        };
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Dependencies = new Dictionary<string, V1CatalogDataDependency>
            {
                {
                    "dep1",
                    new V1CatalogDataDependency
                    {
                        Type = V1CatalogDataDependencyType.Optional,
                        Cardinality = V1CatalogDataDependencyCardinality.One,
                        Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                        {
                            {
                                "sourcecontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Source Context",
                                    Help = "The source context configuration for the dependency",
                                }
                            },
                            {
                                "targetcontext",
                                new V1CatalogDataDependencyConfig
                                {
                                    Required = true,
                                    Max = 255,
                                    Label = "Target Context",
                                    Help = "The target context configuration for the dependency",
                                }
                            },
                        },
                    }
                },
            },
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Legacy,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    SolutionDefinition = "solutionDefinition1",
                    Mappings = new V1LegacyProtocolMappings
                    {
                        Applications =
                        [
                            new V1LegacyProtocolMappingsApplication
                            {
                                Name = "app1",
                                CapabilityDefinition = "capdef1",
                            },
                        ],
                        Dependencies = new Dictionary<string, V1LegacyProtocolDependencyMapping>()
                        {
                            {
                                    "dep1",
                                    new V1LegacyProtocolDependencyMapping
                                    {
                                        IntegrationDefinition = "int1",
                                        SourceContextConfig = "sourcecontext",
                                        TargetContextConfig = "targetcontext",
                                    }
                            },
                        },
                        Geographies = new Dictionary<string, string>()
                        {
                            { "default", "geo1" },
                        },
                    },
                },
                false),
        };
        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(new ServiceCollectionResponse
        {
            Items = [new ServiceResponse { Id = "dep1" }],
        });
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });
        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Valid.ShouldBeTrue();
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public static async Task ServiceEntryValidator_Update_WhenOptionalDependencyConfigAdded_Succeeds(bool existingConfigIsNull)
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = existingConfigIsNull ? null : AddDependencyConfig(true, false),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(!existingConfigIsNull, true),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Valid.ShouldBeTrue();
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenOptionalDependencyConfigRemoved_Succeeds()
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, true),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Valid.ShouldBeTrue();
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public static async Task ServiceEntryValidator_Update_WhenRequiredDependencyConfigAdded_Fails(bool existingConfigIsNull)
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = existingConfigIsNull ? null : AddDependencyConfig(false, false),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("Required config cannot be added to existing dependency: dependency config: config1");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenOptionalDependencyConfigUpdatedToRequired_Fails()
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(false, true),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(false, true),
        };

        newEntryModel.Dependencies["dependency"].Config!["config2"].Required = true;

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("Optional config cannot be changed to required for dependency: dependency config: config2");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenRequiredDependencyConfigUpdatedToOptional_Fails()
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false),
        };

        newEntryModel.Dependencies["dependency"].Config!["config1"].Required = false;

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("Required config cannot be changed to optional for dependency: dependency config: config1");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenRequiredDependencyConfigRemoved_Fails()
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, true),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(false, true),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("Required config cannot be removed for dependency: dependency config: config1");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenDependencyConfigMinUpdatedFromNull_Fails()
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false, null),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("Config min cannot be updated from null for dependency: dependency config: config1");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenDependencyConfigMaxUpdatedFromNull_Fails()
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false, 0, null),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("Config max cannot be updated from null for dependency: dependency config: config1");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenDependencyConfigMinIncreased_Fails()
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false, 5),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("Config min cannot be increased for dependency: dependency config: config1");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenDependencyConfigMaxDecreased_Fails()
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false, 0, 45),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("Config max cannot be decreased for dependency: dependency config: config1");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenDependencyConfigMinUpdatedToNull_Succeeds()
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false, null),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Valid.ShouldBeTrue();
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenDependencyConfigMaxUpdatedToNull_Succeeds()
    {
        // Arrange
        var oldEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false),
        };

        var newEntryModel = new V1ServiceEntry
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test description",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(V1Trigger.Account, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Dependencies = AddDependencyConfig(true, false, 0, null),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        catalogClientMock.Setup(x => x.GetAllAsync()).ReturnsAsync(CatalogResponseWithDependencyService);
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(accountMgmtClientMock.Object, catalogClientMock.Object, catalogEventClientMock.Object, instanceMgmtClientMock.Object, loggerMock.Object, optionsMock.Object);
        var oldEntry = new Entities.V1ServiceEntry { Spec = oldEntryModel, };
        var newEntry = new Entities.V1ServiceEntry { Spec = newEntryModel, };

        // Act
        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        // Assert
        result!.Valid.ShouldBeTrue();
    }

    [Fact]
    public static async Task ServiceEntryValidator_Create_WithValidWebhookProtocol_Succeeds()
    {
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "webhook-service",
            DisplayName = "Webhook Service",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test webhook service",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(
                V1Trigger.Catalog,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Isolated,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://mywebhook.com"),
                },
                false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(
            accountMgmtClientMock.Object,
            catalogClientMock.Object,
            catalogEventClientMock.Object,
            instanceMgmtClientMock.Object,
            loggerMock.Object,
            optionsMock.Object);

        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        // Act
        var result = await subject.CreateAsync(newEntry, true, CancellationToken.None).ConfigureAwait(true);

        result!.Valid.ShouldBeTrue();
    }

    [Fact]
    public static async Task ServiceEntryValidator_Create_WithNullWebhookUri_Fails()
    {
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "webhook-service",
            DisplayName = "Webhook Service",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test webhook service",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(
                V1Trigger.Catalog,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Isolated,
                new V1ProtocolOptions
                {
                    WebhookUri = null,
                },
                false),
        };
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(
            accountMgmtClientMock.Object,
            catalogClientMock.Object,
            catalogEventClientMock.Object,
            instanceMgmtClientMock.Object,
            loggerMock.Object,
            optionsMock.Object);

        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        var result = await subject.CreateAsync(newEntry, true, CancellationToken.None).ConfigureAwait(true);

        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("WebhookUri required for the Webhook Protocol");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Create_WithRelativeWebhookUri_Fails()
    {
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "webhook-service",
            DisplayName = "Webhook Service",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test webhook service",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(
                V1Trigger.Catalog,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Isolated,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("relative/path", UriKind.Relative),
                },
                false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(
            accountMgmtClientMock.Object,
            catalogClientMock.Object,
            catalogEventClientMock.Object,
            instanceMgmtClientMock.Object,
            loggerMock.Object,
            optionsMock.Object);

        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        var result = await subject.CreateAsync(newEntry, true, CancellationToken.None).ConfigureAwait(true);

        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("WebhookUri must be a valid absolute URI");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Create_WithMissingWebhookOptions_Fails()
    {
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "webhook-service",
            DisplayName = "Webhook Service",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test webhook service",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(
                V1Trigger.Catalog,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Isolated,
                new V1ProtocolOptions(),
                false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(
            accountMgmtClientMock.Object,
            catalogClientMock.Object,
            catalogEventClientMock.Object,
            instanceMgmtClientMock.Object,
            loggerMock.Object,
            optionsMock.Object);

        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        var result = await subject.CreateAsync(newEntry, true, CancellationToken.None).ConfigureAwait(true);

        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("WebhookUri required for the Webhook Protocol");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Create_WithMissingProtocolOptions_Fails()
    {
        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "webhook-service",
            DisplayName = "Webhook Service",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Description = "Test webhook service",
            IconUrl = new Uri("https://www.example.com"),
            Lifecycle = new V1Lifecycle(
                V1Trigger.Catalog,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Isolated,
                null,
                false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(
            accountMgmtClientMock.Object,
            catalogClientMock.Object,
            catalogEventClientMock.Object,
            instanceMgmtClientMock.Object,
            loggerMock.Object,
            optionsMock.Object);

        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        var result = await subject.CreateAsync(newEntry, true, CancellationToken.None).ConfigureAwait(true);

        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("ProtocolOptions required for the Webhook Protocol");
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenChangedToWebhookProtocol_Succeeds()
    {
        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.IntegrationEvent,
                "provider",
                V1InstanceMode.Shared,
                null,
                false),
        };

        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://mywebhook.com"),
                },
                false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(
            accountMgmtClientMock.Object,
            catalogClientMock.Object,
            catalogEventClientMock.Object,
            instanceMgmtClientMock.Object,
            loggerMock.Object,
            optionsMock.Object);

        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        result!.Valid.ShouldBeTrue();
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenChangedFromWebhookProtocol_Succeeds()
    {
        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://mywebhook.com"),
                },
                false),
        };

        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.IntegrationEvent,
                "provider",
                V1InstanceMode.Shared,
                null,
                false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(
            accountMgmtClientMock.Object,
            catalogClientMock.Object,
            catalogEventClientMock.Object,
            instanceMgmtClientMock.Object,
            loggerMock.Object,
            optionsMock.Object);

        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        result!.Valid.ShouldBeTrue();
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenWebhookUriModified_Succeeds()
    {
        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://mywebhook.com"),
                },
                false),
        };

        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://newwebhook.com"),
                },
                false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(
            accountMgmtClientMock.Object,
            catalogClientMock.Object,
            catalogEventClientMock.Object,
            instanceMgmtClientMock.Object,
            loggerMock.Object,
            optionsMock.Object);

        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        result!.Valid.ShouldBeTrue();
    }

    [Fact]
    public static async Task ServiceEntryValidator_Update_WhenWebhookUriRemoved_Fails()
    {
        V1ServiceEntry oldEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions
                {
                    WebhookUri = new Uri("https://mywebhook.com"),
                },
                false),
        };

        V1ServiceEntry newEntryModel = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            Category = V1Category.Data,
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(
                V1Trigger.Account,
                V1IntegrationProtocol.Webhook,
                "provider",
                V1InstanceMode.Shared,
                new V1ProtocolOptions(),
                false),
        };

        var accountMgmtClientMock = new Mock<IAccountMgmtClient>();
        var catalogClientMock = new Mock<ICatalogClient>();
        var catalogEventClientMock = new Mock<ICatalogEventClient>();
        var instanceMgmtClientMock = new Mock<IInstanceMgmtClient>();
        var loggerMock = new Mock<ILogger<V1ServiceEntryValidator>>();
        var optionsMock = new Mock<IOptionsMonitor<OperatorSettings>>();
        optionsMock.SetupGet(x => x.CurrentValue)
            .Returns(new OperatorSettings { OnConnectNamespace = "on-connect" });

        var subject = new V1ServiceEntryValidator(
            accountMgmtClientMock.Object,
            catalogClientMock.Object,
            catalogEventClientMock.Object,
            instanceMgmtClientMock.Object,
            loggerMock.Object,
            optionsMock.Object);

        Entities.V1ServiceEntry oldEntry = new Entities.V1ServiceEntry
        {
            Spec = oldEntryModel,
        };
        Entities.V1ServiceEntry newEntry = new Entities.V1ServiceEntry
        {
            Spec = newEntryModel,
        };

        var result = await subject.UpdateAsync(oldEntry, newEntry, true, CancellationToken.None).ConfigureAwait(true);

        result!.Status!.Code.ShouldBe(400);
        result.Status.Message.ShouldBe("WebhookUri required for the Webhook Protocol");
    }

    #endregion Test Cases

    private static Dictionary<string, V1CatalogDataDependency> AddDependencyConfig(bool addRequiredConfig, bool addOptionalConfig, int? requiredConfigMin = 0, int? requiredConfigMax = 50)
    {
        var config = new Dictionary<string, V1CatalogDataDependencyConfig>();

        if (addRequiredConfig)
        {
            config.Add("config1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Required = true, Min = requiredConfigMin, Max = requiredConfigMax });
        }

        if (addOptionalConfig)
        {
            config.Add("config2", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Required = false, Min = 10, Max = 50 });
        }

        return new Dictionary<string, V1CatalogDataDependency>
        {
            {
                "dependency", new V1CatalogDataDependency
                {
                    Type = V1CatalogDataDependencyType.Optional,
                    Cardinality = V1CatalogDataDependencyCardinality.One,
                    Colocated = true,
                    Config = config,
                }
            },
        };
    }

    private static ServiceCollectionResponse CatalogResponseWithDependencyService()
        => new ServiceCollectionResponse { Items = [new ServiceResponse { Id = "dependency" }] };
}