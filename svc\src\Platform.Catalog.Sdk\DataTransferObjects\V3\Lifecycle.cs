﻿namespace Aveva.Platform.Catalog.Sdk.DataTransferObjects.V3;

/// <summary>
/// Lifecycle.
/// </summary>
public class Lifecycle
{
    /// <summary>
    /// Initializes a new instance of the <see cref="Lifecycle"/> class.
    /// </summary>
#pragma warning disable CS8618 // Required for serialization
    public Lifecycle()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="Lifecycle"/> class.
    /// </summary>
    public Lifecycle(string trigger, string? protocol, string providerId, string? instanceMode, LegacyProtocolOptions? protocolOptions)
    {
        Trigger = trigger;
        Protocol = protocol;
        ProviderId = providerId;
        InstanceMode = instanceMode;
        ProtocolOptions = protocolOptions;
    }

    /// <summary>
    /// Gets or sets trigger.
    /// </summary>
    public string Trigger { get; set; }

    /// <summary>
    /// Gets or sets protocol.
    /// </summary>
    public string? Protocol { get; set; }

    /// <summary>
    /// Gets or sets provider id.
    /// </summary>
    public string ProviderId { get; set; }

    /// <summary>
    /// Gets or sets instance mode.
    /// </summary>
    public string? InstanceMode { get; set; }

    /// <summary>
    /// Gets or sets protocol options.
    /// </summary>
    public LegacyProtocolOptions? ProtocolOptions { get; set; }
}