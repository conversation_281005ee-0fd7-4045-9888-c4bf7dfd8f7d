﻿using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Common.Framework.Abstractions.Configuration;
using Aveva.Platform.Common.Framework.Mapping.AutoMapper.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Aveva.Platform.Catalog.Domain.Configuration;

/// <summary>
/// ServiceRegistrationModule class to inject services created in this library.
/// </summary>
public sealed class ServiceRegistrationModule : IServiceRegistrationModule
{
    #region Private Fields

    private readonly IConfiguration _configuration;

    #endregion Private Fields

    #region Public Constructors

    /// <summary>
    /// Initializes a new instance of the <see cref="ServiceRegistrationModule"/> class.
    /// </summary>
    /// <param name="configuration">The configuration.</param>
    /// <exception cref="ArgumentNullException">configuration is <c>null</c>.</exception>
    public ServiceRegistrationModule(IConfiguration configuration)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }
    #endregion Public Constructors
    #region Public Methods

    /// <inheritdoc/>
    public void AddServices(IServiceCollection services)
    {
        // Specify the type mapping support to be used by the application.
        services.AddAutoMapperTypeMappingServices();

        // Add Catalog metrics provider
        services.AddSingleton<CatalogMetrics>();
        services.AddSingleton<EventMetrics>();
    }

    #endregion Public Methods
}