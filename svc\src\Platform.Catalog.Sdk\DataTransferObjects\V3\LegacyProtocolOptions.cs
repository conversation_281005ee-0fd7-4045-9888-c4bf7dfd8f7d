﻿namespace Aveva.Platform.Catalog.Sdk.DataTransferObjects.V3
{
    /// <summary>
    /// LegacyProtocolOptions.
    /// </summary>
    public class LegacyProtocolOptions
    {
        /// <summary>
        /// The solution definition to which this service corresponds.
        /// </summary>
        public string? SolutionDefinition { get; init; }

        /// <summary>
        /// The mappings from the instances of this service created to the solution definition.
        /// </summary>
        public LegacyProtocolMappings? Mappings { get; init; } = new LegacyProtocolMappings();
    }
}