apiVersion: chaos-mesh.org/v1alpha1
kind: Schedule
metadata:
  name: operator-pod-kill-experiment
  namespace: chaos-mesh
spec:
  schedule: "@every 30s"
  type: "PodChaos"
  historyLimit: 25
  concurrencyPolicy: Forbid
  podChaos:
    action: "pod-kill"
    mode: one
    selector:
      namespaces:
        - platform-catalog
      labelSelectors:
        operator: aveva-platform-catalog-operator
    duration: "1m"

# Key parameters for the PodChaos experiment:
# - action: Specifies the type of pod disruption.
# - mode: Determines whether to target one or all matching pods.
# - selector: Filters the pods based on namespace and labels.
# - duration: The total time the pod kill experiment will run.
