{{- define "deployments.events" }}
- deployment:
    name: "catalog-events"
    serviceAccount:
      workloadIdentityClientId: {{ .Values.instance.workloadIdentity }}
    automountServiceAccountToken: true
    dapr:
      enabled: true
      idSuffix: "events"
      appPort: {{ .Values.events.port }}
      {{ if ne .Values.deploymentMode "pipeline" }}
      includeTracing: true
      {{ end }}
    scaling:
      targetCPUUtilizationPercentage: {{ .Values.events.scaling.cpuUtilizationPercentage }}
      targetMemoryUtilizationPercentage : {{ .Values.events.scaling.memoryUtilizationPercentage }}
      minReplicas: 1
      maxReplicas: {{ .Values.events.scaling.maxReplicas }}
    {{- if .Values.image.pullSecret }}
    imagePullSecrets: {{ .Values.image.pullSecret }}
    {{- end }}
    containers:
    - container:
        runAsNonRoot: {{ .Values.events.runAsNonRoot }}
        imageName: {{ .Values.events.image.name | quote }}
        repository: {{ .Values.environment.containerRegistryServer | quote }}
        imagePullPolicy: {{ .Values.events.image.pullPolicy | quote }}
        tag: {{ .Values.buildVariables.tag | quote }}
        port: {{ .Values.events.port }}
        environmentVariables:
          "ASPNETCORE_ENVIRONMENT": {{ eq .Values.deploymentMode "pipeline" | ternary "Production" "Development"  | quote}}
          "ASPNETCORE_URLS": "http://+:{{ .Values.events.port }}"
          "ContainerName": {{ .Values.events.image.name | quote }}
          "Authentication__IdentityDnsZoneName": "{{ .Values.environment.identityDnsZoneName }}"
          "region": {{ .Values.region.region | quote }}
          "geography": {{ .Values.region.geography | quote }}
          "Instrumentation__DebugEnabled": {{ .Values.events.env.Instrumentation__DebugEnabled | quote }}
        {{ if ne .Values.deploymentMode "pipeline" }}
          "Authentication__OverrideAuthenticationForLocalTesting": true
        {{ end}}
        {{ if .Values.events.env.Instrumentation__OtelExporterEndpoint }}
          "Instrumentation__OtelExporterEndpoint": {{ .Values.events.env.Instrumentation__OtelExporterEndpoint | quote }}
        {{ end}}
        livenessProbe:
          httpGet:
            path: "/liveness"
            port: {{ .Values.events.port }}
          {{ if eq .Values.deploymentMode "pipeline" }}
          initialDelaySeconds: {{ .Values.events.livenessProbe.initialDelaySeconds }}
          {{ end}}
          periodSeconds: {{ .Values.events.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.events.livenessProbe.timeoutSeconds }}
        readinessProbe:
          httpGet:
            path: "/readiness"
            port: {{ .Values.events.port }}
          {{ if eq .Values.deploymentMode "pipeline" }}
          initialDelaySeconds: {{ .Values.events.readinessProbe.initialDelaySeconds }}
          {{ end}}
          periodSeconds: {{ .Values.events.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.events.readinessProbe.timeoutSeconds }}
        startupProbe:
          httpGet:
            path: "/startup"
            port: {{ .Values.events.port }}
          initialDelaySeconds: {{ .Values.events.startupProbe.initialDelaySeconds }}
          timeoutSeconds: {{ .Values.events.startupProbe.timeoutSeconds }}
        cpuRequested: {{ .Values.scaling.cpuRequested }}
        cpuLimit: {{ .Values.scaling.cpuLimit }}
        memoryRequested: {{ .Values.scaling.memoryRequested }}
        memoryLimit: {{ .Values.scaling.memoryLimit }}
{{- end -}}