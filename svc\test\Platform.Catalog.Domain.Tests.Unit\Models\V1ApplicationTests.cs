﻿using Aveva.Platform.Catalog.Domain.Models;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Models;

/// <summary>
/// <see cref="V1ApplicationTests"/> unit tests.
/// </summary>
[Trait("Category", "Unit")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Unit")]
public static class V1ApplicationTests
{
    #region Test Cases

    [Fact]
    public static void V1ApplicationTests_Empty_Equals_True()
    {
        // Arrange
        V1Application first = new();

        V1Application second = new();

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1ApplicationTests_Empty_Null_Equals_False()
    {
        // Arrange
        V1Application first = new();

        // Assert
        first.Equals(null).ShouldBeFalse();
    }

    [Fact]
    public static void V1ApplicationTests_Missing_Urls_Equals_True()
    {
        // Arrange
        V1Application first = new()
        {
            Name = "Name",
        };

        V1Application second = new()
        {
            Name = "Name",
        };

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1ApplicationTests_Empty_Urls_Equals_True()
    {
        // Arrange
        V1Application first = new()
        {
            Name = "Name",
            Urls = new Dictionary<string, string>(),
        };

        V1Application second = new()
        {
            Name = "Name",
            Urls = new Dictionary<string, string>(),
        };

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1ApplicationTests_Null_Urls_Equals_True()
    {
        // Arrange
        V1Application first = new()
        {
            Name = "Name",
            Urls = null,
        };

        V1Application second = new()
        {
            Name = "Name",
            Urls = null,
        };

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1ApplicationTests_Empty_Null_Urls_Equals_True()
    {
        // Arrange
        V1Application first = new()
        {
            Name = "Name",
            Urls = new Dictionary<string, string>(),
        };

        V1Application second = new()
        {
            Name = "Name",
            Urls = null,
        };

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1ApplicationTests_Urls_Equals_True()
    {
        // Arrange
        V1Application first = new()
        {
            Name = "Name",
            Urls = new Dictionary<string, string> { { "url1", "https://localhost/url1" } },
        };

        V1Application second = new()
        {
            Name = "Name",
            Urls = new Dictionary<string, string> { { "url1", "https://localhost/url1" } },
        };

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1ApplicationTests_Name_Equals_False()
    {
        // Arrange
        V1Application first = new()
        {
            Name = "First",
        };

        V1Application second = new()
        {
            Name = "Different Name",
        };

        // Assert
        first.Equals(second).ShouldBeFalse();
    }

    [Fact]
    public static void V1ApplicationTests_Urls_Equals_False()
    {
        // Arrange
        V1Application first = new()
        {
            Name = "Name",
            Urls = new Dictionary<string, string> { { "url1", "https://localhost/url1" } },
        };

        V1Application second = new()
        {
            Name = "Name",
            Urls = new Dictionary<string, string> { { "url2", "https://localhost/url2" } },
        };

        // Assert
        first.Equals(second).ShouldBeFalse();
    }

    [Fact]
    public static void V1ApplicationTests_Different_Quantity_Urls_Equals_False()
    {
        // Arrange
        V1Application first = new()
        {
            Name = "Name",
            Urls = new Dictionary<string, string> { { "url1", "https://localhost/url1" } },
        };

        V1Application second = new()
        {
            Name = "Name",
            Urls = new Dictionary<string, string> { { "url1", "https://localhost/url1" }, { "url2", "https://localhost/url2" } },
        };

        // Assert
        first.Equals(second).ShouldBeFalse();
    }
    #endregion Test Cases
}