﻿using System.Diagnostics.CodeAnalysis;
using k8s;
using Microsoft.Extensions.Logging;

namespace Aveva.Platform.Catalog.Tests.Integration.Kube.Common;

[SuppressMessage("Performance", "CA1848:Use the LoggerMessage delegates", Justification = "Not applicable", Scope = "module")]
[SuppressMessage("Usage", "CA2254:Template should be a static expression", Justification = "Not applicable", Scope = "module")]
internal sealed class HelmfileClient
{
    private readonly IntegrationTestOptions _options;
    private readonly ILogger _logger;
    private readonly IKubernetes _kubeClient;

    public HelmfileClient(IntegrationTestOptions options, ILogger logger, IKubernetes kubeClient)
    {
        _options = options;
        _logger = logger;
        _kubeClient = kubeClient;
    }

    public async Task<bool> IsCliInstalledAsync() => (await ProcessExtensions.RunAsync(_options.HelmfileExecutable).ConfigureAwait(false)).Success;

    public async Task<bool> ReleaseExistsAsync(string name, string @namespace)
        => (await ProcessExtensions.RunAsync(_options.HelmExecutable, $"get all {name} -n {@namespace}").ConfigureAwait(false)).Success;

    public async Task ApplyAsync(string helmfilePath, string additionalArguments)
    {
        var command = $"apply --file {helmfilePath} --helm-binary {_options.HelmExecutable} --kube-context {_options.KubeContext}";
        if (!string.IsNullOrWhiteSpace(additionalArguments))
        {
            command += " " + additionalArguments;
        }

        var (success, output) = await ProcessExtensions.RunAsync(_options.HelmfileExecutable, command).ConfigureAwait(false);
        if (!success)
        {
            _logger.LogWarning($"Failed to apply helmfile: {helmfilePath} {command} {output}");
            throw new InvalidOperationException(output);
        }
    }

    public async Task<string> ListAsync(string helmfilePath)
    {
        var command = $"list --file {helmfilePath} --helm-binary {_options.HelmExecutable} --kube-context {_options.KubeContext}";

        var (success, output) = await ProcessExtensions.RunAsync(_options.HelmfileExecutable, command).ConfigureAwait(false);
        if (!success)
        {
            _logger.LogWarning($"Failed to list releases defined in helmfile: {helmfilePath} {command} {output}");
            throw new InvalidOperationException(output);
        }

        return output;
    }

    public async Task<string> StatusAsync(string helmfilePath)
    {
        var command = $"status --file {helmfilePath} --helm-binary {_options.HelmExecutable} --kube-context {_options.KubeContext}";

        var (success, output) = await ProcessExtensions.RunAsync(_options.HelmfileExecutable, command).ConfigureAwait(false);
        if (!success)
        {
            _logger.LogWarning($"Failed to retrieve status of releases for helmfile: {helmfilePath} {command} {output}");
            throw new InvalidOperationException(output);
        }

        return output;
    }

    public async Task SyncAsync(string helmfilePath, string additionalArguments)
    {
        var command = $"sync --file {helmfilePath} --helm-binary {_options.HelmExecutable} --kube-context {_options.KubeContext}";
        if (!string.IsNullOrWhiteSpace(additionalArguments))
        {
            command += " " + additionalArguments;
        }

        var (success, output) = await ProcessExtensions.RunAsync(_options.HelmfileExecutable, command).ConfigureAwait(false);
        if (!success)
        {
            _logger.LogWarning($"Failed to apply helmfile: {helmfilePath} {command} {output}");
            throw new InvalidOperationException(output);
        }
    }
}