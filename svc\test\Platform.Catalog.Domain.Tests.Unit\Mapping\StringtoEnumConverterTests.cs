﻿using Aveva.Platform.Catalog.Domain.Mapping;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Mapping
{
    [Trait("Category", "Unit")]
    [Trait("Category", "Domain.Unit")]
    public class StringtoEnumConverterTests
    {
        private enum EnumConverterTest
        {
            One,
            Two,
            Three,
        }

        [Fact]
        public void TestStringToEnumConverter()
        {
            // Arrange
            var converter = new StringToEnumConverter<EnumConverterTest>();

            // Act
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            var result = converter.Convert("Three", null);
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.

            // Assert
            result.ShouldBe(EnumConverterTest.Three);
            result.GetType().ShouldBe(typeof(EnumConverterTest));
        }
    }
}