﻿namespace Aveva.Platform.Catalog.Tests.PerformanceSetup.Models;

/// <summary>
/// This record is used to keep response of Deserialized object.
/// </summary>
/// <param name="access_token">access_token.</param>
/// <param name="expires_in">expires_in.</param>
/// <param name="token_type">token_type.</param>
/// <param name="expires_date">expires_date.</param>
public record TokenResponse(string? access_token, int expires_in, string? token_type, DateTime expires_date);