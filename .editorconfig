# Tool and sample code editorconfig preferences

####################################################################
# Team-specific Rules, Extensions, and Overrides
# Description:
#   Rules from the common OSIsoft rules may be overridden here.
#   Extensions to enable additional rules is always acceptable.
#   Overrides do disable a rule are by exception only and should
#   include one of the following:
#   1)  A PBI number for the eventual removal of the override.
#       For catalog, in a case where compliance will take
#       time to implement.
#   2)  A justification of why the override is required.
####################################################################
# Catalog to add enforcement of CA1008 and disable SA1000 with PBI:
#   # CA1008: Enums should have zero value
#   dotnet_diagnostic.CA1008.severity = error
#   # SA1000: Keywords should be spaced correctly
#   dotnet_diagnostic.SA1000.severity = none # PBI: <link to PBI>
####################################################################

# CSharp code settings:
[*.cs]

###################################################################################################
# Code Analysis Rule extensions (no justification or PBI reference required)
###################################################################################################
# CA1003: Use generic event handler instances
dotnet_diagnostic.CA1003.severity = warning
# CA1008: Enums should have zero value
dotnet_diagnostic.CA1008.severity = suggestion
# CA1012: Abstract types should not have public constructors
dotnet_diagnostic.CA1012.severity = warning
# CA1019: Define accessors for attribute arguments
dotnet_diagnostic.CA1019.severity = warning
# CA1027: Mark enums with FlagsAttribute
dotnet_diagnostic.CA1027.severity = warning
# CA1033: Interface methods should be callable by child types
dotnet_diagnostic.CA1033.severity = warning
# CA1050: Declare types in namespaces
dotnet_diagnostic.CA1050.severity = error
# CA1060: Move pinvokes to native methods class
dotnet_diagnostic.CA1060.severity = error
# CA1708: Identifiers should differ by more than case
dotnet_diagnostic.CA1708.severity = warning
# CA1711: Identifiers should not have incorrect suffix
dotnet_diagnostic.CA1711.severity = warning
# CA1714: Flags enums should have plural names
dotnet_diagnostic.CA1714.severity = warning
# CA1725: Parameter names should match base declaration
dotnet_diagnostic.CA1725.severity = warning
# CA2201: Do not raise reserved exception types
dotnet_diagnostic.CA2201.severity = suggestion
# CA2217: Do not mark enums with FlagsAttribute
dotnet_diagnostic.CA2217.severity = warning
# CA2218: Override GetHashCode on overriding Equals
dotnet_diagnostic.CA2218.severity = warning
# CA2224: Override Equals on overloading operator equals
dotnet_diagnostic.CA2224.severity = warning
# CA2300: Do not use insecure deserializer BinaryFormatter
dotnet_diagnostic.CA2300.severity = error
# CA2301: Do not call BinaryFormatter.Deserialize without first setting BinaryFormatter.Binder
dotnet_diagnostic.CA2301.severity = error
# CA2302: Ensure BinaryFormatter.Binder is set before calling BinaryFormatter.Deserialize
dotnet_diagnostic.CA2302.severity = error
# CA2305: Do not use insecure deserializer LosFormatter
dotnet_diagnostic.CA2305.severity = error
# CA2310: Do not use insecure deserializer NetDataContractSerializer
dotnet_diagnostic.CA2310.severity = error
# CA2311: Do not deserialize without first setting NetDataContractSerializer.Binder
dotnet_diagnostic.CA2311.severity = error
# CA2312: Ensure NetDataContractSerializer.Binder is set before deserializing
dotnet_diagnostic.CA2312.severity = error
# CA2315: Do not use insecure deserializer ObjectStateFormatter
dotnet_diagnostic.CA2315.severity = error
# CA3001: Review code for SQL injection vulnerabilities
dotnet_diagnostic.CA3001.severity = error
# CA3002: Review code for XSS vulnerabilities
dotnet_diagnostic.CA3002.severity = error
# CA3003: Review code for file path injection vulnerabilities
dotnet_diagnostic.CA3003.severity = error
# CA3004: Review code for information disclosure vulnerabilities
dotnet_diagnostic.CA3004.severity = error
# CA3005: Review code for LDAP injection vulnerabilities
dotnet_diagnostic.CA3005.severity = error
# CA3006: Review code for process command injection vulnerabilities
dotnet_diagnostic.CA3006.severity = error
# CA3007: Review code for open redirect vulnerabilities
dotnet_diagnostic.CA3007.severity = error
# CA3008: Review code for XPath injection vulnerabilities
dotnet_diagnostic.CA3008.severity = error
# CA3009: Review code for XML injection vulnerabilities
dotnet_diagnostic.CA3009.severity = error
# CA3010: Review code for XAML injection vulnerabilities
dotnet_diagnostic.CA3010.severity = error
# CA3011: Review code for DLL injection vulnerabilities
dotnet_diagnostic.CA3011.severity = error
# CA3012: Review code for regex injection vulnerabilities
dotnet_diagnostic.CA3012.severity = error
# CA5358: Review cipher mode usage with cryptography experts
dotnet_diagnostic.CA5358.severity = error
# CA5361: Do Not Disable SChannel Use of Strong Crypto
dotnet_diagnostic.CA5361.severity = error
# CA5362: Potential reference cycle in deserialized object graph
dotnet_diagnostic.CA5362.severity = error
# CA5367: Do Not Serialize Types With Pointer Fields
dotnet_diagnostic.CA5367.severity = error

# SA1028: Code should not contain trailing whitespace
dotnet_diagnostic.SA1028.severity = warning
# SA1116: Split parameters should start on line after declaration
dotnet_diagnostic.SA1116.severity = warning
# SA1117: Parameters should be on same line or separate lines
dotnet_diagnostic.SA1117.severity = warning
# SA1118: Parameter should not span multiple lines
dotnet_diagnostic.SA1118.severity = warning
# SA1122: Use string.Empty for empty strings
dotnet_diagnostic.SA1122.severity = warning
# SA1501: Statement should not be on a single line
dotnet_diagnostic.SA1501.severity = warning
# SA1503: Braces should not be omitted
dotnet_diagnostic.SA1503.severity = warning
# SA1626: Single-line comments should not use documentation style slashes
dotnet_diagnostic.SA1626.severity = warning
# SA1629: Documentation text should end with a period
dotnet_diagnostic.SA1629.severity = warning
# SA1642: Constructor summary documentation should begin with standard text
dotnet_diagnostic.SA1642.severity = warning
# SA1643: Destructor summary documentation should begin with standard text
dotnet_diagnostic.SA1643.severity = warning
# SA1648: inheritdoc should be used with inheriting class
dotnet_diagnostic.SA1648.severity = warning
# SA1651: Do not use placeholder elements
dotnet_diagnostic.SA1651.severity = warning

# CS1573: Parameter 'parameter' has no matching param tag in the XML comment for 'parameter' (but other parameters do)
dotnet_diagnostic.CS1573.severity = suggestion
# CS1591: Missing XML comment for publicly visible type or member 'Type_or_Member'
dotnet_diagnostic.CS1591.severity = silent
# CS1712: Type parameter 'type parameter' has no matching typeparam tag in the XML comment on 'type' (but other type parameters do)
dotnet_diagnostic.CS1712.severity = suggestion

###################################################################################################
# Code Analysis Rule removals + overrides that downgrade code analysis (include justification or PBI #)
###################################################################################################
# CA1707: Identifiers should not contain underscores - Justification: Test method names use an underscore separate convention for readability.
dotnet_diagnostic.CA1707.severity = none
# CA1031: Do not catch general exception types - Justification: Tools and samples frequently must detect any exception type thrown for simplicity and use this approach.
dotnet_diagnostic.CA1031.severity = none
# CA1062: Validate arguments of public methods - Justification: Tools and samples are executed in a well-defined, controlled private runtime environment where superfluous guard clauses will overly complicate the non-production code.
dotnet_diagnostic.CA1062.severity = none
# CA1303: Do not pass literals as localized parameters - Justification: Tools and samples are typically not localized and are not expected to ever need to be.
dotnet_diagnostic.CA1303.severity = none
# CA1304: Specify CultureInfo - Justification: Tools and samples are typically not localized and are not expected to ever need to be.
dotnet_diagnostic.CA1304.severity = none
# CA1305: Specify IFormatProvider - Justification: Tools and samples are typically not localized and are not expected to ever need to be.
dotnet_diagnostic.CA1305.severity = none
# CA2208: Instantiate argument exceptions correctly - Justification: Tools and samples occasionally need to construct argument exceptions in a different manner than production code.
dotnet_diagnostic.CA2208.severity = none
# CA1848 Use the LoggerMessage delegates - Justification: Samples are typically not designed for performance and therefore may be exempted from this rule
dotnet_diagnostic.CA1848.severity=silent
# CA2254 Template should be a static expression - Justification: Samples are typically not designed for performance and therefore may be exempted from this rule
dotnet_diagnostic.CA2254.severity=silent