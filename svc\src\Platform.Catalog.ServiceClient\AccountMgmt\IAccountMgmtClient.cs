﻿using Aveva.Platform.AccountMgmt.Client.Ops.Models;
using Aveva.Platform.Catalog.Domain.Models;

namespace Aveva.Platform.Catalog.ServiceClient.AccountMgmt
{
    /// <summary>
    /// Interface for AccountMgmt API service client.
    /// </summary>
    public interface IAccountMgmtClient
    {
        /// <summary>
        /// Gets all Geographies.
        /// </summary>
        /// <returns>The Geography if found.</returns>
        Task<IEnumerable<GeographyResponse>> QueryGeographiesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the serviceAvailability of services for the account.
        /// </summary>
        /// <param name="accountId">The accountId.</param>
        /// <param name="cancellationToken">The cancellationToken.</param>
        /// <returns>The serviceAvailability of services for the account.</returns>
        Task<Dictionary<string, V1ServiceAvailability>?> QueryAvailabilityAsync(string accountId, CancellationToken cancellationToken = default);
    }
}