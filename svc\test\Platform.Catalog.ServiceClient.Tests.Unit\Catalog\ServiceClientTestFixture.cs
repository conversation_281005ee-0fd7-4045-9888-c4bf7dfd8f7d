﻿using Moq;
using Moq.Protected;
using Xunit;

namespace Aveva.Platform.Catalog.ServiceClient.Tests.Unit.Catalog
{
    public class ServiceClientTestFixture : IAsyncLifetime
    {
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public HttpClient HttpClient { get; private set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public ValueTask DisposeAsync()
        {
            HttpClient?.Dispose();
            GC.SuppressFinalize(this);
            return ValueTask.CompletedTask;
        }

        public ValueTask InitializeAsync()
        {
            return ValueTask.CompletedTask;
        }

        public void SetupHttpClient(HttpResponseMessage responseMessage)
        {
            Mock<HttpMessageHandler> httpMessageHandlerMock = new Mock<HttpMessageHandler>();
            httpMessageHandlerMock.Protected()
              .SetupSequence<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
              .ReturnsAsync(responseMessage);

            HttpClient = new HttpClient(httpMessageHandlerMock.Object)
            {
                BaseAddress = new Uri("http://catalogtest.com"),
            };
        }
    }
}