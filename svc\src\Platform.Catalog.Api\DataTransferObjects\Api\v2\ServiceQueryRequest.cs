﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Microsoft.AspNetCore.Mvc;

namespace Aveva.Platform.Catalog.Api.DataTransferObjects.Api.v2;

/// <summary>
/// ServiceQueryRequest for Api.
/// </summary>
public record ServiceQueryRequest
{
    /// <summary>
    /// Gets category.
    /// </summary>
    [FromQuery(Name = "category")]
    public Category? Category { get; init; }
}