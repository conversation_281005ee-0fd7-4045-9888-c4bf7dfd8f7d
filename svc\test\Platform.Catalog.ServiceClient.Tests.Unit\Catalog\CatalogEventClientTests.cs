﻿using System.Net;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Microsoft.Extensions.Configuration;
using Moq;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.ServiceClient.Tests.Unit.Catalog;

/// <summary>
/// <see cref="CatalogClientTests"/> unit test class.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Unit")]
[Trait("Category", "ServiceClient")]
[Trait("Category", "ServiceClient.Unit")]
[Trait("Tag", "Catalog")]
public class CatalogEventClientTests : IClassFixture<ServiceClientTestFixture>
{
    private readonly ServiceClientTestFixture _serviceClientTestFixture;
    public CatalogEventClientTests(ServiceClientTestFixture serviceClientTestFixture)
    {
        _serviceClientTestFixture = serviceClientTestFixture;
    }

    #region Test Cases
    [Fact]
    public void CatalogEventClient_Create_Succeeds()
    {
        Mock<IConfiguration> configMock = new Mock<IConfiguration>();
        var mockConfSection = new Mock<IConfigurationSection>();
        mockConfSection.SetupGet(m => m[It.Is<string>(s => s.Equals("ServiceReferenceId"))]).Returns("mock value");
        configMock.Setup(x => x.GetSection(It.IsAny<string>())).Returns(mockConfSection.Object);

        using var responseMessage = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
        };

        _serviceClientTestFixture.SetupHttpClient(responseMessage);

        Should.NotThrow(() =>
        {
            CatalogEventClient catalogClient = new CatalogEventClient(configMock.Object, _serviceClientTestFixture.HttpClient);
        });
    }

    [Fact]
    public void CatalogEventClient_CreateNullConfig_Fails()
    {
            Should.Throw<ArgumentNullException>(() =>
            {
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
                CatalogEventClient catalogClient = new CatalogEventClient(null, _serviceClientTestFixture.HttpClient);
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
            });
    }

    [Fact]
    public void CatalogEventClient_CreateNullHttpClient_Fails()
    {
        Mock<IConfiguration> configMock = new Mock<IConfiguration>();
        Should.Throw<ArgumentNullException>(() =>
        {
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            CatalogEventClient catalogClient = new CatalogEventClient(configMock.Object, null);
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
        });
    }

    [Fact]
    public async Task CatalogEventClient_PublishEventAsync_Succeeds()
    {
        // Arrange
        V1ServiceEntry oldEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "entry",
            HostingType = V1HostingType.Regional,
        };

        Mock<IConfiguration> configMock = new Mock<IConfiguration>();
        var mockConfSection = new Mock<IConfigurationSection>();

        mockConfSection.SetupGet(m => m[It.Is<string>(s => s.Equals("ServiceReferenceId"))]).Returns("mock value");
        configMock.Setup(x => x.GetSection(It.IsAny<string>())).Returns(mockConfSection.Object);

        using var responseMessage = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.Accepted,
        };

        _serviceClientTestFixture.SetupHttpClient(responseMessage);

        CatalogEventClient catalogClient = new CatalogEventClient(configMock.Object, _serviceClientTestFixture.HttpClient);

        // Act
        await Should.NotThrowAsync(async () =>
        {
            await catalogClient.PublishEventAsync("create", oldEntry).ConfigureAwait(true);
        }).ConfigureAwait(true);
    }

    [Fact]
    public async Task CatalogEventClient_PublishEventAsync_Fails()
    {
        // Arrange
        V1ServiceEntry oldEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "entry",
            HostingType = V1HostingType.Regional,
        };

        Mock<IConfiguration> configMock = new Mock<IConfiguration>();
        var mockConfSection = new Mock<IConfigurationSection>();

        mockConfSection.SetupGet(m => m[It.Is<string>(s => s.Equals("ServiceReferenceId"))]).Returns("mock value");
        configMock.Setup(x => x.GetSection(It.IsAny<string>())).Returns(mockConfSection.Object);

        using var responseMessage = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.InternalServerError,
        };

        _serviceClientTestFixture.SetupHttpClient(responseMessage);

        CatalogEventClient catalogClient = new CatalogEventClient(configMock.Object, _serviceClientTestFixture.HttpClient);

        // Act
        await Should.ThrowAsync<HttpRequestException>(async () =>
        {
            await catalogClient.PublishEventAsync("Create", oldEntry).ConfigureAwait(true);
        }).ConfigureAwait(true);
    }
    #endregion Test Cases
}