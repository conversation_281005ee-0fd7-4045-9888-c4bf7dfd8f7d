﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Events.Converter;
using Aveva.Platform.Catalog.Events.Publisher;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.Common.Framework.Abstractions.Mapping;
using Aveva.Platform.Common.Messaging.EventBus.Abstractions;
using Aveva.Platform.Common.Messaging.EventBus.Events.Catalog;
using Moq;
using PactNet.Verifier.Messaging;
using Polly;
using Polly.Retry;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.ContractTests.Provider
{
    public class EventProviderTests : IClassFixture<EventProviderFixture>
    {
        private static readonly RetryStrategyOptions _retryStrategyOptions = new RetryStrategyOptions()
        {
            BackoffType = DelayBackoffType.Exponential,
            MaxRetryAttempts = 1,
            Delay = TimeSpan.FromMilliseconds(100),
        };

        private static readonly ResiliencePipeline _retryPipeline = new ResiliencePipelineBuilder()
            .AddRetry(_retryStrategyOptions)
            .Build();
        private EventProviderFixture _fixture;
        private Mock<IEventBus> _eventBus = new Mock<IEventBus>();
        private Mock<ICatalogClient> _catalogClient = new Mock<ICatalogClient>();
        private Mock<EventMetrics> _metrics = new Mock<EventMetrics>();

#pragma warning disable xUnit1041 // Fixture arguments to test classes must have fixture sources
        public EventProviderTests(EventProviderFixture fixture, ITestOutputHelper output)
#pragma warning restore xUnit1041 // Fixture arguments to test classes must have fixture sources
        {
            ArgumentNullException.ThrowIfNull(fixture);

            // Setup the fixture for this service provider
            _fixture = (EventProviderFixture)fixture.SetupVerifier(output);
        }

        /// <summary>
        /// Testcase to generate the events and verify it with Pactflow broker.
        /// </summary>
        [Fact]
        [Trait("Tag", "PactflowEventsTest")]
        public void EventContractTest()
        {
            // Add messages to verify against Pactflow
            _fixture.Verifier?.WithMessages(
            scenarios =>
            {
                scenarios.Add(nameof(CatalogServiceAddV1), CatalogServiceAddV1_Setup)
                .Add(nameof(CatalogServiceUpdateV1), CatalogServiceUpdateV1_Setup)
                .Add(nameof(CatalogServiceDeleteV1), CatalogServiceDeleteV1_Setup);
            });

            _fixture.VerifyInteraction();
        }

        /// <summary>
        /// Method to create CatalogServiceAddV1 event.
        /// Created event is tested against event publisher before verifying against Pactflow.
        /// </summary>
        /// <param name="builder">Pact message builder.</param>
        private async Task CatalogServiceAddV1_Setup(IMessageScenarioBuilder builder)
        {
            // Arrange
            CatalogServiceAddV1Publisher? serviceEntryCreatePublisher = null;
            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = new List<ServiceResponse>() { new ServiceResponse() { Id = "newlyAddedServiceId" } },
            };

            _catalogClient.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(catalogResponse!)!);
            var serviceEntry = new V1ServiceEntry()
            {
                Id = "newlyAddedServiceId",
            };
            var addEvent = new CatalogServiceAddV1()
            {
                Geography = "us",
                Id = "newlyAddedServiceId",
                Region = "eastus2",
            };

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryCreatePublisher = new CatalogServiceAddV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "us", region: "eastus2", retryPipeline: _retryPipeline, _metrics.Object);
                await serviceEntryCreatePublisher!.PublishEventAsync(serviceEntry).ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _catalogClient.Invocations.Count.ShouldBeGreaterThan(0);
            _eventBus.Verify(x => x.PublishAsync(It.IsAny<CatalogServiceAddV1>()), Times.Once);

            // Add the CatalogServiceAddV1 event to verify the consumer contracts published to Pactflow
            builder.WithContent(() =>
            {
                return addEvent;
            });
        }

        /// <summary>
        /// Method to create CatalogServiceUpdateV1 event.
        /// Created event is tested against event publisher before verifying against Pactflow.
        /// </summary>
        /// <param name="builder">Pact message builder.</param>
        private async Task CatalogServiceUpdateV1_Setup(IMessageScenarioBuilder builder)
        {
            // Arrange
            Mock<ITypeMappingService> mapper = new Mock<ITypeMappingService>();
            CatalogServiceUpdateV1Publisher? serviceEntryUpdatePublisher = null;
            var application = new V1Application
            {
                Name = "AppName",
                Urls = new Dictionary<string, string> { { "eu", "http://default.url" } },
            };

            var lifecycle = new V1Lifecycle
            {
                InstanceMode = V1InstanceMode.Shared,
                Protocol = V1IntegrationProtocol.IntegrationEvent,
                ProviderId = "oldProviderId",
                Trigger = V1Trigger.Catalog,
            };

            var oldServiceEntry = new V1ServiceEntry()
            {
                Id = "updatedServiceId",
                DisplayName = "oldDisplayName",
                Description = "oldDescription",
                Category = V1Category.Data,
                HostingType = V1HostingType.Environment,
                Dependencies = new Dictionary<string, V1CatalogDataDependency>
                {
                    {
                        "dependency1", new V1CatalogDataDependency
                            {
                                Type = V1CatalogDataDependencyType.Required,
                                Cardinality = V1CatalogDataDependencyCardinality.One,
                                Colocated = false,
                                Config = new Dictionary<string, V1CatalogDataDependencyConfig>
                                {
                                    { "config1", new V1CatalogDataDependencyConfig { Label = "label", Help = "help", Required = true, Min = 10, Max = 100 } },
                                },
                            }
                    },
                },
                Applications = new List<V1Application> { application },
                Lifecycle = lifecycle,
            };

            var newServiceEntry = new V1ServiceEntry
            {
                Id = "updatedServiceId",
                DisplayName = "newDisplayName",
                Description = "newDescription",
                Category = V1Category.Data,
                HostingType = V1HostingType.Environment,
                Dependencies = oldServiceEntry.Dependencies,
                Applications = oldServiceEntry.Applications,
                Lifecycle = new V1Lifecycle
                {
                    InstanceMode = V1InstanceMode.Isolated,
                    Protocol = V1IntegrationProtocol.IntegrationEvent,
                    ProviderId = "newProviderId",
                    Trigger = V1Trigger.Account,
                },
                Geographies = new List<V1Geography> { new() { Id = "eu" } },
            };

#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
            var newMappedServiceEntry = new ServiceResponse()
            {
                Id = newServiceEntry.Id,
                DisplayName = newServiceEntry.DisplayName,
                Description = newServiceEntry.Description,
                Category = Category.Data,
                HostingType = "Environment",
                Dependencies = new Dictionary<string, Dependency>
                    {
                        {
                            "dependency1", new Dependency
                                {
                                    Type = DependencyType.Required,
                                    Cardinality = DependencyCardinality.One,
                                    Colocated = false,
                                    Config = new Dictionary<string, DependencyConfig>
                                    {
                                        { "config1", new DependencyConfig { Label = "label", Help = "help", Required = true, Min = 10, Max = 100 } },
                                    },
                                }
                        },
                    },
                Applications = newServiceEntry.Applications?
                        .Select(app => new Application
                        {
                            Name = app.Name,
                            Urls = app.Urls,
                        }).ToList(),
                Geographies = new List<Geography> { new() { Id = "eu" } },
                Lifecycle = new Lifecycle(
                        trigger: newServiceEntry.Lifecycle.Trigger.ToString(),
                        protocol: newServiceEntry.Lifecycle.Protocol.ToString(),
                        providerId: newServiceEntry.Lifecycle.ProviderId,
                        instanceMode: newServiceEntry.Lifecycle.InstanceMode.ToString(),
                        protocolOptions: null),
            };

            List<ServiceResponse> lstServiceResponse = new List<ServiceResponse>()
            {
                new ServiceResponse()
                {
                    Id = "updatedServiceId",
                    DisplayName = "newDisplayName",
                    Description = "newDescription",
                    Category = Category.Data,
                    HostingType = "Environment",
                    Dependencies = new Dictionary<string, Dependency>
                    {
                        {
                            "dependency1", new Dependency
                                {
                                    Type = DependencyType.Required,
                                    Cardinality = DependencyCardinality.One,
                                    Colocated = false,
                                    Config = new Dictionary<string, DependencyConfig>
                                    {
                                        { "config1", new DependencyConfig { Label = "label", Help = "help", Required = true, Min = 10, Max = 100 } },
                                    },
                                }
                        },
                    },
                    Applications = newServiceEntry.Applications?
                        .Select(app => new Application
                        {
                            Name = app.Name,
                            Urls = app.Urls,
                        }).ToList(),
                    Geographies = new List<Geography> { new() { Id = "eu" } },
                    Lifecycle = new Lifecycle(
                        trigger: newServiceEntry.Lifecycle.Trigger.ToString(),
                        protocol: newServiceEntry.Lifecycle.Protocol.ToString(),
                        providerId: newServiceEntry.Lifecycle.ProviderId,
                        instanceMode: newServiceEntry.Lifecycle.InstanceMode.ToString(),
                        protocolOptions: null),
                },
            };
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = lstServiceResponse,
            };

            var updateEvent = new CatalogServiceUpdateV1
            {
                Geography = "us",
                Id = "updatedServiceId",
                Region = "eastus2",
                OldServiceEntry = oldServiceEntry.ToDto(),
                NewServiceEntry = newServiceEntry.ToDto(),
            };

            _catalogClient.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(catalogResponse)!);
            mapper.Setup(x => x.Map<V1ServiceEntry, ServiceResponse>(It.IsAny<V1ServiceEntry>())).Returns(newMappedServiceEntry);

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryUpdatePublisher = new CatalogServiceUpdateV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "us", region: "eastus2", retryPipeline: _retryPipeline, mapper.Object, _metrics.Object);
                await serviceEntryUpdatePublisher!.PublishEventAsync(newServiceEntry, oldServiceEntry).ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _eventBus.Verify(x => x.PublishAsync(It.IsAny<CatalogServiceUpdateV1>()), Times.Once);

            // Add the CatalogServiceUpdateV1 event to verify the consumer contracts published to Pactflow
            builder.WithContent(() =>
            {
                return updateEvent;
            });
        }

        /// <summary>
        /// Method to create CatalogServiceDeleteV1 event.
        /// Created event is tested against event publisher before verifying against Pactflow.
        /// </summary>
        /// <param name="builder">Pact message builder.</param>
        private async Task CatalogServiceDeleteV1_Setup(IMessageScenarioBuilder builder)
        {
            // Arrange
            CatalogServiceDeleteV1Publisher? serviceEntryCreatePublisher = null;
            ServiceCollectionResponse? catalogResponse = new ServiceCollectionResponse()
            {
                Items = new List<ServiceResponse>() { new ServiceResponse() { Id = "newlyAddedServiceId", } },
            };

            _catalogClient.Setup(x => x.GetAllAsync()).Returns(Task.FromResult(catalogResponse!)!);
            var serviceEntry = new V1ServiceEntry()
            {
                Id = "deletedId",
                Lifecycle = new V1Lifecycle(
                    V1Trigger.Catalog,
                    V1IntegrationProtocol.IntegrationEvent,
                    "kubernetes",
                    V1InstanceMode.Isolated,
                    null,
                    false),
            };
            var deleteEvent = new CatalogServiceDeleteV1()
            {
                Geography = "us",
                Id = "deletedId",
                Region = "eastus2",
                Lifecycle = new CatalogServiceLifecycleV1
                {
                    Protocol = LifecycleProtocolV1.IntegrationEvent,
                    ProviderId = "kubernetes",
                },
            };

            // Act
            await Should.NotThrowAsync(async () =>
            {
                serviceEntryCreatePublisher = new CatalogServiceDeleteV1Publisher(_eventBus.Object, _catalogClient.Object, geography: "us", region: "eastus2", retryPipeline: _retryPipeline, _metrics.Object);
                await serviceEntryCreatePublisher!.PublishEventAsync(serviceEntry).ConfigureAwait(true);
            }).ConfigureAwait(true);

            // Assert
            _catalogClient.Invocations.Count.ShouldBeGreaterThan(0);
            _eventBus.Verify(x => x.PublishAsync(It.IsAny<CatalogServiceDeleteV1>()), Times.Once);

            // Add the CatalogServiceDeleteV1 event to verify the consumer contracts published to Pactflow
            builder.WithContent(() =>
            {
                return deleteEvent;
            });
        }
    }
}