﻿using System.ComponentModel.DataAnnotations;

namespace Aveva.Platform.Catalog.Domain.Models;

/// <summary>
/// Application.
/// </summary>
public class V1Application
{
#pragma warning disable CS8618
    /// <summary>
    /// Gets or sets name.
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    /// Gets or sets urls.
    /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
    public IDictionary<string, string>? Urls { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only
#pragma warning restore CS8618

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        if (obj is not V1Application item)
        {
            return false;
        }

        var result = string.Equals(item.Name, Name, StringComparison.InvariantCultureIgnoreCase);

        if (Urls == null && item.Urls != null)
        {
            if (item.Urls.Count > 0)
            {
                result = result && false;
            }
        }

        if (Urls != null && item.Urls == null)
        {
            if (Urls.Count > 0)
            {
                result = result && false;
            }
        }

        if (Urls != null && item.Urls != null)
        {
            result = result
                     && (Urls.Count == item.Urls.Count)
                     && (Urls.Except(item.Urls).Any() == false)
                     && (item.Urls.Except(Urls).Any() == false);
        }

        return result;
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
}