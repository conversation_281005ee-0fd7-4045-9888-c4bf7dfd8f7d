﻿using System.Text.Json;
using Aveva.Platform.Common.Abstractions.Extensions;

namespace Aveva.Platform.Catalog.Domain.Serialization;

/// <summary>
/// Json serialization options.
/// </summary>
public static class JsonSerializationOptions
{
    /// <summary>
    /// Gets options.
    /// </summary>
    public static JsonSerializerOptions Options => new JsonSerializerOptions().ConfigureDefaults(
        enumsAsStrings: true,
        propertyNameCaseInsensitive: true,
        propertyNamingPolicy: JsonNamingPolicy.CamelCase);
}