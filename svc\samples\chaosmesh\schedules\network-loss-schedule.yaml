apiVersion: chaos-mesh.org/v1alpha1
kind: Schedule
metadata:
  name: schedule-network-loss
  namespace: chaos-mesh
spec:
  schedule: "@every 2m"
  type: "NetworkChaos"
  historyLimit: 5
  concurrencyPolicy: Forbid
  networkChaos:
    action: loss
    mode: all
    selector:
      namespaces:
        - platform-catalog
      labelSelectors:
        pod-selector: catalog-api
    loss:
      loss: "30"           # 30% packet loss
      correlation: '50'
    duration: "2m"
