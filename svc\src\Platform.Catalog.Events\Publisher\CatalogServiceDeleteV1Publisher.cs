﻿using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Events.Logging;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.Common.Messaging.EventBus.Abstractions;
using Aveva.Platform.Common.Messaging.EventBus.Events.Catalog;
using Polly;

namespace Aveva.Platform.Catalog.Events.Publisher
{
    /// <summary>
    /// Catalog ServiceEntry delete event publisher class.
    /// </summary>
    public sealed class CatalogServiceDeleteV1Publisher : IEventPublisher
    {
        private static string _geography = string.Empty;
        private static string _region = string.Empty;
        private readonly IEventBus _eventBus;
        private readonly ICatalogClient _catalogClient;
        private readonly ResiliencePipeline _retryPipeline;
        private readonly ILogger? _logger;
        private readonly EventMetrics _metrics;

        /// <summary>
        /// Initializes a new instance of the <see cref="CatalogServiceDeleteV1Publisher"/> class.
        /// </summary>
        /// <param name="eventBus">Event bus to publish event.</param>
        /// <param name="catalogClient">Catalog Api client.</param>
        /// <param name="geography">Geography where Catalog Event Service is running.</param>
        /// <param name="region">Region where Catalog Event Service is running.</param>
        /// <param name="retryPipeline">Polly ResiliencePipeline instance.</param>
        /// <param name="logger">Instance of ILogger.</param>
        /// <param name="metrics">CatalogMetrics class for instrumentation.</param>
        public CatalogServiceDeleteV1Publisher(
            IEventBus eventBus,
            ICatalogClient catalogClient,
            string geography,
            string region,
            ResiliencePipeline retryPipeline,
            EventMetrics metrics,
            ILogger? logger = null)
        {
            ArgumentNullException.ThrowIfNull(eventBus, nameof(eventBus));
            ArgumentNullException.ThrowIfNull(catalogClient, nameof(catalogClient));
            ArgumentException.ThrowIfNullOrEmpty(geography, nameof(geography));
            ArgumentException.ThrowIfNullOrEmpty(region, nameof(region));
            ArgumentNullException.ThrowIfNull(retryPipeline, nameof(retryPipeline));
            ArgumentNullException.ThrowIfNull(metrics, nameof(EventMetrics));

            _eventBus = eventBus;
            _catalogClient = catalogClient;
            _geography = geography;
            _region = region;
            _retryPipeline = retryPipeline;
            _logger = logger;
            _metrics = metrics;
        }

        /// <inheritdoc/>
        [SuppressMessage("Naming", "CA1031:Do not catch general exception types", Justification = "We will be taking the same action for all exceptions. So specific exception catching is not required.", Scope = "module")]
        public async Task PublishEventAsync(V1ServiceEntry newServiceEntry, V1ServiceEntry? oldServiceEntry = null)
        {
            ArgumentNullException.ThrowIfNull(newServiceEntry, nameof(V1ServiceEntry));
            var serviceId = newServiceEntry.Id;
            var deleteEvent = new CatalogServiceDeleteV1()
            {
                Id = serviceId,
                Geography = _geography,
                Region = _region,
                Lifecycle = ToCatalogServiceLifecycleV1(newServiceEntry.Lifecycle),
            };

            using var activity = CatalogTraceSource.EventsTrace.StartActivity("catalog.newServiceEntry.deleteevent", ActivityKind.Producer);
            {
                var timer = new Stopwatch();
                timer.Start();
                _logger?.CatalogServicEntryDeleteEvent(serviceId, _region);
                try
                {
                    await _retryPipeline.ExecuteAsync(
                    async (token) =>
                    {
                        var existingServiceEntries = await _catalogClient.GetAllAsync().ConfigureAwait(false);
                        if (existingServiceEntries!.Items!.Any(x => x.Id!.Equals(serviceId, StringComparison.InvariantCultureIgnoreCase)))
                        {
                            throw new InvalidDataException($"{serviceId} is not deleted from Catalog service entries.");
                        }
                    },
                    CancellationToken.None).ConfigureAwait(false);

                    await _eventBus.PublishAsync(deleteEvent).ConfigureAwait(false);
                    timer.Stop();
                    _metrics.RecordEvents(V1EventPublishStatus.Success, deleteEvent);
                    _metrics.PublishEventProcessingTime(timer.Elapsed.TotalMilliseconds, deleteEvent);
                }
                catch (Exception ex)
                {
                    activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
                    _metrics.RecordEvents(V1EventPublishStatus.Failure, deleteEvent);
                    activity?.AddException(ex);
                    _logger?.CatalogServicEntryDeleteEventError(serviceId, _region);
                }
            }
        }

        private static CatalogServiceLifecycleV1? ToCatalogServiceLifecycleV1(V1Lifecycle? lifecycle)
        {
            return new CatalogServiceLifecycleV1
            {
                Protocol = ToLifecycleProtocolV1(lifecycle?.Protocol),
                ProviderId = lifecycle?.ProviderId,
            };
        }

        private static LifecycleProtocolV1? ToLifecycleProtocolV1(V1IntegrationProtocol? protocol)
        {
            return protocol switch
            {
                null => null,
                V1IntegrationProtocol.IntegrationEvent => LifecycleProtocolV1.IntegrationEvent,
                _ => throw new ArgumentOutOfRangeException(nameof(protocol)),
            };
        }
    }
}