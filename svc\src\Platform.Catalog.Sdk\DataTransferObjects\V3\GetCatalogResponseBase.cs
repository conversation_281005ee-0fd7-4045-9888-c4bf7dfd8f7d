﻿﻿namespace Aveva.Platform.Catalog.Sdk.DataTransferObjects.V3;

/// <summary>
/// Dto.
/// </summary>
public abstract record GetCatalogResponseBase
{
    #region Public Properties

    /// <summary>
    /// Gets the item identifier.
    /// </summary>
    /// <value>The identifier for an item.</value>
    public string? Id { get; init; }

    /// <summary>
    /// Gets the name of an item.
    /// </summary>
    /// <value>The name of an item.</value>
    public string? DisplayName { get; init; }

    /// <summary>
    /// Gets or sets icon url of the service.
    /// </summary>
    public Uri? IconUrl { get; init; }

    /// <summary>
    /// Gets or sets the description of an item.
    /// </summary>
    /// <value>The description of an item.</value>
    public string? Description { get; init; }

    /// <summary>
    /// Gets or sets dependencies.
    /// </summary>
    /// <remarks>This is not good as the model is not api versioned and exposes unversioned domain models.</remarks>
#pragma warning disable CA2227 // Collection properties should be read only
    public Dictionary<string, CatalogDataDependency>? Dependencies { get; set; }

    /// <summary>
    /// Gets or Sets Applications.
    /// </summary>
    public List<Application>? Applications { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only

    #endregion Public Properties
}