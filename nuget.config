<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <solution>
    <add key="disableSourceControlIntegration" value="true" />
  </solution>
  <config>
    <add key="repositoryPath" value="packages" />
  </config>
  <packageRestore>
    <add key="enabled" value="True" />
    <add key="automatic" value="True" />
  </packageRestore>
  <activePackageSource>
    <add key="All" value="(Aggregate source)" />
  </activePackageSource>
  <packageSources>
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" />
    <add key="CloudPlatform" value="https://pkgs.dev.azure.com/AVEVA-VSTS/_packaging/Cloud-Platform/nuget/v3/index.json" />
  </packageSources>
<!-- Packages must be mapped to target sources when using central package management. -->
<packageSourceMapping>
  <packageSource key="nuget.org">
			<package pattern="*" />
  </packageSource>
  <packageSource key="CloudPlatform">
	<package pattern="Aveva.*" />
	<package pattern="VogueDeputy" />
	<package pattern="OSIsoft.*" />
	<package pattern="Aveva.SDL.BinaryScanner" />
	<package pattern="lessmsi.ProductSecurity" />
	<package pattern="Microsoft.BinScope" />
	<package pattern="ProductSecurity.7zip" />
  </packageSource>
  <packageSource key="feed-CloudPlatform">
			<package pattern="Aveva.*" />
			<package pattern="VogueDeputy" />
			<package pattern="OSIsoft.*" />
  </packageSource>
</packageSourceMapping>	
</configuration>