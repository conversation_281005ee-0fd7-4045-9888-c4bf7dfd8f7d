﻿using System.Diagnostics.Metrics;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Common.Messaging.EventBus.Events.Catalog;

namespace Aveva.Platform.Catalog.Domain.Instrumentation
{
    /// <summary>
    /// A class to define all the meters for tracking Catalog.Events service instrumentation.
    /// </summary>
    public class EventMetrics
    {
        /// <summary>
        /// Catalog events service metrics meter name.
        /// </summary>
        public const string MeterName = "Aveva.Platform.Catalog.Events";
        private static readonly Meter _eventMeter = new Meter(MeterName, "1.0.0");
        private readonly Counter<int> _eventStatus;
        private readonly Counter<double> _eventTimeTaken;

        /// <summary>
        /// Initializes a new instance of the <see cref="EventMetrics"/> class.
        /// </summary>
        public EventMetrics()
        {
            _eventStatus = _eventMeter.CreateCounter<int>("Catalog.ServiceEntries.Events", "Count", "ServiceEntry Events and its status.");
            _eventTimeTaken = _eventMeter.CreateCounter<double>("Catalog.ServiceEntries.EventsProcessingTime", "Time(ms)", "Time taken by Catalog events service to relay events to servicebus.");
        }

        /// <summary>
        /// Records the event.
        /// </summary>
        public void RecordEvents<T>(V1EventPublishStatus status, T catalogEvent) where T : BaseCatalogEventV1
        {
            ArgumentNullException.ThrowIfNull(catalogEvent, nameof(catalogEvent));
            _eventStatus.Add(
                  1,
                  new KeyValuePair<string, object?>("catalog.event", typeof(T).Name),
                  new KeyValuePair<string, object?>("region", catalogEvent.Region),
                  new KeyValuePair<string, object?>("catalog.eventStatus", status.ToString()));
        }

        /// <summary>
        /// Records the event.
        /// </summary>
        public void PublishEventProcessingTime<T>(double timeTaken, T catalogEvent) where T : BaseCatalogEventV1
        {
            ArgumentNullException.ThrowIfNull(catalogEvent, nameof(catalogEvent));
            _eventTimeTaken.Add(
                timeTaken,
                new KeyValuePair<string, object?>("catalog.event", typeof(T).Name),
                new KeyValuePair<string, object?>("region", catalogEvent.Region));
        }
    }
}