﻿using Aveva.Platform.Catalog.Infrastructure.Entities;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Infrastructure.Tests.Unit.K8sCustomResources
{
    [Trait("Category", "Domain")]
    [Trait("Category", "Unit")]
    [Trait("Category", "Domain.Unit")]
    [Trait("Tag", "V1K8sServiceEntryListTests")]
    public class V1K8sServiceEntryListTests
    {
        [Fact]
        public void V1K8sServiceEntryList()
        {
            // Arrange & Act
            V1K8sServiceEntryList newEntry = new V1K8sServiceEntryList()
            {
                ApiVersion = "v1",
                Kind = "ServiceEntry",
            };

            // Assert
            newEntry.ShouldNotBeNull();
            newEntry.Metadata.ShouldBeNull();
            newEntry.Items.ShouldBeNull();
        }
    }
}