/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { type V2RequestBuilder, V2RequestBuilderNavigationMetadata } from './v2/index.js';
// @ts-ignore
import { type BaseRequestBuilder, type KeysToExcludeForNavigationMetadata, type NavigationMetadata } from '@microsoft/kiota-abstractions';

/**
 * Builds and executes requests for operations under /ops/catalog
 */
export interface CatalogRequestBuilder extends BaseRequestBuilder<CatalogRequestBuilder> {
    /**
     * The v2 property
     */
    get v2(): V2RequestBuilder;
}
/**
 * Uri template for the request builder.
 */
export const CatalogRequestBuilderUriTemplate = "{+baseurl}/ops/catalog";
/**
 * Metadata for all the navigation properties in the request builder.
 */
export const CatalogRequestBuilderNavigationMetadata: Record<Exclude<keyof CatalogRequestBuilder, KeysToExcludeForNavigationMetadata>, NavigationMetadata> = {
    v2: {
        navigationMetadata: V2RequestBuilderNavigationMetadata,
    },
};
/* tslint:enable */
/* eslint-enable */
