﻿using Aveva.Platform.Catalog.Domain.Logging;

namespace Aveva.Platform.Catalog.Api.Logging;

/// <summary>
/// Pre-defined event identifiers for this application to use with logging.
/// </summary>
public enum LoggerEvents
{
    /// <summary>
    /// None.
    /// </summary>
    None = 0,

    /// <summary>
    /// The access was unauthorized.
    /// </summary>
    UnAuthorizedAccess = LoggerEventsConstants.UnAuthorizedAccess,

    /// <summary>
    /// k8sWatcher Error.
    /// </summary>
    ServiceEntryWatcherError = LoggerEventsConstants.ApiEventIdRangeStartId + 2,
}