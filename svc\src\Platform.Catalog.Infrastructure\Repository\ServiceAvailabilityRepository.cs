﻿using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Models;

namespace Aveva.Platform.Catalog.Infrastructure.Repository;

/// <summary>
/// Retrieves ServiceAvailabilities.
/// </summary>
public class ServiceAvailabilityRepository() : IServiceAvailabilityRepository
{
    /// <summary>
    /// Retrieves the availability for a service entry.
    /// </summary>
    /// <param name="service">The ServiceEntry.</param>
    /// <param name="accountAvailabilities">The service availability overrides from the account.</param>
    /// <returns>The serviceAvailability of a service for a given account.</returns>
    public V1ServiceAvailability? GetForEntry(V1ServiceEntry service, Dictionary<string, V1ServiceAvailability> accountAvailabilities)
    {
        return GetForEntries([service], accountAvailabilities).First().Value;
    }

    /// <summary>
    /// Retrieves the availability for a list of service entries for a given account.
    /// </summary>
    /// <param name="services">List of serviceEntries.</param>
    /// <param name="accountAvailabilities">The service availability overrides from the account.</param>
    /// <returns>A dictionary mapping the service entries to their availability for the given account.</returns>
    public Dictionary<V1ServiceEntry, V1ServiceAvailability?> GetForEntries(IEnumerable<V1ServiceEntry> services, Dictionary<string, V1ServiceAvailability> accountAvailabilities)
    {
        var servicesWithAvailability = FilterServicesWithAvailability(services);
        var defaultAvailability = V1ServiceAvailability.GetDefault();

        return services.ToDictionary(
            service => service,
            service =>
            {
                if (servicesWithAvailability.Contains(service))
                {
                    accountAvailabilities.TryGetValue(service.Id, out var accountOverrideAvailability);
                    return new V1ServiceAvailability
                    {
                        Enabled = EvaluateAvailability(accountOverrideAvailability?.Enabled, service.Availability?.Enabled, defaultAvailability.Enabled!.Value),
                        Limit = EvaluateAvailability(accountOverrideAvailability?.Limit, service.Availability?.Limit, defaultAvailability.Limit!.Value),
                    };
                }

                return null;
            });
    }

    /// <summary>
    /// Filters the services with availability.
    /// </summary>
    /// <param name="services">The services to be filtered.</param>
    /// <returns>The filtered services.</returns>
    private static IEnumerable<V1ServiceEntry> FilterServicesWithAvailability(IEnumerable<V1ServiceEntry> services)
    {
        return services
            .Where(x => x.Lifecycle != null && x.Lifecycle.Trigger == V1Trigger.Catalog)
            .ToList();
    }

    private static T EvaluateAvailability<T>(T? accountOverride, T? serviceOverride, T fallback)
         where T : struct
    {
        if (accountOverride != null)
        {
            return accountOverride!.Value;
        }

        if (serviceOverride != null)
        {
            return serviceOverride.Value;
        }

        return fallback;
    }
}