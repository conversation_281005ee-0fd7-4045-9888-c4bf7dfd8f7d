﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Aveva.Platform.Catalog.Operator;

/// <summary>
/// Class to add healthchecks for Operator.
/// </summary>
public static class OperatorHealthChecks
{
    private const string LivenessPattern = "/liveness";
    private const string ReadinessPattern = "/readiness";

    /// <summary>
    /// Method to add health checks.
    /// </summary>
    /// <param name="services">Servicecollection.</param>
    public static void AddOperatorHealthChecks(this IServiceCollection services)
    {
        services
            .AddHealthChecks()
            .AddCheck("self", () => HealthCheckResult.Healthy());
    }

    /// <summary>
    /// Method to map the health checks to Liveness/readiness probes.
    /// </summary>
    /// <param name="builder">IEndpointRouteBuilder.</param>
    [ExcludeFromCodeCoverage(Justification = "Not unit testable")]
    public static void MapOperatorHealthChecks(this IEndpointRouteBuilder builder)
    {
        builder.MapHealthChecks(LivenessPattern, new HealthCheckOptions
        {
            Predicate = _ => true,
        }).AllowAnonymous();
        builder.MapHealthChecks(ReadinessPattern, new HealthCheckOptions
        {
            Predicate = _ => true,
        }).AllowAnonymous();
    }
}