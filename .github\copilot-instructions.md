# API and developer documentation guidelines

Instructions for GitHub Copilot when generating customer-facing documentation. This includes documentation for APIs, SDKs, CLIs, and other developer tools. These guidelines apply to both controller-based and minimal API projects.

## Document Overview

This guideline document consists of three major sections:

1. **Universal Documentation Standards**: Core principles and guidelines that apply to all API documentation regardless of implementation approach. Includes grammar, style, operation descriptions, parameter documentation, and example guidelines that should be consistently applied across all APIs.

2. **Controller-based API Documentation**: Specific guidance for documenting traditional ASP.NET APIs using controller classes with XML comments. Details how XML documentation tags map to OpenAPI specification elements and provides guidelines for writing effective summaries, remarks, parameter documentation, and return descriptions.

3. **Minimal API Documentation**: Targeted guidance for documenting the newer minimal API approach that uses route handler delegates with direct endpoint registration. Explains how to use extension methods and attributes to document these APIs and how they map to OpenAPI specification elements.

## Universal documentation standards

### Grammar and style

- Use natural language for documentation strings. Avoid overly technical language.
- Use sentence-style casing (capitalize the first letter of sentences).
- Use present tense rather than past tense (e.g., "Get user information" instead of "Gets user information").
- Use the Oxford comma in lists (e.g., "apples, oranges, and bananas").
- Avoid unnecessary capitalization (e.g., resource names or feature names).
- End all sentences with a period.
- Use language and terms consistent with REST APIs and the OpenAPI specification.
- Don't use C#-specific language like "class" or "model". Prefer agnostic terms that align with REST APIs and the OpenAPI specification.
- Stylize resource names written in pascal case with backticks (e.g., `resourceName`).
- Stylize new terms with italics (e.g., *new term*). Follow new terms immediately with a definition in parentheses.

### OpenAPI Info Description Guidelines

The `info.description` section in your OpenAPI specification provides a crucial high-level overview of your entire service. It's often the first substantial text developers will read when exploring your service documentation. A well-crafted description sets appropriate expectations and helps developers quickly understand if your service meets their needs.

#### Content recommendations for `info.description`

- Start with a concise overview (1-2 sentences) describing what the service does in business terms.
- Explain the core purpose of the service and the primary problem it solves.
- Outline key capabilities the service provides without using bullets or subheadings.
- Write in a flowing narrative style with connected paragraphs.
- Present information in a logical sequence from general to specific details.
- Describe how the service fits into the larger Platform API ecosystem and what differentiates it from related services.
- Focus on business value and use cases rather than technical implementation.
- Keep the total length reasonable (150-300 words) - comprehensive but not overwhelming.
- Use a professional, straightforward tone that reflects your brand voice.
- Avoid technical jargon that isn't necessary for understanding the service's purpose.
- Write with both technical and less technical audiences in mind.
- Do not include authentication, rate limiting, or other platform-wide concerns.
- Avoid mentioning common API conventions or implementation details.
- Do not include links to external documentation, support contacts, or other resources.
- Omit references to specific URLs that may change over time.

#### Example

```md
The Entity Management service provides a standardized way to create, retrieve, update, and delete business entities within the Platform ecosystem. It serves as the central repository for entity data used across multiple applications.

This service enables you to manage entities with flexible property schemas, allowing you to define and store various types of business objects according to your organizational needs. You can create complex entity hierarchies, apply security controls through tagging, and extend basic entities with specialized components.

The entity structure supports both simple and complex data patterns, making it suitable for a wide range of business applications. For advanced scenarios, the service provides specialized endpoints for bulk operations, batched requests, and resolved entity views that include all related data in a single response. These features help optimize performance and reduce complexity in client applications.
```

### Tag description standards

- Use action-oriented, verb-first descriptions that start with a verb like "Manage" or "Perform".
- Keep descriptions concise but informative (typically 1-3 sentences).
- Include a brief definition of any resources unique to the tag group.
- Describe the primary capabilities or operations available within the tag.
- Add practical usage context to help developers understand when to use these endpoints.
- Differentiate similar-sounding tag groups by clearly explaining their distinct purposes.
- Use present tense and consistent terminology throughout all tag descriptions.
- End all descriptions with a period.
- Avoid technical implementation details in favor of business-oriented explanations.

Examples:

- **Entities**: "Manage individual entities. These endpoints support creating, retrieving, updating, and deleting entities, as well as managing security tags and entity ownership. An entity represents a core business resource with properties and relationships."

- **Components**: "Manage components associated with entities. Components are modular pieces of functionality or data that extend an entity's capabilities without changing its core definition. Use these endpoints when working with specialized attributes that may apply to only certain entity types."

- **Batch**: "Perform multiple entity-related operations in a single request. Unlike bulk operations that apply the same action to multiple resources, batch operations allow different action types to be combined in one transaction. Use batch operations when you need to execute several distinct but related changes atomically."

- **Bulk**: "Perform operations on multiple entities at once. These endpoints apply the same operation type across a collection of entities, optimizing performance for high-volume changes. Use bulk operations when you need to apply identical operations to many resources efficiently."

- **ResolvedEntities**: "Query and retrieve resolved entities. Resolved entities include their core properties along with related components and references fully expanded. Use these endpoints when you need complete entity representations with all associated data in a single response."

#### Implementation Note

The `info.description` content should be configured in the repo's startup code. Depending on project structure, add this to either `appsettings.json` or `Program.cs`.

Example:

```csharp
"SwaggerSettings": {
  "Title": "Entity Management",
  "Version": "v1",
  "Description": "The Entity Management service provides a standardized way..."
}
```

### Operation documentation standards

- Use verb-first, action-oriented descriptions for summaries.
- Omit periods at the end of summaries.
- Keep summaries concise (e.g., "Get user information").
- Provide more detailed information in descriptions.
- Use consistent verbs (Get, Create, Update, Delete, Query). Use the singular tense for summaries (such as "Get" or "Create"), but use the plural tense for descriptions (such as "Gets" or "Creates"). See [Word choice](#word-choice) for more guidance.
- Omit all typography styling in summaries (e.g., no bold, italics, or underlines).
- Do not use pascal case for resource names in summaries. Use natural language (e.g., "Get user information" instead of "Get UserInformation").
- Prefer using pascal case decorated with backticks for resource names in descriptions (e.g., `entityId` rather than "entity ID"), but avoid using it in summaries. This helps the audience understand the context of the resource being described.

### Parameter documentation standards

- Describe the parameter's purpose clearly.
- Include periods at the end of descriptions.
- Mention any constraints or formats.
- Use backticks for resource references.
- If a user must provide a parameter, provide detail on how the user can find that information.
- For IDs, explicitly state the resource that it pertains to for clarification, especially in situations where multiple resources and IDs are present.
- Do not use bullet points within parameter descriptions as Swashbuckle doesn't handle them well, resulting in unreadable output. Instead, use comma-delimited lists for simple items or semicolon-delimited lists for more complex items.

### Property documentation standards

- Describe what the property represents.
- Include periods at the end of descriptions.
- Provide context about usage/meaning.
- If a user must provide a property in the request body, provide detail on how the user can find that information.
- For IDs, explicitly state the resource that it pertains to for clarification, especially in situations where multiple resources and IDs are present.
- Do not use bullet points within property descriptions as Swashbuckle doesn't handle them well, resulting in unreadable output. Instead, use comma-delimited lists for simple items or semicolon-delimited lists for more complex items.

### Example guidelines

- Include examples with realistic values.
- Examples should cater to industrial data use cases. Use examples that role play a company named "Windtopia" that is a wind turbine manufacturer.
- Do not include real data in examples. Use placeholders like:
  - Email: `<EMAIL>`
  - Phone: `555-0123`
  - Address: `123 Main St`

### Word choice

- Use the following verbs in summaries and descriptions:
  - `Get` for retrieving data.
  - `Query` for retrieving data with filters or conditions.
  - `Create` for creating new resources.
  - `Update` for modifying existing resources.
  - `Delete` for removing resources.
  - Use the same verb for summary and description.
- Use "request" rather than "call" or "invoke".
- Use `ID` for identifiers, not `Id`, `id`, or `identifier`.
- Use `ID` for identifiers when referring to unique identifiers (e.g., "unique ID", not "unique identifier").
- Use "endpoint" rather than "method", "function", or "operation".
- Use "resource" rather than "object".
- Use "property" rather than "field".
- Use "request body" or "response body" rather than "model" or "schema".
- Use "for example" rather than "e.g.".
- Use "like" rather than "i.e.".

### Branding

- The name for the platform is "CONNECT", not "AVEVA CONNECT" or any other variation. The company wants to emphasize the platform's open nature.
- Use "Stream Store", not "Sequential Data Store" or "SDS". The later is a deprecated term being phased out. It's okay on first mention to add a parenthetical note like "(formerly known as SDS)" to clarify for existing users, but avoid using "SDS" in subsequent mentions.

## Controller-based API documentation

When documenting controller-based APIs with XML comments:

### `<summary>` → OpenAPI Operation Summary

- Maps to `summary` property in OpenAPI operations
- Follow universal operation standards as above.
- Place `<summary>` tags on controller methods.
- Keep summaries focused on what the operation does, not implementation details.

### `<remarks>` → OpenAPI Operation Description

- Maps to `description` property in OpenAPI operations.
- Add a `<remarks>` tag to provide additional details not covered in the `<summary>`.
- Expand on the operation summary, adding more detail.
- Include any special conditions or behaviors.
- Include any important notes or warnings.
- Include any important constraints.
- For "Query" operations:
  - Include a note about the default behavior of returning all resources if no filters are applied.
  - Document if specific filters are designed to be used together or if they are mutually exclusive.
  - Document if the operation supports pagination and how to use it.

### `<param>` → OpenAPI Parameter Object

- Maps to `parameters[].description` in OpenAPI.
- Follow universal parameter standards as above.
- Document each parameter individually with a `<param>` tag.
- End all `<param>` tags with a period.
- For route parameters, ensure the name exactly matches the route template parameter.

### `<returns>` → OpenAPI Response Schema Description

- Maps to `responses.description` and influences response schema documentation
- Describe what the operation returns.
- Include periods at the end.
- Specify the return type in business terms, not technical terms.
- Mention any conditions that affect what's returned.

### `<example>` → OpenAPI Examples

- Maps to `examples` object in the OpenAPI schema
- For each property, include an `<example>` tag with a realistic value.
- Follow the universal example guidelines above.
- Examples appear in the generated OpenAPI documentation and SDK examples.

## Minimal API documentation

When documenting minimal APIs:

### `.WithSummary()` → OpenAPI Operation Summary

- Maps directly to `summary` property in OpenAPI operations.
- Use `.WithSummary()` extension method on endpoint registration.
- Follow the same guidelines as controller `<summary>` tags.

### `.WithDescription()` → OpenAPI Operation Description

- Maps directly to `description` property in OpenAPI operations.
- Use `.WithDescription()` extension method on endpoint registration.
- Include detailed information about endpoint behavior, constraints, and special cases.

### Parameter annotations → OpenAPI Parameter Object

- Maps to parameter definitions in OpenAPI specification.
- Parameter documentation may come from multiple sources:
  - Use XML comments (`///`) on parameters in handler methods, as these may be used by internal libraries.
  - Apply attributes like `[FromRoute]`, `[FromQuery]`, `[FromHeader]` to clarify parameter binding.
  - Use Swashbuckle's `.WithOpenApi()` to further customize parameter documentation when needed.
- For route parameters, ensure the parameter name in code matches the route template parameter exactly.
- Include information about constraints, validation rules, and required/optional status.
- Follow universal parameter documentation standards for all sources of documentation.

### Property documentation in Models → OpenAPI Schema Properties

- For properties in request/response models, the `<value>` tag provides the public-facing documentation.
- Leave `<summary>` tags untouched as they contain developer-focused documentation.
- Treat `<value>` tags the same way property summaries in controller-based projects are treated:
  - Describe what the property represents clearly and concisely.
  - Include periods at the end of descriptions.
  - Provide context about usage and meaning.
  - Mention any constraints or formats the property must follow.
  - Use backticks for resource references.
  - For IDs, explicitly state the resource they pertain to.
  - If a property is required, explain how users can determine appropriate values.

When updating existing model documentation, focus exclusively on improving `<value>` tags while preserving `<summary>` tags as developer reference documentation.

Example:

```csharp
/// <summary>
/// Internal developer note about implementation details of this property.
/// </summary>
/// <value>
/// The unique identifier for this calculation. This ID must be unique across all calculations in the system and is used to reference this specific calculation in other operations.
/// </value>
public string Id { get; set; }
```

### Request/Response annotations → OpenAPI Schema Objects

- Maps to request and response schemas in OpenAPI.
- Use extension methods or attributes to document request and response bodies.
- Ensure all properties of request and response models are properly documented.

### Example annotations → OpenAPI Examples

- Maps to `examples` object in the OpenAPI schema.
- Use appropriate extension methods to provide example requests and responses.
- Examples will appear in generated API documentation.
