namespace: platform-catalog-operator-system
namePrefix: platform-catalog-operator-
commonLabels:
  operator: platform-catalog-operator
resources:
- operator-role.yaml
- operator-role-binding.yaml
- deployment.yaml
- service.yaml
- validators.yaml
- serviceentries_servicecatalog_aveva_com.yaml
- namespace.yaml
images:
- name: operator
  newName: accessible-docker-image
  newTag: latest
configMapGenerator:
- name: webhook-config
  literals:
  - KESTREL__ENDPOINTS__HTTP__URL=http://0.0.0.0:5000
  - KESTREL__ENDPOINTS__HTTPS__URL=https://0.0.0.0:5001
  - KESTREL__ENDPOINTS__HTTPS__CERTIFICATE__PATH=/certs/svc.pem
  - KESTREL__ENDPOINTS__HTTPS__CERTIFICATE__KEYPATH=/certs/svc-key.pem
secretGenerator:
- name: webhook-ca
  files:
  - ca.pem
  - ca-key.pem
- name: webhook-cert
  files:
  - svc.pem
  - svc-key.pem
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization