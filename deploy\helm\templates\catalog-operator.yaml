{{ include "standard.deploymentModeValuesMerge"  . }}

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: '{{ include "operator.name" . }}-role'
  labels:
    operator: '{{ include "operator.name" . }}'
    operator-element: rbac
rules:
- apiGroups:
  - servicecatalog.aveva.com
  - coordination.k8s.io
  - apiextensions.k8s.io
  resources:
  - serviceentries
  - leases
  verbs:
  - '*'
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - get
  - list
  - update 
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: '{{ include "operator.name" . }}-role-binding'
  labels:
    operator: '{{ include "operator.name" . }}'
    operator-element: rbac
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: '{{ include "operator.name" . }}-role'
subjects:
- kind: ServiceAccount
  name: '{{ include "operator.serviceaccount" . }}'
  namespace: {{ .Release.Namespace }}

---

apiVersion: v1
kind: ConfigMap
data:
  KESTREL__ENDPOINTS__HTTP__URL: http://0.0.0.0:8080
  KESTREL__ENDPOINTS__HTTPS__CERTIFICATE__KEYPATH: /certs/tls.key
  KESTREL__ENDPOINTS__HTTPS__CERTIFICATE__PATH: /certs/tls.crt
  KESTREL__ENDPOINTS__HTTPS__URL: https://0.0.0.0:8443
metadata:
  labels:
    operator: '{{ include "operator.name" . }}'
    operator-element: configmap
  name: '{{ include "operator.name" . }}-webhook-config'
  namespace: {{ .Release.Namespace }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
      operator: 'aveva-{{ include "operator.name" . }}'
  name: '{{ include "operator.name" . }}'
  namespace: {{ .Release.Namespace }}
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      operator: 'aveva-{{ include "operator.name" . }}'
  template:
    metadata:
      annotations:
        dapr.io/app-id: ni--catalog--operator
        dapr.io/app-port: '8443'
        dapr.io/enabled: 'true'
        dapr.io/placement-host-address: " "
        dapr.io/sidecar-cpu-limit: '{{ .Values.operator.dapr.sidecarCpuLimit }}'
        dapr.io/sidecar-memory-limit: '{{ .Values.operator.dapr.sidecarMemoryLimit }}'
        dapr.io/sidecar-cpu-request: '{{ .Values.operator.dapr.sidecarCpuRequest }}'
        dapr.io/sidecar-memory-request: '{{ .Values.operator.dapr.sidecarMemoryRequest }}'
      labels:
        operator: 'aveva-{{ include "operator.name" . }}'
    spec:
      containers:
      - env:
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: ASPNETCORE_ENVIRONMENT
          value: {{ eq .Values.deploymentMode "pipeline" | ternary "Production" "Development"  | quote}}
        - name: ASPNETCORE_URLS
          value: http://*:8080;https://*:8443
        - name: Authentication__IdentityDnsZoneName
          value: '{{ .Values.environment.identityDnsZoneName }}'
        - name: Instrumentation__DebugEnabled
          value: '{{ .Values.operator.env.Instrumentation__DebugEnabled }}'
       {{- if .Values.operator.env.Instrumentation__OtelExporterEndpoint }}
        - name: Instrumentation__OtelExporterEndpoint
          value: '{{ .Values.operator.env.Instrumentation__OtelExporterEndpoint }}'
       {{- end }}
       {{- if .Values.operator.env.Authentication__DebugDisabled }}
        - name: Authentication__DebugDisabled
          value: '{{ .Values.operator.env.Authentication__DebugDisabled }}'
        {{- end }}
        - name: OnConnectNamespace
          value: '{{ .Values.operator.onConnectNamespace }}'
        envFrom:
        - configMapRef:
            name: '{{ include "operator.name" . }}-webhook-config'
        image: '{{ include "operator.image" . }}'
        imagePullPolicy: '{{ .Values.operator.image.pullPolicy }}'
        livenessProbe:
          httpGet:
            path: /liveness
            port: http
          initialDelaySeconds: 30
          timeoutSeconds: 1
        securityContext:
          readOnlyRootFilesystem: true
          runAsNonRoot: true
        name: '{{ .Values.operator.image.name }}'
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8443
          name: https
        readinessProbe:
          httpGet:
            path: /readiness
            port: http
          initialDelaySeconds: 15
          timeoutSeconds: 1
        resources: {{- include "operator.resources" . }}
        volumeMounts:
        - mountPath: /certs
          name: certificates
          readOnly: true
        - mountPath: /ca
          name: ca-certificates
          readOnly: true
      imagePullSecrets:
      {{- if .Values.image.pullSecret }}
        - name: {{ .Values.image.pullSecret }}
      {{- end }}
      serviceAccountName: '{{ include "operator.serviceaccount" . }}'
      terminationGracePeriodSeconds: 10
      volumes:
      - name: certificates
        secret:
          secretName: '{{ include "operator.servingCertificate" . }}'  
      - name: ca-certificates
        secret:
          secretName: '{{ include "operator.rootCACertificate" . }}'  
---
apiVersion: v1
kind: Service
metadata:
  labels:
      operator: 'aveva-{{ include "operator.name" . }}'
  name: '{{ include "operator.name" . }}'
spec:
  ports:
  - name: https
    port: 443
    targetPort: 8443
  selector:
    operator: 'aveva-{{ include "operator.name" . }}'
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  annotations:
    cert-manager.io/inject-ca-from: '{{ .Release.Namespace }}/{{ include "operator.servingCertificate" . }}'
  name: 'validators.{{ include "operator.name" . }}'
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: '{{ include "operator.name" . }}'
      namespace: '{{ .Release.Namespace }}'
      path: /validate/v1serviceentry
  matchPolicy: Exact
  name: validate.serviceentry.servicecatalog.aveva.com.v1
  rules:
  - apiGroups:
    - servicecatalog.aveva.com
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    - DELETE
    resources:
    - serviceentries
  sideEffects: None
  timeoutSeconds: {{ .Values.operator.timeoutSeconds }}