﻿namespace Aveva.Platform.Catalog.Domain.Models
{
    /// <summary>
    /// Protocol options for service entries, supporting both legacy and webhook protocols.
    /// </summary>
    public class V1ProtocolOptions
    {
        /// <summary>
        /// The legacy SCM solution definition identifier for backward compatibility with legacy systems.
        /// </summary>
        public string? SolutionDefinition { get; set; }

        /// <summary>
        /// The mappings configuration for the legacy protocol, defining how service aspects translate to the legacy SCM system.
        /// </summary>
        public V1LegacyProtocolMappings? Mappings { get; set; }

        /// <summary>
        /// The webhook URI for webhook protocol communication.
        /// </summary>
        public Uri? WebhookUri { get; set; }

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is V1ProtocolOptions item
                && string.Equals(item.SolutionDefinition, SolutionDefinition, StringComparison.InvariantCultureIgnoreCase)
                && ((item.Mappings == null && Mappings == null) || (item.Mappings != null && item.Mappings.Equals(Mappings)))
                && ((item.WebhookUri == null && WebhookUri == null) || (item.WebhookUri != null && item.WebhookUri.Equals(WebhookUri)));
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return HashCode.Combine(
                SolutionDefinition?.ToUpperInvariant(),
                Mappings,
                WebhookUri);
        }
    }
}