// <auto-generated/>
#pragma warning disable CS0618
using Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2
{
    /// <summary>
    /// Builds and executes requests for operations under \api\account\{accountId}\catalog\v2
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class V2RequestBuilder : BaseRequestBuilder
    {
        /// <summary>The services property</summary>
        public global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder Services
        {
            get => new global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.Services.ServicesRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.V2RequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public V2RequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/account/{accountId}/catalog/v2", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.V2RequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public V2RequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/account/{accountId}/catalog/v2", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
