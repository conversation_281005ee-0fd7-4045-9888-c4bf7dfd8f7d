{{- define "infrastructure.alerts" }}
{{- if eq (include "helpers.isStandardCoreOrPerformanceEnv" .) "true" }}
alerts:
  {{- if eq .Values.environment.isStandardCoreEnvironment "true" }}
  actionGroups:
  - name: "{{ include "helpers.teamsChannelActionGroupName" $ }}"
    groupShortName: "PCTC-1"
    webHookReceiverDetails:
    - webhookName: "webhook-catalog" # The webhook handles directing the message to the desired channel
      webhookUrl: "{{ include "helpers.teamWebhookUrl" $ }}"
  {{- end }}
  ruleGroups:
  - name: {{ printf "%s-rulegroup-%s" .Values.daprId.serviceId .Values.geography.name }}
    description: "Catalog alerts"
    rules:
    - type: alert
      name: CatalogServiceEntriesMissing
      expression: '((catalog_serviceentries_count offset 10m == 0 ) and on(instance) catalog_serviceentries_count offset 15m > 0)'
      labels:
        service: 'catalog'
      severity: 1
      annotations:
        description: "Number of service entry count is 0 in Catalog api."
      resolveConfiguration:
        autoResolved: true
        timeToResolve: 'PT10M'
      {{ include "helpers.actionGroupReference" $ | indent 6 }}
    - type: alert
      name: CatalogWatcherOff
      expression: '((catalog_k8swatcher_status_on_off offset 1m == 0 ) and ignoring (time) (catalog_k8swatcher_status_on_off offset 2m == 1))'
      labels:
        service: 'catalog'
      severity: 1
      annotations:
        description: "Catalog api k8s watcher could be in error state."
      resolveConfiguration:
        autoResolved: true
        timeToResolve: 'PT10M'
      {{ include "helpers.actionGroupReference" $ | indent 6 }}              
    - type: alert
      name: CatalogServiceEntriesEventFailure
      expression: 'increase(catalog_serviceentries_events_count_total{catalog_eventstatus="failure"}[1m])'
      labels:
        service: 'catalog'
      severity: 1
      annotations:
        description: "Service entry create or update or delete event trigger failure."
      resolveConfiguration:
        autoResolved: true
        timeToResolve: 'PT10M'  
      {{ include "helpers.actionGroupReference" $ | indent 6 }}
  sloDefinitions:
  - slos:
    - name: "readiness-successrate"
      description: "Success rate of readiness health check"
      objectivePercentage: "95.0"
      objectiveDecimal: "0.950"
      errorQuery: 'sum(rate(http_server_request_duration_seconds_count{aveva_platform_service_name="catalog", http_route="/readiness", http_response_status_code!="200"}[%s]))'
      totalQuery: 'sum(rate(http_server_request_duration_seconds_count{aveva_platform_service_name="catalog", http_route="/readiness"}[%s]))'
      {{ include "helpers.actionGroupReference" $ | indent 6 }}
    - name: "allrequests-successrate"
      description: "Success rate of all HTTP requests"
      objectivePercentage: "99.0"
      objectiveDecimal: "0.990"
      errorQuery: 'sum(rate(http_server_request_duration_seconds_count{aveva_platform_service_name="catalog", http_response_status_code=~"5.."}[%s]))'
      totalQuery: 'sum(rate(http_server_request_duration_seconds_count{aveva_platform_service_name="catalog"}[%s]))'
      {{ include "helpers.actionGroupReference" $ | indent 6 }}
{{- end }}
{{- end -}}