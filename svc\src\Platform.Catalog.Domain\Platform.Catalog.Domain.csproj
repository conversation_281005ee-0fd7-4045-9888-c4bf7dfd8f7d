﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <!-- Assembly properties -->
  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.Domain</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.Domain</RootNamespace>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Aveva.Platform.Authorization.Sdk" />
    <PackageReference Include="Aveva.Platform.Common.Framework.Abstractions" />
    <PackageReference Include="Aveva.Platform.Common.Framework.Mapping.AutoMapper" />
    <PackageReference Include="Aveva.Platform.Common.Messaging.EventBus.Events" />
    <PackageReference Include="KubernetesClient" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
    <PackageReference Include="System.Net.Http" />
    <PackageReference Include="System.Text.RegularExpressions" />
    <PackageReference Include="Newtonsoft.Json" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>