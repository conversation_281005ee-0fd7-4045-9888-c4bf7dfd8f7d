﻿using Aveva.Platform.Catalog.Domain.Models;

namespace Aveva.Platform.Catalog.ServiceClient.Catalog
{
    /// <summary>
    /// Interface for Catalog API service client.
    /// </summary>
    public interface ICatalogEventClient
    {
        /// <summary>
        /// Calls Catalog events internal endpoints to create the events.
        /// </summary>
        public Task PublishEventAsync(string serviceEntryEventType, V1ServiceEntry serviceEntry);

        /// <summary>
        /// Calls Catalog events update endpoint to update the events.
        /// </summary>
        /// <param name="newServiceEntry">The new service entry.</param>
        /// <param name="oldServiceEntry">The old service entry.</param>
        Task PublishUpdateEventAsync(V1ServiceEntry newServiceEntry, V1ServiceEntry oldServiceEntry);
    }
}