{"Logging": {"LogLevel": {"Default": "Trace", "Microsoft.AspNetCore": "Warning"}}, "Apis": {"CatalogService": {"serviceIdentityId": "catalog", "BaseServiceUrl": "http://ni--catalog--api.platform-catalog", "GetAllServiceEntries": "ops/v2/services"}, "CatalogEventsService": {"serviceIdentityId": "catalog", "BaseServiceUrl": "http://ni--catalog--events.platform-catalog", "PostEvents": "internal/v1/eventtype/"}}, "Authentication": {"OverrideAuthenticationForLocalTesting": true}, "Instrumentation": {"DebugEnabled": "false", "RoleName": "catalog-operator", "ServiceName": "catalog", "SamplingProbability": 1.0}}