applicationDetails:
  name: {{ .Values.daprId.serviceId | quote }}
  version: "1.1.0"
  area: "core-services"
  team: "cloud_platform"
  region: {{ .Values.region.region | quote }}

{{ include "daprResources" . }}

{{ include "secrets" . }}

{{- if eq .Values.deploymentMode "pipeline" }}
infrastructure:
  nameOverride: catalog
  {{ include "infrastructure.alerts" . | nindent 2 }}
  {{ include "infrastructure.identities" . | nindent 2 }}
  skipGeographicServiceBus: true
  skipMonitorWorkspaceRoleAssignment: true
{{- end }}

{{- if and .Values.instance .Values.instance.workloadIdentity }}
deployments:
{{ include "deployments.api" . }}
{{ include "deployments.events" . }}

{{- end }}