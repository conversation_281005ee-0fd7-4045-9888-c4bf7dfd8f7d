buildVariables:
  packageVersion: 0.1377.5619921
  tag: 0.1377.5619921
deploymentMode: pipeline
envBicepOp: {}
environment:
  connectAdminPortalApplicationId: ed95607d-e396-4d6d-a4a4-462b649e4b09
  connectOpsPortalApplicationId: 8b01c4f6-e0e8-46af-8283-1064a1862d4b
  connectOpsPortalScopeUri: api://dev-ConnectOps-App/ops
  connectV1IntegrationAudience: https://services.dev-connect.aveva.com
  connectV1IntegrationAuthority: https://signin.dev-connect.aveva.com
  connectV1IntegrationBaseUrl: https://services.dev-connect.aveva.com
  connectV1IntegrationDirection: both
  connectV1IntegrationEnabled: "True"
  connectV1IntegrationLegacyUrl: https://api.dev-connect.aveva.com
  connectV1IntegrationProfileServiceUrl: https://profile.dev-connect.aveva.com
  connectV1IntegrationV1Environment: dev
  containerRegistryName: cloudplatformbuildregistry
  containerRegistryServer: cloudplatformbuildregistry.azurecr.io
  dnsZoneName: infradev.platform.capdev-connect.aveva.com
  environment: infradev
  environmentLevel: dev
  environmentSubscriptionId: 5c49443f-ce4b-470d-8ba8-c9571c1de06d
  environmentType: development
  frontDoorLocation: Global
  frontDoorName: afd-infradev-gl
  geographiesList: '[{"id":"us","name":"United States","primaryGeography":"True","regions":[{"id":"westus3","name":"West
    United States","primaryRegion":"True","regionCode":"usw3"}],"primaryRegionId":"westus3"},{"id":"eu","name":"Europe","regions":[{"id":"northeurope","name":"North
    Europe","regionCode":"euno"}],"primaryRegionId":"northeurope"}]'
  globalKeyVaultName: kv-infradev-gl-nocciadlc
  globalResourceGroupName: rg-infradev-gl-usw3
  globalServiceBusConnectionStringSecretName: sb-infradev-gl-r3ttrwhxkql7o-connectionstring
  globalServiceBusName: sb-infradev-gl-r3ttrwhxkql7o
  identityDnsZoneName: identity.infradev.platform.capdev-connect.aveva.com
  identityKeyVaultName: kv-infradev-gl-ident-noc
  identityTestAuthCodeClientApplicationId: 5cac0034-9944-48f5-b645-3d108acca375
  isStandardCoreEnvironment: "true"
  isStandardEnvironment: "true"
  logAnalyticsWorkspaceName: log-infradev-gl
  opsDnsZoneName: ops.infradev.portal.capdev-connect.aveva.com
  piToConnectApplicationId: 8f4f44bc-7f17-4c76-b612-41d2734ebd6e
  piUsageKeyVaultName: kv-infradev-gl-piusg-noc
  portalDnsZoneName: infradev.portal.capdev-connect.aveva.com
  powerBiApplicationId: 6b06ecdd-7c91-412d-b003-50856f3c4688
  primaryGlobalLocation: northeurope
  regionNames:
  - northeurope
  - westus3
  sdsUsageKeyVaultName: kv-infradev-gl-sdsug-noc
  tenantId: d1fe8e44-4d35-4dad-8ea9-d9d9cb9a988c
geoBicepOp: {}
geography:
  adxBackupContainerName: ""
  adxBackupStorageAccountName: ""
  adxClusterName: ""
  adxClusterUri: ""
  adxDatabaseName: ""
  adxLogsMigratorClientIdName: ""
  adxLogsMigratorClientSecretName: ""
  adxLogsReaderClientIdName: ""
  adxLogsReaderClientSecretName: ""
  applicationInsightsConnectionStringSecretName: appi-infradev-eu-connectionstring
  applicationInsightsInstrumentationKeySecretName: appi-infradev-eu-instrumentationkey
  applicationInsightsName: appi-infradev-eu
  asoWorkloadIdentityClientId: 4aab1876-c04f-4ef6-9ae4-9e792d18cd12
  asoWorkloadIdentityTenantId: d1fe8e44-4d35-4dad-8ea9-d9d9cb9a988c
  logAnalyticsWorkspaceName: log-infradev-eu
  monitoringActionGroupId: /subscriptions/5c49443f-ce4b-470d-8ba8-c9571c1de06d/resourceGroups/monitoring-actiongroup-dev-rg/providers/Microsoft.Insights/actionGroups/monitoring-actiongroup-dev
  name: eu
  primaryRegion: northeurope
  prometheusQueryEndpoint: https://amws-infradev-eu-fhbggeexg8ekguan.northeurope.prometheus.monitor.azure.com
  prometheusQueryEndpointClientId: infradev-northeurope-prometheus-data-reader-clientid
  prometheusQueryEndpointClientSecret: infradev-northeurope-prometheus-data-reader-secret
  regionNames:
  - northeurope
  resourceGroupName: rg-infradev-eu
  serviceBusConnectionStringSecretName: sb-infradev-eu-5jz4mgqdyb4bc-connectionstring
  serviceBusName: sb-infradev-eu-5jz4mgqdyb4bc
instance:
  workloadIdentity: 79d105a0-7e25-44f8-8de7-3d768ddef631
region:
  aksClusterName: aks-infradev-euno
  aksControlPlanePrincipalId: 275173f2-16e9-46c3-abfd-415c240f3b54
  aksKeyVaultSecretsProviderClientId: 2b5c5fc2-7938-4192-8eda-4e6b0eac5bc3
  aksKeyVaultSecretsProviderPrincipalId: 9960029c-5777-437a-abd9-70991b0effb6
  aksKubeletIdentityPrincipalId: ded50f20-5603-4fdf-9261-ea44b2a5c54e
  aksOidcIssuerUrl: https://northeurope.oic.prod-aks.azure.com/d1fe8e44-4d35-4dad-8ea9-d9d9cb9a988c/eca99afd-5e42-4675-9168-c2683f19c6a1/
  aksTenantId: d1fe8e44-4d35-4dad-8ea9-d9d9cb9a988c
  geography: eu
  nodeResourceGroupName: rg-infradev-euno-node-aks
  plsName: pl-infradev-euno-gat-dgyi
  region: northeurope
  regionalKeyVaultName: kv-infradev-euno-dgyi3xa
  regionalResourceGroupName: rg-infradev-euno
  regioncode: euno
timestamp: "2025-04-14T18:01:45.3313526Z"