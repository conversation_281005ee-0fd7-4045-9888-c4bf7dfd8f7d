{
  "Logging": {
    "LogLevel": {
      "Default": "Trace",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Application": {
    "EnableSwaggerUI": "true",
    "SwaggerServiceTitle": "Catalog Event Service API",
    "SwaggerServiceDescription": "An API for triggering catalog service entry events"
  },
  "Authentication": {
    "ServiceId": "catalog",
    "IdentityDnsZoneName": "https://devonebox.platform.capdev-connect.aveva.com",
    "TurnOffHttpsRequiredForTokenValidation": true,
    "OverrideAuthenticationForLocalTesting": true
  },
  "Instrumentation": {
    "DebugEnabled": "false", //Should always be true for local development.
    // "OtelExporterEndpoint":  "http://localhost:4317",
    "RoleName": "catalog-events",
    "ServiceName": "catalog",
    "SamplingProbability": 1.0 //custom traces needs this value for tracing.
  },
  "Apis": {
    "CatalogService": {
      "serviceIdentityId": "catalog",
      "BaseServiceUrl": "http://ni--catalog--api.platform-catalog",
      "GetAllServiceEntries": "ops/v2/services"
    },
    "CatalogEventsService": {
      "serviceIdentityId": "catalog",
      "BaseServiceUrl": "http://ni--catalog--events.platform-catalog",
      "PostEvents": "internal/v1/eventtype/"
    }
  },
  "geography": "eu",
  "region": "usw3"
}