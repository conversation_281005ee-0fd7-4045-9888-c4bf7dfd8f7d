---
# Create a selfsigned Issuer, in order to create a root CA certificate for
# signing webhook serving certificates
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: {{ include "operator.selfSignedIssuer" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ include "operator.name" . }}
    chart: {{ include "operator.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  selfSigned: {}
---
# Generate a CA Certificate used to sign certificates for the webhook
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{ include "operator.rootCACertificate" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ include "operator.name" . }}
    chart: {{ include "operator.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  secretName: {{ include "operator.rootCACertificate" . }}
  duration: 43800h # 5y
  issuerRef:
    name: {{ include "operator.selfSignedIssuer" . }}
  commonName: "ca.operator.cert-manager"
  isCA: true

---
# Create an Issuer that uses the above generated CA certificate to issue certs
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: {{ include "operator.rootCAIssuer" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ include "operator.name" . }}
    chart: {{ include "operator.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  ca:
    secretName: {{ include "operator.rootCACertificate" . }}
---
# Finally, generate a serving certificate for the webhook to use
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{ include "operator.servingCertificate" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    app: {{ include "operator.name" . }}
    chart: {{ include "operator.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  secretName: {{ include "operator.servingCertificate" . }}
  duration: 8760h # 1y
  issuerRef:
    name: {{ include "operator.rootCAIssuer" . }}
  dnsNames:
  - {{ include "operator.name" . }}
  - {{ include "operator.name" . }}.{{ .Release.Namespace }}
  - {{ include "operator.name" . }}.{{ .Release.Namespace }}.svc
