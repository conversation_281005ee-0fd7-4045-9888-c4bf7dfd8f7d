﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;

/// <summary>
/// Represents an external identity that can be associated with a service. External identities provide integration points with third-party authentication and authorization systems.
/// </summary>
public class ExternalIdentity
{
    /// <summary>
    /// The unique identifier for this external identity. This ID is used to reference this specific identity in other operations.
    /// </summary>
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
    public string Id { get; set; }

    /// <summary>
    /// The type of external identity. This determines the authentication system and protocol used by this identity.
    /// </summary>
    public ExternalIdentityType Type { get; set; }

    /// <summary>
    /// The authorization scopes associated with this external identity. Each scope represents a specific permission or access level granted to the identity.
    /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
    public List<ExternalIdentityScope> Scopes { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        if (obj is not ExternalIdentity item)
        {
            return false;
        }

        var result = item.Id.Equals(Id, StringComparison.OrdinalIgnoreCase)
            && item.Type.Equals(Type);

        if (Scopes == null && item.Scopes != null)
        {
            if (item.Scopes.Count > 0)
            {
                return false;
            }
        }

        if (Scopes != null && item.Scopes == null)
        {
            if (Scopes.Count > 0)
            {
                return false;
            }
        }

        if (Scopes != null && item.Scopes != null)
        {
            result = result
                     && (Scopes.Count == item.Scopes.Count)
                     && !(from scope in Scopes
                          let match = item.Scopes.Contains(scope)
                          where !match
                          select scope).Any();
        }

        return result;
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
}