﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;

/// <summary>
/// Defines the classification categories for services in the catalog. Categories help organize services based on their primary purpose or function.
/// </summary>
public enum Category
{
    /// <summary>
    /// Services focused on data storage, processing, and management. This category includes services that primarily work with structured and unstructured data, such as databases, analytics engines, and data transformation tools.
    /// </summary>
    Data,

    /// <summary>
    /// Services for data acquisition and input handling. This category includes services that collect, validate, and process incoming data from various sources like industrial equipment, IoT devices, and external systems.
    /// </summary>
    Ingress,
}