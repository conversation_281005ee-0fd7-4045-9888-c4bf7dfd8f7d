﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Api.v2
{
    /// <summary>
    /// Represents a collection of service catalog entries returned by the API.
    /// </summary>
    public class ServiceCollectionResponse
    {
        /// <summary>
        /// The list of catalog services available to the account. Each item contains detailed information about a service including its capabilities, requirements, and provisioning details.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public List<ServiceResponse>? Items { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only
    }
}