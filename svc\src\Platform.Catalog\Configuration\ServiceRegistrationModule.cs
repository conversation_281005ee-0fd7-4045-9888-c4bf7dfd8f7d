﻿using Aveva.Platform.Authentication.Sdk.Server.Extensions;
using Aveva.Platform.Authorization.Sdk;
using Aveva.Platform.Authorization.Sdk.Domain.Models;
using Aveva.Platform.Catalog.Domain;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Watchers;
using Aveva.Platform.Common.Framework.Abstractions.Configuration;
using Aveva.Platform.Common.Framework.AspNetCore.Configuration;
using Aveva.Platform.Common.Monitoring.Instrumentation;
using k8s;
using Microsoft.AspNetCore.Authorization;

namespace Aveva.Platform.Catalog.Configuration;

/// <summary>
/// Initializes a new instance of the <see cref="ServiceRegistrationModule"/> class.
/// </summary>
/// <param name="configuration">The configuration.</param>
/// <exception cref="ArgumentNullException">configuration is <c>null</c>.</exception>
internal sealed class ServiceRegistrationModule(IConfiguration configuration) : IServiceRegistrationModule
{
    #region Private Fields

    private readonly IConfiguration _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

    #endregion Private Fields

    #region Public Methods

    /// <inheritdoc/>
    public void AddServices(IServiceCollection services)
    {
        // Configure the application settings.
        services
            .AddOptions<ApplicationOptions>()
            .Bind(_configuration.GetSection(ApplicationOptions.DefaultConfigurationSectionName));

        services.AddOtelConfiguration(_configuration);
        services.AddOpenTelemetry()
        .WithTracing(tracerProviderBuilder =>
        {
            // Add a custom Activity Source for traces
            tracerProviderBuilder.AddSource(CatalogTraceSource.WatcherTraceName);
        })
        .WithMetrics(meterProviderBuilder =>
        {
            // Add a custom Meter for metrics
            meterProviderBuilder.AddMeter(CatalogMetrics.MeterName);
        });

        // Add Identity service SDKs.
        services.AddPlatformAuthentication(options =>
        {
            _configuration.GetSection("Authentication").Bind(options);
        });

        var aspNetCoreEnvironment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

        if (string.Equals(aspNetCoreEnvironment, Environments.Development, StringComparison.OrdinalIgnoreCase))
        {
            services.AddPlatformAuthorization(ServiceId.Parse(CatalogConstants.ServiceId), authorizationOptions =>
            {
                authorizationOptions.EnableAuthorizationBypass = true;
            });
        }
        else
        {
            services.AddPlatformAuthorization(ServiceId.Parse(CatalogConstants.ServiceId), authorizationOptions =>
            {
                authorizationOptions.MemoryCacheEnabled = true;
                authorizationOptions.EnableImplicitAuthorization = true;
            });
        }

        services.AddAuthorization(options =>
        {
            options.FallbackPolicy = new AuthorizationPolicyBuilder()
                .RequireAuthenticatedUser()
                .Build();
        });

        KubernetesClientConfiguration k8sConfig = new KubernetesClientConfiguration();

        if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != null)
        {
            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")!.Equals(Environments.Development, StringComparison.OrdinalIgnoreCase))
            {
                k8sConfig = KubernetesClientConfiguration.BuildDefaultConfig();
            }
            else if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("KUBERNETES_SERVICE_HOST")))
            {
                k8sConfig = KubernetesClientConfiguration.InClusterConfig();
            }
        }

        services.AddSingleton<IKubernetes>(x => new Kubernetes(k8sConfig));

        if (!string.IsNullOrEmpty(k8sConfig.Host))
        {
            services.AddHostedService<V1ServiceEntryWatcher>();
        }
    }

    #endregion Public Methods
}