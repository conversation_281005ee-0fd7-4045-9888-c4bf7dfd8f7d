﻿using System.Text.Json.Serialization;
using k8s;
using k8s.Models;

namespace Aveva.Platform.Catalog.Domain.K8sCustomResources
{
    /// <summary>
    /// Base class for Kubernetes custom resources.
    /// </summary>
#pragma warning disable SA1402 // File may only contain a single type
    public abstract class K8sCustomResource : KubernetesObject, IMetadata<V1ObjectMeta>
    {
        /// <summary>
        /// Gets or Sets Kubernetes metadata property.
        /// Holds values like Namespace, creationtime etc.
        /// </summary>
        [JsonPropertyName("metadata")]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public V1ObjectMeta Metadata { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }

    /// <summary>
    /// K8sCustomResource class extended to include Spec and Status property.
    /// </summary>
    /// <typeparam name="TSpec">Kubernetes objects specification.</typeparam>
    /// <typeparam name="TStatus">Kubernetes objects status.</typeparam>
    public abstract class K8sCustomResource<TSpec, TStatus> : K8sCustomResource
    {
        /// <summary>
        /// Kuberntes objects Spec property.
        /// Values of the kubernetes object.
        /// </summary>
        [JsonPropertyName("spec")]
        public TSpec? Spec { get; set; }

        /// <summary>
        /// Kubernetes objects status property.
        /// </summary>
        [JsonPropertyName("status")]
        public TStatus? Status { get; set; }
    }
#pragma warning disable SA1402 // File may only contain a single type

}