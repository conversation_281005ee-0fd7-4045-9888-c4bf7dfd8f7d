﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
  </PropertyGroup>

  <PropertyGroup>
    <UserSecretsId>a955337f-564a-4203-891d-7e6affc6265c</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <EnableSdkContainerSupport>true</EnableSdkContainerSupport>
    <RootNamespace>Aveva.Platform.Catalog</RootNamespace>
    <AssemblyName>Aveva.Platform.Catalog</AssemblyName>
    <ContainerRuntimeIdentifier>linux-x64</ContainerRuntimeIdentifier>
    <ContainerImageName>aveva-platform-catalog-api</ContainerImageName>
    <ContainerImageTag>latest</ContainerImageTag>
    <OutputType>EXE</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aveva.Platform.Authentication.Sdk.Server" />
    <PackageReference Include="Aveva.Platform.Authorization.Sdk" />
    <PackageReference Include="Aveva.Platform.Common.Abstractions" />
    <PackageReference Include="Aveva.Platform.Common.Framework.ExceptionHandling" />
    <PackageReference Include="Aveva.Platform.Common.Framework.Mapping.AutoMapper" />
    <PackageReference Include="Aveva.Platform.Common.Monitoring.Instrumentation" />
    <PackageReference Include="Dapr.Client" />
    <PackageReference Include="Dapr.AspNetCore" />
    <PackageReference Include="Dapr.Extensions.Configuration" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Platform.Catalog.Api\Platform.Catalog.Api.csproj" />
    <ProjectReference Include="..\Platform.Catalog.Infrastructure\Platform.Catalog.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <ItemGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <Content Remove="appsettings.Development.json" />
  </ItemGroup>

  <PropertyGroup>
    <ApiV2ClientNamespace>Aveva.Platform.Catalog.Client.V2.Api</ApiV2ClientNamespace>
    <OpsV2ClientNamespace>Aveva.Platform.Catalog.Client.V2.Ops</OpsV2ClientNamespace>
  </PropertyGroup>
  
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="dotnet tool restore" />

    <Exec Command="dotnet swagger tofile --output $(OutputPath)platform-catalog-operations-v2-swagger.json $(OutputPath)$(AssemblyName).dll operations-v2" EnvironmentVariables="DOTNET_ROLL_FORWARD=LatestMajor" />
    <Exec Command="dotnet aveva-gateway create-openapidoc-cr --displayName &quot;Catalog&quot; --serviceApi &quot;$(ProjectDir)..\..\..\deploy\crhelm\templates\gateway\service-api-ops-v2.yaml&quot; --swagger &quot;$(OutputPath)platform-catalog-operations-v2-swagger.json&quot;" />
    <Exec Command="dotnet generatepublicswagger --openApi &quot;$(ProjectDir)..\..\..\deploy\crhelm\templates\gateway\open-api-ops-service-v2.yaml&quot; --output &quot;$(OutputPath)platform-catalog-public-operations-v2-swagger.json&quot; " />

    <Exec Command="dotnet swagger tofile --output $(OutputPath)platform-catalog-api-v2-swagger.json $(OutputPath)$(AssemblyName).dll api-v2" EnvironmentVariables="DOTNET_ROLL_FORWARD=LatestMajor" />
    <Exec Command="dotnet aveva-gateway create-openapidoc-cr --displayName &quot;Catalog&quot; --serviceApi &quot;$(ProjectDir)..\..\..\deploy\crhelm\templates\gateway\service-api-customer-v2.yaml&quot; --pathPattern &quot;/api/account/{accountId}/v2/{**catch-all}&quot; --swagger &quot;$(OutputPath)platform-catalog-api-v2-swagger.json&quot;" />
    <Exec Command="dotnet generatepublicswagger --openApi &quot;$(ProjectDir)..\..\..\deploy\crhelm\templates\gateway\open-api-customer-accountserviceinstance-v2.yaml&quot; --output &quot;$(OutputPath)platform-catalog-public-api-v2-swagger.json&quot; " />

  </Target>
  <Target Name="Kiota" AfterTargets="PostBuild">
    <!--Generate Kiota C sharp Clients for Catalog v2 api-->
    <Exec Command="dotnet kiota generate --openapi &quot;$(OutputPath)platform-catalog-public-operations-v2-swagger.json&quot; -n $(OpsV2ClientNamespace) -o ../../../lib/Aveva.Platform.Library.Catalog/svc/src/Aveva.Platform.Catalog.Client/V2/Ops -l CSharp -c Client --log-level error --clean-output true --exclude-backward-compatible true" />
    <Exec Command="dotnet kiota generate --openapi &quot;$(OutputPath)platform-catalog-public-api-v2-swagger.json&quot; -n $(ApiV2ClientNamespace) -o ../../../lib/Aveva.Platform.Library.Catalog/svc/src/Aveva.Platform.Catalog.Client/V2/Api -l CSharp -c Client --log-level error --clean-output true --exclude-backward-compatible true" />

    <!--Generate kiota typescript client-->
    <Exec Command="dotnet kiota generate --openapi &quot;$(OutputPath)platform-catalog-public-operations-v2-swagger.json&quot; -n $(OpsV2ClientNamespace) -o ../../../lib/Aveva.Platform.Library.Catalog/svc/src/Aveva.Platform.Catalog.TSClient/V2/Ops -l typescript -c Client --log-level error --clean-output true --exclude-backward-compatible true" />
    <Exec Command="dotnet kiota generate --openapi &quot;$(OutputPath)/platform-catalog-public-api-v2-swagger.json&quot; -n $(ApiV2ClientNamespace) -o ../../../lib/Aveva.Platform.Library.Catalog/svc/src/Aveva.Platform.Catalog.TSClient/V2/Api -l typescript -c Client --log-level error --clean-output true --exclude-backward-compatible true" />
  </Target>
</Project>