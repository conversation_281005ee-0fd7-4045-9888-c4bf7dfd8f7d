<Project>
  <!-- 
        Shared build properties file - reference: https://docs.microsoft.com/en-us/visualstudio/msbuild/customize-your-build
        Hierarchical if you add this to child Directory.Build.props files:
        <Import Project="$([MSBuild]::GetPathOfFileAbove($(MSBuildThisFile), $(MSBuildThisFileDirectory)..))" />
  -->
  <!-- Automatically apply the solution .runsettings for all tests to apply standard settings and properly scope code coverage -->
  <PropertyGroup Condition="Exists('$(MSBuildThisFileDirectory)..\..\Omf.Validation.runsettings')">
    <RunSettingsFilePath>$(MSBuildThisFileDirectory)..\..\Omf.Validation.runsettings</RunSettingsFilePath>
  </PropertyGroup>

  <Import Project="$([MSBuild]::GetPathOfFileAbove($(MSBuildThisFile), $(MSBuildThisFileDirectory)..))" />
</Project>