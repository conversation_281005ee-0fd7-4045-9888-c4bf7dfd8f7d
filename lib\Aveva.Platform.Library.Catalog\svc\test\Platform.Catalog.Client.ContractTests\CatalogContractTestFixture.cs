﻿using Aveva.Platform.Common.Testing.ApiContracts.Common;
using Aveva.Platform.Common.Testing.ApiContracts.Fixtures;

namespace Aveva.Platform.Catalog.ContractTests
{
    /// <summary>
    /// Catalog pact contract test fixture.
    /// </summary>
    public class CatalogContractTestFixture : PactContractTestFixture
    {
        public CatalogContractTestFixture()
            : base(
                consumerName: Environment.GetEnvironmentVariable("contractConsumerName") ?? throw new ArgumentException("The 'contractConsumerName' environment variable is not set."),
                providerName: PactConstants.CatalogPackageName,
                providerRoute: ApiContractRoutes.Operations,
                consumerRoute: ApiContractRoutes.Operations,
                providerVersion: "v2",
                isPublicPact: true)
        {
        }
    }
}