﻿using Aveva.Platform.Authorization.Sdk;
using Aveva.Platform.Authorization.Sdk.Domain.Models;
using Aveva.Platform.Authorization.Sdk.Domain.Models.Target;
using Aveva.Platform.Catalog.Api.Routing;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Domain.Queries;
using Aveva.Platform.Catalog.ServiceClient.AccountMgmt;
using Aveva.Platform.Common.Framework.Abstractions.Mapping;
using Aveva.Platform.Common.Framework.Abstractions.Results;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using ApiResponse = Aveva.Platform.Catalog.Domain.DataTransferObjects.Api.v2;
using OpsResponse = Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;

namespace Aveva.Platform.Catalog.Api.Tests.Unit.Helpers;

/// <summary>
/// Extension methods for <see cref="Mock{T}"/> mock objects used by test cases.
/// </summary>
internal static class MockExtensions
{
    #region Mock Object Set Up

    /// <summary>
    /// Set ups for GetAll tests for V2.
    /// </summary>
    /// <param name="mockFactory">The mock factory to use to create mock object instances.</param>
    /// <param name="expectedEntities">List of expected entities.</param>
    /// <returns>Tuple containing set up mock object instances for repositories and type mappers.</returns>
    internal static CommonMocks SetupGetAllV2Ops(this MockRepository mockFactory, List<V1ServiceEntry>? expectedEntities = null)
    {
        expectedEntities = expectedEntities ?? TestDataV2.CreateServiceEntries().ToList();
        var finalEntities = expectedEntities.Where(e => e.Availability == null || e.Availability.Enabled == true).ToList();
        IEnumerable<OpsResponse.ServiceResponse> expectedDtos = TestDataV2.CreateServiceEntryTestDtosOps(finalEntities);

        Mock<IServiceEntryRepository> repositoryMock = mockFactory.Create<IServiceEntryRepository>();
        repositoryMock
            .Setup(m => m.QueryAsync(It.IsAny<ServiceEntryQuery>()).Result)
            .Returns((List<V1ServiceEntry>)expectedEntities)
            .Verifiable();

        Mock<ITypeMappingService> dtoTypeMapperMock = mockFactory.Create<ITypeMappingService>();
        dtoTypeMapperMock.Setup(m => m.Map<OpsResponse.ServiceResponse, V1ServiceEntry>(finalEntities)).Returns(expectedDtos.ToArray);

        Mock<HttpContext> contextMock = mockFactory.Create<HttpContext>();

        Mock<IAuthorizationProvider> authorizationMock = new Mock<IAuthorizationProvider>();
        Result successResult = Result.Success();
        authorizationMock
            .Setup(m => m.AuthorizeResourceAsync(It.IsAny<ResourcePath>(), It.IsAny<ResourceAction>(), It.IsAny<AuthorizationTarget>(), It.IsAny<AuthorizationMetadata>(), It.IsAny<CancellationToken>()))
            .Returns((ResourcePath resourcePath, ResourceAction resourceAction, AuthorizationTarget target, AuthorizationMetadata metadata, CancellationToken token) => ValueTask.FromResult(successResult))
            .Verifiable();

        Mock<ILogger<CatalogV2RouteRegistrationModule>> loggerMock = new Mock<ILogger<CatalogV2RouteRegistrationModule>>();

        Mock<CatalogMetrics> metricsMock = new Mock<CatalogMetrics>();
        Mock<IAccountMgmtClient> accountMgmtMock = new Mock<IAccountMgmtClient>();
        accountMgmtMock
            .Setup(m => m.QueryAvailabilityAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, V1ServiceAvailability>())
            .Verifiable();

        Mock<IServiceAvailabilityRepository> mockServiceAvailabilityRepository = new Mock<IServiceAvailabilityRepository>();
        mockServiceAvailabilityRepository
            .Setup(x => x.GetForEntries(expectedEntities, It.IsAny<Dictionary<string, V1ServiceAvailability>>()))
            .Returns(expectedEntities is null ? []
                    : expectedEntities.ToDictionary(
                        service => service,
                        service => service.Availability))
            .Verifiable();

        return new CommonMocks(repositoryMock, dtoTypeMapperMock, contextMock, authorizationMock, accountMgmtMock, loggerMock, metricsMock, mockServiceAvailabilityRepository);
    }

    /// <summary>
    /// Set ups for GetById tests.
    /// </summary>
    /// <param name="mockFactory">The mock factory to use to create mock object instances.</param>
    /// <param name="identifier">The identifier to be used in calls.</param>
    /// <param name="expectedAvailability">The expected availability.</param>
    /// <returns>Object containing set up mock object instances.</returns>
    internal static CommonMocks SetupGetByIdV2Ops(this MockRepository mockFactory, string identifier, OpsResponse.ServiceAvailability? expectedAvailability = null)
    {
        var expectedV1Availability = new V1ServiceAvailability
        {
            Enabled = expectedAvailability?.Enabled,
            Limit = expectedAvailability?.Limit,
        };

        V1ServiceEntry expectedEntity = TestDataV2.CreateServiceEntries(1, expectedV1Availability).First();
        OpsResponse.ServiceResponse expectedDto = TestDataV2.CreateServiceEntryTestDtoOps(expectedEntity);

        Mock<IServiceEntryRepository> repositoryMock = mockFactory.Create<IServiceEntryRepository>();
        Mock<ITypeMappingService> dtoTypeMapperMock = mockFactory.Create<ITypeMappingService>();
        if (string.Equals(identifier, expectedEntity.Id, StringComparison.InvariantCulture))
        {
            repositoryMock
                .Setup(m => m.GetByIdAsync(identifier))
                .Returns(Task.FromResult(expectedEntity)!)
                .Verifiable();
            dtoTypeMapperMock.Setup(m => m.Map<OpsResponse.ServiceResponse, V1ServiceEntry>(expectedEntity)).Returns(expectedDto);
        }
        else
        {
            repositoryMock
                .Setup(m => m.GetByIdAsync(It.IsAny<string>()))
                .Returns(Task.FromResult((V1ServiceEntry?)null))
                .Verifiable();
        }

        Mock<HttpContext> contextMock = mockFactory.Create<HttpContext>();

        Mock<IAuthorizationProvider> authorizationMock = new Mock<IAuthorizationProvider>();
        Result successResult = Result.Success();
        authorizationMock
            .Setup(m => m.AuthorizeResourceAsync(It.IsAny<ResourcePath>(), It.IsAny<ResourceAction>(), It.IsAny<AuthorizationTarget>(), It.IsAny<AuthorizationMetadata>(), It.IsAny<CancellationToken>()))
            .Returns((ResourcePath resourcePath, ResourceAction resourceAction, AuthorizationTarget target, AuthorizationMetadata metadata, CancellationToken token) => ValueTask.FromResult(successResult))
            .Verifiable();

        Mock<ILogger<CatalogV2RouteRegistrationModule>> loggerMock = new Mock<ILogger<CatalogV2RouteRegistrationModule>>();

        Mock<CatalogMetrics> metricsMock = new Mock<CatalogMetrics>();
        Mock<IAccountMgmtClient> accountMgmtMock = new Mock<IAccountMgmtClient>();
        accountMgmtMock
            .Setup(m => m.QueryAvailabilityAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, V1ServiceAvailability>())
            .Verifiable();

        Mock<IServiceAvailabilityRepository> mockServiceAvailabilityRepository = new Mock<IServiceAvailabilityRepository>();
        mockServiceAvailabilityRepository
            .Setup(x => x.GetForEntry(expectedEntity, It.IsAny<Dictionary<string, V1ServiceAvailability>>()))
            .Returns(expectedV1Availability);

        return new CommonMocks(repositoryMock, dtoTypeMapperMock, contextMock, authorizationMock, accountMgmtMock, loggerMock, metricsMock, mockServiceAvailabilityRepository);
    }

    /// <summary>
    /// Set ups for GetAll tests for V2.
    /// </summary>
    /// <param name="mockFactory">The mock factory to use to create mock object instances.</param>
    /// <param name="expectedEntities">List of expected entities.</param>
    /// <returns>Tuple containing set up mock object instances for repositories and type mappers.</returns>
    internal static CommonMocks SetupGetAllV2Api(this MockRepository mockFactory, List<V1ServiceEntry>? expectedEntities = null)
    {
        expectedEntities = expectedEntities ?? TestDataV2.CreateServiceEntries().ToList();
        var finalEntities = expectedEntities.Where(e => e.Availability == null || e.Availability.Enabled == true).ToList();
        IEnumerable<ApiResponse.ServiceResponse> expectedDtos = TestDataV2.CreateServiceEntryTestDtosApi(finalEntities);

        Mock<IServiceEntryRepository> repositoryMock = mockFactory.Create<IServiceEntryRepository>();
        repositoryMock
            .Setup(m => m.QueryAsync(It.IsAny<ServiceEntryQuery>()).Result)
            .Returns((List<V1ServiceEntry>)expectedEntities)
            .Verifiable();

        Mock<ITypeMappingService> dtoTypeMapperMock = mockFactory.Create<ITypeMappingService>();
        dtoTypeMapperMock.Setup(m => m.Map<ApiResponse.ServiceResponse, V1ServiceEntry>(finalEntities)).Returns(expectedDtos.ToArray);

        Mock<HttpContext> contextMock = mockFactory.Create<HttpContext>();

        Mock<IAuthorizationProvider> authorizationMock = new Mock<IAuthorizationProvider>();
        Result successResult = Result.Success();
        authorizationMock
            .Setup(m => m.AuthorizeResourceAsync(It.IsAny<ResourcePath>(), It.IsAny<ResourceAction>(), It.IsAny<AuthorizationTarget>(), It.IsAny<AuthorizationMetadata>(), It.IsAny<CancellationToken>()))
            .Returns((ResourcePath resourcePath, ResourceAction resourceAction, AuthorizationTarget target, AuthorizationMetadata metadata, CancellationToken token) => ValueTask.FromResult(successResult))
            .Verifiable();

        Mock<ILogger<CatalogV2RouteRegistrationModule>> loggerMock = new Mock<ILogger<CatalogV2RouteRegistrationModule>>();

        Mock<CatalogMetrics> metricsMock = new Mock<CatalogMetrics>();
        Mock<IAccountMgmtClient> accountMgmtMock = new Mock<IAccountMgmtClient>();
        accountMgmtMock
            .Setup(m => m.QueryAvailabilityAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, V1ServiceAvailability>())
            .Verifiable();

        Mock<IServiceAvailabilityRepository> mockServiceAvailabilityRepository = new Mock<IServiceAvailabilityRepository>();
        mockServiceAvailabilityRepository
            .Setup(x => x.GetForEntries(expectedEntities, It.IsAny<Dictionary<string, V1ServiceAvailability>>()))
            .Returns(expectedEntities is null ? []
                        : expectedEntities.ToDictionary(
                            service => service,
                            service => service.Availability))
            .Verifiable();

        return new CommonMocks(repositoryMock, dtoTypeMapperMock, contextMock, authorizationMock, accountMgmtMock, loggerMock, metricsMock, mockServiceAvailabilityRepository);
    }

    /// <summary>
    /// Set ups for GetById tests.
    /// </summary>
    /// <param name="mockFactory">The mock factory to use to create mock object instances.</param>
    /// <param name="identifier">The identifier to be used in calls.</param>
    /// <param name="expectedV1Availability">The expected availability.</param>
    /// <returns>Object containing set up mock object instances.</returns>
    internal static CommonMocks SetupGetByIdV2Api(this MockRepository mockFactory, string identifier, V1ServiceAvailability? expectedV1Availability = null)
    {
        V1ServiceEntry expectedEntity = TestDataV2.CreateServiceEntries(1, expectedV1Availability).First();
        ApiResponse.ServiceResponse expectedDto = TestDataV2.CreateServiceEntryTestDtoApi(expectedEntity);

        Mock<IServiceEntryRepository> repositoryMock = mockFactory.Create<IServiceEntryRepository>();
        Mock<ITypeMappingService> dtoTypeMapperMock = mockFactory.Create<ITypeMappingService>();
        if (string.Equals(identifier, expectedEntity.Id, StringComparison.InvariantCulture))
        {
            repositoryMock
                .Setup(m => m.GetByIdAsync(identifier))
                .Returns(Task.FromResult(expectedEntity)!)
                .Verifiable();
            dtoTypeMapperMock.Setup(m => m.Map<ApiResponse.ServiceResponse, V1ServiceEntry>(expectedEntity)).Returns(expectedDto);
        }
        else
        {
            repositoryMock
                .Setup(m => m.GetByIdAsync(It.IsAny<string>()))
                .Returns(Task.FromResult((V1ServiceEntry?)null))
                .Verifiable();
        }

        Mock<HttpContext> contextMock = mockFactory.Create<HttpContext>();

        Mock<IAuthorizationProvider> authorizationMock = new Mock<IAuthorizationProvider>();
        Result successResult = Result.Success();
        authorizationMock
            .Setup(m => m.AuthorizeResourceAsync(It.IsAny<ResourcePath>(), It.IsAny<ResourceAction>(), It.IsAny<AuthorizationTarget>(), It.IsAny<AuthorizationMetadata>(), It.IsAny<CancellationToken>()))
            .Returns((ResourcePath resourcePath, ResourceAction resourceAction, AuthorizationTarget target, AuthorizationMetadata metadata, CancellationToken token) => ValueTask.FromResult(successResult))
            .Verifiable();

        Mock<ILogger<CatalogV2RouteRegistrationModule>> loggerMock = new Mock<ILogger<CatalogV2RouteRegistrationModule>>();

        Mock<CatalogMetrics> metricsMock = new Mock<CatalogMetrics>();
        Mock<IAccountMgmtClient> accountMgmtMock = new Mock<IAccountMgmtClient>();
        accountMgmtMock
            .Setup(m => m.QueryAvailabilityAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, V1ServiceAvailability>())
            .Verifiable();

        Mock<IServiceAvailabilityRepository> mockServiceAvailabilityRepository = new Mock<IServiceAvailabilityRepository>();
        mockServiceAvailabilityRepository
            .Setup(x => x.GetForEntry(expectedEntity, It.IsAny<Dictionary<string, V1ServiceAvailability>>()))
            .Returns(expectedV1Availability);

        return new CommonMocks(repositoryMock, dtoTypeMapperMock, contextMock, authorizationMock, accountMgmtMock, loggerMock, metricsMock, mockServiceAvailabilityRepository);
    }

    #endregion Mock Object Set Up
}