{"name": "@platform/aveva.platform.catalog.tsclient", "version": "1.0.0", "type": "module", "files": ["V2/api/**/*", "V2/ops/**/*", "index.*"], "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@microsoft/kiota-bundle": "^1.0.0-preview.92", "@types/node": "^20.12.12", "typescript": "^5.4.5", "rimraf": "^5.0.5", "tsc-alias": "^1.8.10"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "transpile": "tsc", "aliasTranspile": "tsc-alias", "build": "npm run transpile", "start": "npm run clean && npm run build && npm run aliasTranspile"}}