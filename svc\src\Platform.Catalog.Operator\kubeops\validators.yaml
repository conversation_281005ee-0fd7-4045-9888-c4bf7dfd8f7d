apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validators
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tDQpNSUlCbFRDQ0FUdWdBd0lCQWdJSVVvTkcrRWd3OFlzd0NnWUlLb1pJemowRUF3UXdQakVaTUJjR0ExVUVBd3dRDQpUM0JsY21GMGIzSWdVbTl2ZENCRFFURU1NQW9HQTFVRUJoTURSRVZXTVJNd0VRWURWUVFIREFwTGRXSmxjbTVsDQpkR1Z6TUI0WERUSTFNRGN3TXpBd01EQXdNRm9YRFRNd01EY3dNekF3TURBd01Gb3dQakVaTUJjR0ExVUVBd3dRDQpUM0JsY21GMGIzSWdVbTl2ZENCRFFURU1NQW9HQTFVRUJoTURSRVZXTVJNd0VRWURWUVFIREFwTGRXSmxjbTVsDQpkR1Z6TUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFd3EyeGFmOEVFdytKdjJjV0RTYlBoVGVRDQo5NkVqSmlGUnNuRkc4K2laSTQwNzdrQXhSU2Q2Q1JnZFkzYUxtWkpPbzhXRTFSYm5Fdi9OTFY0YXYwSkJJNk1qDQpNQ0V3RHdZRFZSMFRBUUgvQkFVd0F3RUIvekFPQmdOVkhROEJBZjhFQkFNQ0FTWXdDZ1lJS29aSXpqMEVBd1FEDQpTQUF3UlFJZ045WDFiY1dlTkl4ZUNsdGgzazVjajFkYTFLcXdxSjRWUmd2UnNJQnY1bVVDSVFEL254Q3hLVnA5DQpRMjQ1eWtJNUlaSVoraXJTcmhuUDdiWUk2aHB5eTFlaUR3PT0NCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0NCg==
    service:
      name: operator
      path: /validate/v1serviceentry
  matchPolicy: Exact
  name: validate.serviceentry.servicecatalog.aveva.com.v1
  rules:
  - apiGroups:
    - servicecatalog.aveva.com
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    - DELETE
    resources:
    - serviceentries
  sideEffects: None