﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.Client.ContractTests</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.Client.ContractTests</RootNamespace>
    <IsPackable>true</IsPackable>
  </PropertyGroup>
  <PropertyGroup>
    <Trademark>AVEVA</Trademark>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>
  <PropertyGroup>
    <TargetsForTfmSpecificContentInPackage>$(TargetsForTfmSpecificContentInPackage);PackRuntimeFiles</TargetsForTfmSpecificContentInPackage>
  </PropertyGroup>
  <Target Name="PackRuntimeFiles">
    <ItemGroup>
      <TfmSpecificPackageFile Include="$(OutputPath)/**/*.*">
        <PackagePath>lib/$(TargetFramework)</PackagePath>
      </TfmSpecificPackageFile>
    </ItemGroup>
  </Target>
  <ItemGroup>
    <PackageReference Include="Aveva.Platform.Common.Testing.ApiContracts" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Microsoft.Kiota.Abstractions" />
    <PackageReference Include="PactNet" />
    <PackageReference Include="PactNet.Abstractions" />
    <PackageReference Include="PactNet.Output.Xunit" />
    <PackageReference Include="xunit" />
    <PackageReference Include="System.Text.Json"/>
    <PackageReference Include="xunit.runner.visualstudio">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\Aveva.Platform.Catalog.Client\Platform.Library.Catalog.Client.csproj" />
  </ItemGroup>
</Project>
