﻿using System.Text;
using System.Text.Json;
using Aveva.Platform.AccountMgmt.Client.Ops;
using Aveva.Platform.AccountMgmt.Client.Ops.Models;
using Aveva.Platform.AccountMgmt.Client.Ops.Ops.AccountMgmt.V1.Accounts;
using Aveva.Platform.Catalog.Tests.PerformanceSetup.Models;
using Microsoft.Extensions.Configuration;
namespace Aveva.Platform.Catalog.Tests.PerformanceSetup.Utilities;

public static class PerformanceSetupUtilities
{
    /// <summary>
    /// This method is used to generate the token.
    /// </summary>
    /// <param name="tokenEndpoint">tokenEndpoint.</param>
    /// <param name="clientId">clientId.</param>
    /// <param name="clientSecret">clientSecret.</param>
    /// <returns>bearer_access_token.</returns>
    public static async Task<string> GenerateBearerToken(string tokenEndpoint, string clientId, string clientSecret)
    {
        string bearer_access_token = string.Empty;
        try
        {
            var uri = new Uri(tokenEndpoint);
            using var client = new HttpClient();
            var requestBody = new Dictionary<string, string>
                {
                    { Constants.GRANTTYPE, Constants.CLIENTCREDENTIALS },
                    { Constants.CLIENTIDPARAM, clientId! },
                    { Constants.CLIENTSECRETPARAM, clientSecret! },
                    { Constants.SCOPE, Constants.CONNECTOPS },
                };

            using HttpContent formContent = new FormUrlEncodedContent(requestBody);
            HttpResponseMessage response = await client.PostAsync(uri, formContent).ConfigureAwait(true);

            response.EnsureSuccessStatusCode();
            string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            var respObj = JsonSerializer.Deserialize<TokenResponse>(content);

            bearer_access_token = Constants.BEARER + respObj?.access_token;
        }
        catch (ArgumentException ex)
        {
            GetExceptionMessage(ex);
            throw;
        }
        catch (FormatException ex)
        {
            GetExceptionMessage(ex);
            throw;
        }
        catch (Exception ex)
        {
            GetExceptionMessage(ex);
            throw;
        }

        return bearer_access_token;
    }

    /// <summary>
    /// To get the account details.
    /// </summary>
    /// <param name="client">client.</param>
    /// <param name="getQueryParam">getQueryParam.</param>
    /// <returns>accountDetail.</returns>
    public static async Task<AccountDetail> GetAccountDetails(Client client, object getQueryParam)
    {
        AccountDetail? accountDetail = null;
        try
        {
            if (client != null)
            {
                var accountCollectionResponse = await client.Ops.AccountMgmt.V1.Accounts.GetAsync(config =>
                {
                    config.QueryParameters = (AccountsRequestBuilder.AccountsRequestBuilderGetQueryParameters)getQueryParam!;
                }).ConfigureAwait(false);

                if (accountCollectionResponse?.Items?.Count > 0)
                {
                    accountDetail = new AccountDetail
                    {
                        AccountGUID = accountCollectionResponse?.Items?[0]?.Id,
                        AccountName = accountCollectionResponse?.Items?[0]?.Name,
                        Status = accountCollectionResponse?.Items?[0]?.Status?.ToString(),
                        CreatedDate = accountCollectionResponse?.Items?[0]?.CreatedDate?.ToString(),
                        ModifiedDate = string.IsNullOrEmpty(accountCollectionResponse?.Items?[0]?.ModifiedDate.ToString())
                                           ? Constants.NOTAVAILABLE
                                           : accountCollectionResponse?.Items?[0]?.ModifiedDate.ToString(),
                    };
                }
            }
        }
        catch (NullReferenceException ex)
        {
            GetExceptionMessage(ex);
            throw;
        }
        catch (Exception ex)
        {
            GetExceptionMessage(ex);
            throw;
        }

        return accountDetail!;
    }

    /// <summary>
    /// To retrive the environment details.
    /// </summary>
    /// <returns>EnvironmentDetails.</returns>
    public static async Task<EnvironmentDetails> GetEnvironmentDetails()
    {
        try
        {
            var configBuilder = new ConfigurationBuilder()
                        .SetBasePath(Directory.GetCurrentDirectory())
                        .AddJsonFile(Constants.APPSETTINGSFILE, optional: false, reloadOnChange: true);

            IConfiguration config = configBuilder.Build();

            bool isExist = config.GetSection(Constants.ENVIRONMENTSETTINGS).Exists();
            var environmentDetails = new EnvironmentDetails();
            if (isExist)
            {
                environmentDetails.Dns = config.GetValue<string>(Constants.DNS)!;
                environmentDetails.EnvPrefix = config.GetValue<string>(Constants.ENVPREFIX)!;
                environmentDetails.TenantId = config.GetValue<string>(Constants.TENANTID)!;
                environmentDetails.ClientId = config.GetValue<string>(Constants.CLIENTID)!;
                environmentDetails.ClientSecret = config.GetValue<string>(Constants.CLIENTSECRET)!;
                environmentDetails.Accounts = config.GetSection(Constants.ACCOUNTSNAME)?.Get<List<AccountData>>()?.Where(val => !string.IsNullOrEmpty(val.Name)).DistinctBy(account => account.Name).ToList();
                string? clientId = environmentDetails.ClientId;
                string? clientSecret = environmentDetails.ClientSecret;
                string tokenEndpoint = Constants.URI + environmentDetails.TenantId + Constants.AUTHVIRSION;
                environmentDetails.CurrentToken = await GenerateBearerToken(tokenEndpoint, clientId!, clientSecret!).ConfigureAwait(false);
                environmentDetails.BaseUrllDetails = Constants.HTTPPROTOCAL + environmentDetails?.EnvPrefix + Constants.DOT + environmentDetails?.Dns;
            }

            return environmentDetails!;
        }
        catch (FileNotFoundException ex)
        {
            GetExceptionMessage(ex);
            throw;
        }
        catch (Exception ex)
        {
            GetExceptionMessage(ex);
            throw;
        }
    }

    /// <summary>
    /// To create file and append contents.
    /// </summary>
    /// <param name="accountDetails">accountDetails.</param>
    public static void CreateFile(List<ActiveAccountDetails>? accountDetails)
    {
        try
        {
            if (accountDetails != null)
            {
                var csv = new StringBuilder();
                var newLine = string.Format(Constants.FORMAT, Constants.ACCOUNTNAME, Constants.ACCOUNTGUID);
                foreach (var item in accountDetails!)
                {
                    newLine += string.Format(Constants.FORMAT, item.AccountName, item.AccountGUID);
                    Console.Write(JsonSerializer.Serialize(newLine));
                }

                csv?.AppendLine(newLine);
                File.WriteAllText(Constants.OUTPUTPATH, csv?.ToString());
            }
        }
        catch (ArgumentNullException ex)
        {
            GetExceptionMessage(ex);
            throw;
        }
        catch (Exception ex)
        {
            GetExceptionMessage(ex);
            throw;
        }
    }

    /// <summary>
    /// To print message.
    /// </summary>
    /// <param name="message">message.</param>
    public static void Print(string? message)
    {
        Console.WriteLine(message);
    }

    /// <summary>
    /// Do print the exception details.
    /// </summary>
    /// <param name="ex">ex.</param>
    public static void GetExceptionMessage(Exception ex)
    {
        if (ex != null)
        {
            var status = ex?.GetType()?.GetProperty("Status")?.GetValue(ex) ?? ex?.GetType()?.GetProperty("ResponseStatusCode")?.GetValue(ex);
            var detail = ex?.GetType()?.GetProperty("Detail")?.GetValue(ex) ?? "Reason for this error not available.";

            string message = $"Time: [{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}] \n \n" +
                   $"ErrorCode: {status ?? "Status code is not available."} \n \n" +
                   $"Reason for error: {detail} \n \n" +
                   $"Message: {ex?.GetType()?.GetProperty("Message")?.GetValue(ex)} \n \n" +
                   $"StackTrace: {ex?.GetType()?.GetProperty("StackTrace")?.GetValue(ex)}";

            Print(message);
        }
    }

    /// <summary>
    /// To Generate GenerateQueryParam.
    /// </summary>
    /// <param name="accountData">account.</param>
    /// <param name="type">type.</param>
    /// <returns>queryParameter.</returns>
    public static object GetQueryParam(AccountData accountData, string? type)
    {
        object? queryParameter = null;
        try
        {
            switch (type)
            {
                case Constants.GET:
                    queryParameter = new AccountsRequestBuilder.AccountsRequestBuilderGetQueryParameters()
                    {
                        Name = accountData?.Name,
                    };
                    break;

                case Constants.DELETE:
                    queryParameter = new AccountsRequestBuilder.AccountsRequestBuilderDeleteQueryParameters()
                    {
                        Name = accountData?.Name,
                    };
                    break;
                case Constants.POST:
                    queryParameter = new AccountCreateRequest()
                    {
                        AliasId = accountData?.Name,
                        Name = accountData?.Name,
                        Geography = accountData?.Geography?.ToLower(System.Globalization.CultureInfo.CurrentCulture),
                        Type = AccountType.InternalTest,
                    };
                    break;
            }
        }
        catch (Exception ex)
        {
            GetExceptionMessage(ex);
            throw;
        }

        return queryParameter!;
    }

    public static async Task<AccountDetail> GetResponse(string? type, AccountDetail accountDetail, Client client, AccountData account)
    {
        DateTime timeoutTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(120));
        try
        {
            if (client != null)
            {
                    switch (type)
                    {
                        case Constants.POST:
                        while (!(DateTime.UtcNow >= timeoutTime))
                        {
                            var result = await client.Ops.AccountMgmt.V1.Accounts.PostAsync((AccountCreateRequest)PerformanceSetupUtilities.GetQueryParam(account!, Constants.POST)).ConfigureAwait(false);
                            await Task.Delay(TimeSpan.FromSeconds(2)).ConfigureAwait(false);
                            accountDetail = await PerformanceSetupUtilities.GetAccountDetails(client, PerformanceSetupUtilities
                                .GetQueryParam(account!, Constants.GET)).ConfigureAwait(false);

                            if (result != null && accountDetail != null)
                            {
                                while (accountDetail?.Status != Constants.ACTIVE
                                    && accountDetail?.Status != Constants.CREATEFAILED)
                                {
                                    if (DateTime.UtcNow >= timeoutTime)
                                    {
                                        throw new Exception(Constants.OPERATIONTIMEDOUT);
                                    }

                                    PerformanceSetupUtilities.Print(string.Format(Constants.CREATEINPROGRESS, accountDetail?.AccountName, accountDetail?.Status));
                                    await Task.Delay(TimeSpan.FromSeconds(2)).ConfigureAwait(false);
                                    accountDetail = await PerformanceSetupUtilities.GetAccountDetails(client, PerformanceSetupUtilities.GetQueryParam(account, Constants.GET)).ConfigureAwait(false);
                                }
                            }

                            if (accountDetail != null)
                            {
                                break;
                            }
                            else if (DateTime.UtcNow >= timeoutTime)
                            {
                                throw new Exception(Constants.OPERATIONTIMEDOUT);
                            }
                        }

                        break;

                        case Constants.DELETE:
                        while (!(DateTime.UtcNow >= timeoutTime))
                        {
                            await client.Ops.AccountMgmt.V1.Accounts.DeleteAsync(config =>
                            {
                                config.QueryParameters = (AccountsRequestBuilder.AccountsRequestBuilderDeleteQueryParameters)PerformanceSetupUtilities.GetQueryParam(account!, Constants.DELETE);
                            }).ConfigureAwait(false);

                            accountDetail = await PerformanceSetupUtilities.GetAccountDetails(client, PerformanceSetupUtilities
                                .GetQueryParam(account!, Constants.GET)).ConfigureAwait(false);

                            if (accountDetail != null && !string.IsNullOrEmpty(accountDetail?.Status))
                            {
                                while (accountDetail?.Status != Constants.DELETED
                                    && accountDetail?.Status != Constants.PURGED)
                                {
                                    if (accountDetail?.Status == null)
                                    {
                                        // PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTDELETEDSUCCESSFULLY, account?.Name));
                                        break;
                                    }
                                    else if (DateTime.UtcNow >= timeoutTime)
                                    {
                                        throw new Exception(Constants.OPERATIONTIMEDOUT);
                                    }

                                    PerformanceSetupUtilities.Print(string.Format(Constants.DELETEPROGRESS, accountDetail?.AccountName, accountDetail?.Status));
                                    await Task.Delay(TimeSpan.FromSeconds(2)).ConfigureAwait(false);
                                    accountDetail = await PerformanceSetupUtilities.GetAccountDetails(client, PerformanceSetupUtilities.GetQueryParam(account!, Constants.GET)).ConfigureAwait(false);
                                }
                            }

                            if (accountDetail?.Status == null || string.Equals(accountDetail?.Status, Constants.DELETED, StringComparison.OrdinalIgnoreCase) || string.Equals(accountDetail?.Status, Constants.PURGED, StringComparison.OrdinalIgnoreCase))
                            {
                                PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTDELETEDSUCCESSFULLY, account?.Name));
                                break;
                            }
                            else if (DateTime.UtcNow >= timeoutTime)
                            {
                                throw new Exception(Constants.OPERATIONTIMEDOUT);
                            }
                        }

                        break;
                    }
            }
        }
        catch (Exception ex)
        {
            GetExceptionMessage(ex);
            throw;
        }

        return accountDetail!;
    }
}