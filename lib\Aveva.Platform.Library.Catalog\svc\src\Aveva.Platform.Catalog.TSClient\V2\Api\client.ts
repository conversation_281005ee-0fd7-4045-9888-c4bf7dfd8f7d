/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { ApiRequestBuilderNavigationMetadata, type ApiRequestBuilder } from './api/index.js';
// @ts-ignore
import { apiClientProxifier, ParseNodeFactoryRegistry, SerializationWriterFactoryRegistry, type BaseRequestBuilder, type KeysToExcludeForNavigationMetadata, type NavigationMetadata, type RequestAdapter } from '@microsoft/kiota-abstractions';
// @ts-ignore
import { FormParseNodeFactory, FormSerializationWriterFactory } from '@microsoft/kiota-serialization-form';
// @ts-ignore
import { JsonParseNodeFactory, JsonSerializationWriterFactory } from '@microsoft/kiota-serialization-json';
// @ts-ignore
import { MultipartSerializationWriterFactory } from '@microsoft/kiota-serialization-multipart';
// @ts-ignore
import { TextParseNodeFactory, TextSerializationWriterFactory } from '@microsoft/kiota-serialization-text';

/**
 * The main entry point of the SDK, exposes the configuration and the fluent API.
 */
export interface Client extends BaseRequestBuilder<Client> {
    /**
     * The api property
     */
    get api(): ApiRequestBuilder;
}
/**
 * Instantiates a new {@link Client} and sets the default values.
 * @param requestAdapter The request adapter to use to execute the requests.
 */
// @ts-ignore
export function createClient(requestAdapter: RequestAdapter) {
    if (requestAdapter === undefined) {
        throw new Error("requestAdapter cannot be undefined");
    }
    let serializationWriterFactory : SerializationWriterFactoryRegistry
    let parseNodeFactoryRegistry : ParseNodeFactoryRegistry
    
    if (requestAdapter.getParseNodeFactory() instanceof ParseNodeFactoryRegistry) {
        parseNodeFactoryRegistry = requestAdapter.getParseNodeFactory() as ParseNodeFactoryRegistry
    } else {
        throw new Error("requestAdapter.getParseNodeFactory() is not a ParseNodeFactoryRegistry")
    }
    
    if (requestAdapter.getSerializationWriterFactory() instanceof SerializationWriterFactoryRegistry) {
        serializationWriterFactory = requestAdapter.getSerializationWriterFactory() as SerializationWriterFactoryRegistry
    } else {
        throw new Error("requestAdapter.getSerializationWriterFactory() is not a SerializationWriterFactoryRegistry")
    }
    
    serializationWriterFactory.registerDefaultSerializer(JsonSerializationWriterFactory);
    serializationWriterFactory.registerDefaultSerializer(TextSerializationWriterFactory);
    serializationWriterFactory.registerDefaultSerializer(FormSerializationWriterFactory);
    serializationWriterFactory.registerDefaultSerializer(MultipartSerializationWriterFactory);
    
    const backingStoreFactory = requestAdapter.getBackingStoreFactory();
    parseNodeFactoryRegistry.registerDefaultDeserializer(JsonParseNodeFactory, backingStoreFactory);
    parseNodeFactoryRegistry.registerDefaultDeserializer(TextParseNodeFactory, backingStoreFactory);
    parseNodeFactoryRegistry.registerDefaultDeserializer(FormParseNodeFactory, backingStoreFactory);
    const pathParameters: Record<string, unknown> = {
        "baseurl": requestAdapter.baseUrl,
    };
    return apiClientProxifier<Client>(requestAdapter, pathParameters, ClientNavigationMetadata, undefined);
}
/**
 * Uri template for the request builder.
 */
export const ClientUriTemplate = "{+baseurl}";
/**
 * Metadata for all the navigation properties in the request builder.
 */
export const ClientNavigationMetadata: Record<Exclude<keyof Client, KeysToExcludeForNavigationMetadata>, NavigationMetadata> = {
    api: {
        navigationMetadata: ApiRequestBuilderNavigationMetadata,
    },
};
/* tslint:enable */
/* eslint-enable */
