// <auto-generated/>
#pragma warning disable CS0618
using Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog
{
    /// <summary>
    /// Builds and executes requests for operations under \api\account\{accountId}\catalog
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class CatalogRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The v2 property</summary>
        public global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.V2RequestBuilder V2
        {
            get => new global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.V2.V2RequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.CatalogRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public CatalogRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/account/{accountId}/catalog", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.Catalog.CatalogRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public CatalogRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/account/{accountId}/catalog", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
