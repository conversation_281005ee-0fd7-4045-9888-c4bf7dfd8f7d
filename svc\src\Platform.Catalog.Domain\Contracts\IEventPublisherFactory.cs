﻿namespace Aveva.Platform.Catalog.Domain.Contracts
{
    /// <summary>
    /// Interface for creating new instance of Catalog Event publishers.
    /// </summary>
    public interface IEventPublisherFactory
    {
        /// <summary>
        /// Publish Catalog service generated events.
        /// </summary>
        /// <returns>List of catalog entires.</returns>
        public Task<IEventPublisher?> GetEventPublisherAsync(string eventType);
    }
}