import { RootRequest, HttpVerb } from "@platform/performance-libs";
import { Api as ApiAccessMgmt } from "@platform/aveva.platform.accessmgmt.tsclient";
import { Ops as OpsIdentity } from "@platform/aveva.platform.identity.tsclient";
import { checkApiResponse } from "@platform/performance-libs";

export function getAssignmentId(data: any): void {
    // ******** CHECK IF ROLE ASSIGNMENT CREATED AS PART OF PRESETUP IS AVAILABLE FROM GET CALL ********
    data.apiClient.config.apiClientId = data.clientCredentialClientId;
    data.apiClient.config.apiClientSecret = data.clientCredentialClientSecret;

    const request = new RootRequest(HttpVerb.GET, `/account/${data.accountGuid}/accessMgmt/v1/roles/${data.accountAdminRoleGuid}/assignments/${data.adminRoleAssignmentId}`);
    const response = data.apiClient.request(request);

    checkApiResponse(response, 200, request, "Get Role Assignment by Id for given Account");

    if (response.status !== 200) {
        data.logger.error(`Get Role Assignment by Id for given Account error: '${response.body}' (${response.status})`);
    }

    // Parse the response body as JSON and type it
    let roleAssignResponseBody: ApiAccessMgmt.RoleAssignmentResponse = JSON.parse(response.body);

    if (roleAssignResponseBody.id == data.adminRoleAssignmentId) {
        data.logger.info(`Successfully found the admin role assignment id - ${roleAssignResponseBody.id} in returned response`);
    } else {
        data.logger.error("Failed to find the admin role assignment id in returned response");
    }
}

export function assignIdpToAccount(data: any): void {
    // ******** CHECK IF MSA IDENTITY PROVIDER IS ALREADY ADDED ********        
    data.logger.info("Checking and adding MSA IDP to account " + data.accountGuid);

    const request = new RootRequest(HttpVerb.GET, `/account/${data.accountGuid}/identity/v1/IdentityProviders`);
    const response = data.opsClient.request(request);

    checkApiResponse(response, 200, request, "Get IDP for account");

    // Parse the response body as JSON and type it        
    let providersResponse: OpsIdentity.IdentityProviderCollectionResponse = JSON.parse(response.body);

    let index = providersResponse.items.findIndex(item => item.name == "Microsoft Account");

    if (index == -1) {
        const addIdpBody: OpsIdentity.IdentityProviderCreateRequest = {
            name: "Microsoft Account",
            type: "a707f5d7-23cc-4e70-817a-a6a3cbcf4a26",
            emailValidationRequired: false
        };

        const request = new RootRequest(HttpVerb.POST, `/account/${data.accountGuid}/identity/v1/IdentityProviders`, addIdpBody);
        const response = data.opsClient.request(request);

        checkApiResponse(response, 201, request, "Add IDP to existing account");

        // Parse the response body as JSON and type it            
        let addIdpResponse: OpsIdentity.IdentityProviderResponse = JSON.parse(response.body);

        if (addIdpResponse.name == "Microsoft Account") {
            data.logger.info(`Successfully assigned IdP ${addIdpResponse.name}..`);
        } else {
            data.logger.info(`Unable to add IdP to account, exiting..`);
        }

    } else {
        data.logger.info("Idp already assigned to the account, skipping the same!");
    }
}