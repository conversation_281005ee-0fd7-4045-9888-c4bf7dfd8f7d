﻿namespace Aveva.Platform.Catalog.Domain.Models
{
    /// <summary>
    /// V1LegacyProtocolMappings.
    /// </summary>
    public class V1LegacyProtocolMappings
    {
        /// <summary>
        /// Gets or sets the dependency mappings.
        /// </summary>
        /// <remarks>
        /// The key is the id of the dependency on the <seealso cref="V1ServiceEntry"/> and the value is the name of the V1 Integration Definition to which it maps.
        /// </remarks>
#pragma warning disable CA2227 // Collection properties should be read only
        public Dictionary<string, V1LegacyProtocolDependencyMapping>? Dependencies { get; set; } = new Dictionary<string, V1LegacyProtocolDependencyMapping>();
#pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// Gets or sets the application mappings.
        /// </summary>
        /// <example>
        /// applications:
        ///  - name: Manager
        ///    capabilityDefinition: Insight
        /// .
        /// </example>
#pragma warning disable CA2227 // Collection properties should be read only
        public List<V1LegacyProtocolMappingsApplication>? Applications { get; set; } = new List<V1LegacyProtocolMappingsApplication>();
#pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// Gets or sets the geography mappings.
        /// </summary>
        /// <remarks>
        /// The key is a Geography and the value is region that exists on the V1 Solution Definition.
        /// </remarks>
        /// <example>
        /// geographies:
        ///  default: eu-west-1
        ///  eu: eu-west-1
        /// .
        /// </example>
#pragma warning disable CA2227 // Collection properties should be read only
        public Dictionary<string, string>? Geographies { get; set; } = new Dictionary<string, string>();
#pragma warning restore CA2227 // Collection properties should be read only

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is V1LegacyProtocolMappings item
                && ((item.Dependencies == null && Dependencies == null) || (item.Dependencies != null && Dependencies != null && item.Dependencies.SequenceEqual(Dependencies)))
                && ((item.Geographies == null && Geographies == null) || (item.Geographies != null && Geographies != null && item.Geographies.SequenceEqual(Geographies)))
                && ((item.Applications == null && Applications == null) || (item.Applications != null && Applications != null && item.Applications.SequenceEqual(Applications)));
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}