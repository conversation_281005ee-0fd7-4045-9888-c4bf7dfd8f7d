<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <!-- Assembly properties -->
  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.Infrastructure</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.Infrastructure</RootNamespace>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>
  <ItemGroup>
	  <PackageReference Include="Aveva.Platform.Common.Monitoring.Instrumentation" />
    <PackageReference Include="Aveva.Platform.Common.Abstractions" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Platform.Catalog.Domain\Platform.Catalog.Domain.csproj" />
    <ProjectReference Include="..\Platform.Catalog.ServiceClient\Platform.Catalog.ServiceClient.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>