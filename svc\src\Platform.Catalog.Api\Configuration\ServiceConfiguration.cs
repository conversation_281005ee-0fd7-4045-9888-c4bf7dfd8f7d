﻿using System.Diagnostics.CodeAnalysis;

namespace Aveva.Platform.Catalog.Api.Configuration;

/// <summary>
/// Central point of management for service configuration settings.
/// </summary>
[ExcludeFromCodeCoverage(Justification = "Unused class")]
internal static class ServiceConfiguration
{
    #region Public Fields

    /// <summary>
    /// The application settings configuration section name.
    /// </summary>
    public const string ApplicationSettings = "Application:";

    /// <summary>
    /// The key vault settings configuration section name.
    /// </summary>
    public const string KeyVaultInfoSettings = "KeyVaultInfo:";

    /// <summary>
    /// The logging settings configuration section name.
    /// </summary>
    public const string LoggingSettings = "Logging:";

    /// <summary>
    /// The telemetry settings configuration section name.
    /// </summary>
    public const string TelemetrySettings = "Telemetry:";

    #endregion Public Fields

    #region Internal Classes

    /// <summary>
    /// Service configuration setting names and settings functionality.
    /// </summary>
    internal static class Settings
    {
        #region General Constants

        internal const string EnableSwaggerUISetting = ApplicationSettings + "EnableSwaggerUI";
        internal const string SwaggerServiceTitleSetting = ApplicationSettings + "SwaggerServiceTitle";
        internal const string SwaggerServiceDescriptionSetting = ApplicationSettings + "SwaggerServiceDescription";
        internal const string SwaggerDocumentNameSetting = ApplicationSettings + "SwaggerDocumentName";
        internal const string SwaggerServiceApiRoutePrefixSetting = ApplicationSettings + "SwaggerServiceApiRoutePrefix";
        internal const string ClusterNameSetting = ApplicationSettings + "ClusterName";

        #endregion General Constants

        #region Key Vault Constants

        internal const string KeyVaultNameSetting = KeyVaultInfoSettings + "KeyVaultName";
        internal const string DevOneKeyVaultNameSetting = KeyVaultInfoSettings + "DevOneKeyVaultName";

        #endregion Key Vault Constants

        #region Telemetry / Logging Constants

        internal const string EnableTelemetrySetting = TelemetrySettings + "EnableTelemtry";
        internal const string LoggingLogLevelSetting = LoggingSettings + "LogLevel";
        internal const string LoggingDevOneSetting = LoggingSettings + "EnableDevOneLogging";
        internal const string LoggingInstrumentationKeyName = "AppInsightsInstrumentationKey";
        internal const string LoggingInstrumentationKeyNameSetting = LoggingSettings + "AppInsightsInstrumentationKey";

        #endregion Telemetry / Logging Constants
    }

    /// <summary>
    /// Logging related service configuration.
    /// </summary>
    internal static class Logging
    {
        #region Internal Constants

        internal const string CustomerFacingApplicationName = "CloudPlatform Catalog";
        internal const string ServiceTitle = "cloud platform Catalog";

        #endregion Internal Constants
    }

    /// <summary>
    /// Documentation related service configuration.
    /// </summary>
    internal static class Documentation
    {
        #region Internal Constants

        internal const string ServiceRoutePrefix = "api-docs/services";
        internal const string DefaultServiceVersion = "1";
        internal const string ServiceTitle = "cloud platform Catalog API";
        internal const string ServiceDescription = "Aveva cloud platform Catalog REST API";

        #endregion Internal Constants
    }

    #endregion Internal Classes
}