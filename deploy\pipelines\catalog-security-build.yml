trigger: none

schedules:
    - cron: "0 0 * * *"
      displayName: Nightly Catalog Security Scan
      branches:
        include:
        - main
      always: true

parameters:
- name: securityPackageVersion
  displayName: Version of the package to check
  type: string
  default: ' '

extends:
  template: catalog-ci-build.yml
  parameters:    
    pipelineType: 'security'
    securityPackageVersion: "${{ parameters.securityPackageVersion }}"