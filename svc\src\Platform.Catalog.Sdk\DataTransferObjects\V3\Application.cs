﻿namespace Aveva.Platform.Catalog.Sdk.DataTransferObjects.V3;

/// <summary>
/// Model for defining applications.
/// </summary>
public class Application
{
    /// <summary>
    /// Initializes a new instance of the <see cref="Application"/> class.
    /// </summary>
    public Application() // Required by EF
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="Application"/> class.
    /// </summary>
    public Application(string name)
    {
        Name = name;
    }

    /// <summary>
    /// Gets or sets name.
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets urls.
    /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
    public IDictionary<string, string>? Urls { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only
}