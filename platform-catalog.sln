﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32014.148
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{AF882300-2072-40BE-BCB6-5BA3AC2C73EA}"
	ProjectSection(SolutionItems) = preProject
		svc\docs\LICENSE.md = svc\docs\LICENSE.md
		svc\docs\PackageIcon.png = svc\docs\PackageIcon.png
		svc\docs\README.md = svc\docs\README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "samples", "samples", "{84389B99-637D-4E80-81D6-78DB7124B928}"
	ProjectSection(SolutionItems) = preProject
		svc\samples\.editorconfig = svc\samples\.editorconfig
		svc\samples\Directory.Build.props = svc\samples\Directory.Build.props
		svc\samples\README.md = svc\samples\README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{E96147C3-2719-427A-9BBD-AAF7FEB43767}"
	ProjectSection(SolutionItems) = preProject
		svc\src\.editorconfig = svc\src\.editorconfig
		svc\src\Directory.Build.props = svc\src\Directory.Build.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{46664E54-4441-40AF-978C-C880371E6CA2}"
	ProjectSection(SolutionItems) = preProject
		svc\test\.editorconfig = svc\test\.editorconfig
		svc\test\Directory.Build.props = svc\test\Directory.Build.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{66F53530-0080-4F85-9D98-EC161733770E}"
	ProjectSection(SolutionItems) = preProject
		svc\tools\.editorconfig = svc\tools\.editorconfig
		svc\tools\Directory.Build.props = svc\tools\Directory.Build.props
		svc\tools\README.md = svc\tools\README.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog", "svc\src\Platform.Catalog\Platform.Catalog.csproj", "{92D10AE0-DEB9-4789-9079-592869052314}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Api", "svc\src\Platform.Catalog.Api\Platform.Catalog.Api.csproj", "{5851E7B5-A387-43C5-9B10-31F16C8E3581}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Domain", "svc\src\Platform.Catalog.Domain\Platform.Catalog.Domain.csproj", "{193EAF41-F683-4522-AD4C-CD8620D0EE0E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Infrastructure", "svc\src\Platform.Catalog.Infrastructure\Platform.Catalog.Infrastructure.csproj", "{177F9595-21DA-4FCA-92FE-A97F291BF9C9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".Solution Items", ".Solution Items", "{CE5271A5-A049-462F-9097-4C6AADEFAA6E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "config", "config", "{2178962A-CA85-4DBB-8517-2DFE20755873}"
	ProjectSection(SolutionItems) = preProject
		svc\.editorconfig = svc\.editorconfig
		svc\CodeAnalysis.props = svc\CodeAnalysis.props
		coverage.runsettings = coverage.runsettings
		svc\Directory.Build.props = svc\Directory.Build.props
		svc\Directory.Packages.props = svc\Directory.Packages.props
		global.json = global.json
		nuget.config = nuget.config
		svc\stylecop.json = svc\stylecop.json
		svc\WebSign.props = svc\WebSign.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "PlatformCatalogService", "PlatformCatalogService", "{3741FC07-CB73-436B-BFC8-24822E71FEA0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "pipelines", "pipelines", "{F62B7EBE-7A50-42EA-A008-BBB6F3EAD54E}"
	ProjectSection(SolutionItems) = preProject
		deploy\pipelines\catalog-ci-build.yml = deploy\pipelines\catalog-ci-build.yml
		deploy\pipelines\catalog-client-publish.yml = deploy\pipelines\catalog-client-publish.yml
		deploy\pipelines\catalog-gated-build.yml = deploy\pipelines\catalog-gated-build.yml
		deploy\pipelines\catalog-perf-test.yml = deploy\pipelines\catalog-perf-test.yml
		deploy\pipelines\catalog-security-build.yml = deploy\pipelines\catalog-security-build.yml
		deploy\pipelines\catalog-test.yml = deploy\pipelines\catalog-test.yml
		deploy\pipelines\polaris.yml = deploy\pipelines\polaris.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "deploy", "deploy", "{766179F7-78A8-49E2-8227-968B514BFBD0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "helm", "helm", "{1299F7FE-26A9-463E-B0BD-286C68801FB5}"
	ProjectSection(SolutionItems) = preProject
		deploy\helm\Chart.yaml = deploy\helm\Chart.yaml
		deploy\helm\devonebox.values.yaml = deploy\helm\devonebox.values.yaml
		deploy\helm\local.values.yaml = deploy\helm\local.values.yaml
		deploy\helm\pipeline.values.yaml = deploy\helm\pipeline.values.yaml
		deploy\helm\values.yaml = deploy\helm\values.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "templates", "templates", "{00D6E8C6-017F-4ACC-A12C-94E633D40451}"
	ProjectSection(SolutionItems) = preProject
		deploy\helm\templates\catalog-crd.yaml = deploy\helm\templates\catalog-crd.yaml
		deploy\helm\templates\catalog-operator-certs.yaml = deploy\helm\templates\catalog-operator-certs.yaml
		deploy\helm\templates\catalog-operator.yaml = deploy\helm\templates\catalog-operator.yaml
		deploy\helm\templates\catalog-role-assignment.yaml = deploy\helm\templates\catalog-role-assignment.yaml
		deploy\helm\templates\helpers.tpl = deploy\helm\templates\helpers.tpl
		deploy\helm\templates\service-account.yaml = deploy\helm\templates\service-account.yaml
		deploy\helm\templates\standard-include.yaml = deploy\helm\templates\standard-include.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "unit", "unit", "{F8B53A91-8328-4CEF-8381-996F4FC34DFD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "local", "local", "{8036126D-1E73-46D0-A274-8354753B30CB}"
	ProjectSection(SolutionItems) = preProject
		local\helmfile.yaml = local\helmfile.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "yamls", "yamls", "{A45916C2-DA35-45EB-BD81-3CC310DD902B}"
	ProjectSection(SolutionItems) = preProject
		deploy\helm\yamls\deployment.yaml = deploy\helm\yamls\deployment.yaml
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Operator", "svc\src\Platform.Catalog.Operator\Platform.Catalog.Operator.csproj", "{9204229F-C59D-42B8-AFC5-DFC25AEFA740}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "crhelm", "crhelm", "{DA9883D0-07FB-4D3C-B8E9-FE71988551B1}"
	ProjectSection(SolutionItems) = preProject
		deploy\crhelm\Chart.yaml = deploy\crhelm\Chart.yaml
		deploy\crhelm\values.yaml = deploy\crhelm\values.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "templates", "templates", "{CD66F662-1927-4C87-9897-873544F62D44}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "catalog", "catalog", "{105F7C5B-1C12-476C-AC38-D558B52FDE2B}"
	ProjectSection(SolutionItems) = preProject
		deploy\crhelm\templates\catalog\service-entry.yaml = deploy\crhelm\templates\catalog\service-entry.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "gateway", "gateway", "{F1016DA1-7052-467A-80BA-2D64D8FB5B55}"
	ProjectSection(SolutionItems) = preProject
		deploy\crhelm\templates\gateway\open-api-ops-service-v2.yaml = deploy\crhelm\templates\gateway\open-api-ops-service-v2.yaml
		deploy\crhelm\templates\gateway\service-api-customer-v2.yaml = deploy\crhelm\templates\gateway\service-api-customer-v2.yaml
		deploy\crhelm\templates\gateway\service-api-ops-v2.yaml = deploy\crhelm\templates\gateway\service-api-ops-v2.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "debug", "debug", "{169C759D-6F54-44C4-871C-77FFBD1E8138}"
	ProjectSection(SolutionItems) = preProject
		local\debug\additionalvalues.yaml = local\debug\additionalvalues.yaml
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Events", "svc\src\Platform.Catalog.Events\Platform.Catalog.Events.csproj", "{3A769D38-56FF-4627-B1B2-924CD1D888EE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "integration", "integration", "{FBFF5EDC-D1AE-4C87-B6BC-D5B5407EB0FF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "deployment", "deployment", "{E8BD96AD-77B5-4D3F-AE24-85EFFD7FC0D2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "contract", "contract", "{9BCDE2DA-7768-4155-9F48-85E2056602A4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Tests.Deployment", "svc\test\Platform.Catalog.Tests.Deployment\Platform.Catalog.Tests.Deployment.csproj", "{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.ContractTests", "svc\test\Platform.Catalog.ContractTests\Platform.Catalog.ContractTests\Platform.Catalog.ContractTests.csproj", "{22D7AE98-589E-485F-B243-746751B7EDE7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Api.Tests.Integration", "svc\test\Platform.Catalog.Api.Tests.Integration\Platform.Catalog.Api.Tests.Integration.csproj", "{82422988-C71A-4B23-95FC-6BE131810022}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Domain.Tests.Integration", "svc\test\Platform.Catalog.Domain.Tests.Integration\Platform.Catalog.Domain.Tests.Integration.csproj", "{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Infrastructure.Tests.Integration", "svc\test\Platform.Catalog.Infrastructure.Tests.Integration\Platform.Catalog.Infrastructure.Tests.Integration.csproj", "{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Tests.Integration", "svc\test\Platform.Catalog.Tests.Integration\Platform.Catalog.Tests.Integration.csproj", "{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Tests.Integration.Kube", "svc\test\Platform.Catalog.Tests.Integration.Kube\Platform.Catalog.Tests.Integration.Kube.csproj", "{DEEB540B-6B56-4A38-BE0E-84424D36B01A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Events.Tests.Unit", "svc\test\Platform.Catalog.Events.Tests.Unit\Platform.Catalog.Events.Tests.Unit.csproj", "{F886220F-E9FC-4A54-B3BD-0461D74DE724}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Api.Tests.Unit", "svc\test\Platform.Catalog.Api.Tests.Unit\Platform.Catalog.Api.Tests.Unit.csproj", "{F5B45583-2ABA-4E42-86D9-C37068691787}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Domain.Tests.Unit", "svc\test\Platform.Catalog.Domain.Tests.Unit\Platform.Catalog.Domain.Tests.Unit.csproj", "{C365AE2D-99E0-491B-B33E-15653B4F1A7F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Infrastructure.Tests.Unit", "svc\test\Platform.Catalog.Infrastructure.Tests.Unit\Platform.Catalog.Infrastructure.Tests.Unit.csproj", "{CEEDC807-9A52-4E2B-B2E9-753DF556425A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Operator.Tests.Unit", "svc\test\Platform.Catalog.Operator.Tests.Unit\Platform.Catalog.Operator.Tests.Unit.csproj", "{59ACAF23-4C82-4177-BF53-9217138DE9CD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Tests.Unit", "svc\test\Platform.Catalog.Tests.Unit\Platform.Catalog.Tests.Unit.csproj", "{74691D44-064B-42F8-9A11-09D23DEFAB17}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "dashboards", "dashboards", "{1B7FDFAD-A498-45E4-896F-8054550B9A47}"
	ProjectSection(SolutionItems) = preProject
		deploy\dashboards\catalog.json = deploy\dashboards\catalog.json
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Library.Catalog.Client", "lib\Aveva.Platform.Library.Catalog\svc\src\Aveva.Platform.Catalog.Client\Platform.Library.Catalog.Client.csproj", "{DF51AFCB-695E-46FA-A046-57D54A73777E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "variables", "variables", "{1D51E2CC-C230-4155-B45B-9E0EDE5E30E5}"
	ProjectSection(SolutionItems) = preProject
		deploy\pipelines\variables\k6TestFiles.yml = deploy\pipelines\variables\k6TestFiles.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "chaosmesh", "chaosmesh", "{D1A624FA-306F-4280-BCB3-BD04A96F1C9A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "experiments", "experiments", "{F19094A8-0F69-4306-A9CA-E0BAC27E2251}"
	ProjectSection(SolutionItems) = preProject
		svc\samples\chaosmesh\experiments\api-node-cpu.yaml = svc\samples\chaosmesh\experiments\api-node-cpu.yaml
		svc\samples\chaosmesh\experiments\api-node-memory.yaml = svc\samples\chaosmesh\experiments\api-node-memory.yaml
		svc\samples\chaosmesh\experiments\api-pod-failure.yaml = svc\samples\chaosmesh\experiments\api-pod-failure.yaml
		svc\samples\chaosmesh\experiments\api-pod-kill.yaml = svc\samples\chaosmesh\experiments\api-pod-kill.yaml
		svc\samples\chaosmesh\experiments\authn-api-pod-failure.yaml = svc\samples\chaosmesh\experiments\authn-api-pod-failure.yaml
		svc\samples\chaosmesh\experiments\authz-api-pod-failure.yaml = svc\samples\chaosmesh\experiments\authz-api-pod-failure.yaml
		svc\samples\chaosmesh\experiments\events-node-cpu.yaml = svc\samples\chaosmesh\experiments\events-node-cpu.yaml
		svc\samples\chaosmesh\experiments\events-node-memory.yaml = svc\samples\chaosmesh\experiments\events-node-memory.yaml
		svc\samples\chaosmesh\experiments\events-pod-failure.yaml = svc\samples\chaosmesh\experiments\events-pod-failure.yaml
		svc\samples\chaosmesh\experiments\events-pod-kill.yaml = svc\samples\chaosmesh\experiments\events-pod-kill.yaml
		svc\samples\chaosmesh\experiments\network-delay.yaml = svc\samples\chaosmesh\experiments\network-delay.yaml
		svc\samples\chaosmesh\experiments\network-loss.yaml = svc\samples\chaosmesh\experiments\network-loss.yaml
		svc\samples\chaosmesh\experiments\operator-node-cpu.yaml = svc\samples\chaosmesh\experiments\operator-node-cpu.yaml
		svc\samples\chaosmesh\experiments\operator-node-memory.yaml = svc\samples\chaosmesh\experiments\operator-node-memory.yaml
		svc\samples\chaosmesh\experiments\operator-pod-failure.yaml = svc\samples\chaosmesh\experiments\operator-pod-failure.yaml
		svc\samples\chaosmesh\experiments\operator-pod-kill.yaml = svc\samples\chaosmesh\experiments\operator-pod-kill.yaml
		svc\samples\chaosmesh\experiments\servicebus-network-failure.yaml = svc\samples\chaosmesh\experiments\servicebus-network-failure.yaml
		svc\samples\chaosmesh\experiments\servicebus-network-loss.yaml = svc\samples\chaosmesh\experiments\servicebus-network-loss.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "schedules", "schedules", "{67869168-76A5-4710-AEA8-78B3ABDC517F}"
	ProjectSection(SolutionItems) = preProject
		svc\samples\chaosmesh\schedules\network-delay-schedule.yaml = svc\samples\chaosmesh\schedules\network-delay-schedule.yaml
		svc\samples\chaosmesh\schedules\network-failure-schedule.yaml = svc\samples\chaosmesh\schedules\network-failure-schedule.yaml
		svc\samples\chaosmesh\schedules\network-loss-schedule.yaml = svc\samples\chaosmesh\schedules\network-loss-schedule.yaml
		svc\samples\chaosmesh\schedules\node-cpu-schedule.yaml = svc\samples\chaosmesh\schedules\node-cpu-schedule.yaml
		svc\samples\chaosmesh\schedules\node-memory-schedule.yaml = svc\samples\chaosmesh\schedules\node-memory-schedule.yaml
		svc\samples\chaosmesh\schedules\pod-failure-schedule.yaml = svc\samples\chaosmesh\schedules\pod-failure-schedule.yaml
		svc\samples\chaosmesh\schedules\pod-kill-schedule.yaml = svc\samples\chaosmesh\schedules\pod-kill-schedule.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "setup", "setup", "{E8BE095F-13E4-4BF3-8837-39849C9D0BCE}"
	ProjectSection(SolutionItems) = preProject
		svc\samples\chaosmesh\setup\rbac.yaml = svc\samples\chaosmesh\setup\rbac.yaml
		svc\samples\chaosmesh\setup\test-cr.yaml = svc\samples\chaosmesh\setup\test-cr.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "performance", "performance", "{ABD4762B-F533-4308-9BC0-14778F8C4076}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "access-management", "access-management", "{E5681A99-F0FC-4A3B-B87A-A6C8497088CA}"
	ProjectSection(SolutionItems) = preProject
		deploy\crhelm\templates\access-management\ops-reader.yaml = deploy\crhelm\templates\access-management\ops-reader.yaml
		deploy\crhelm\templates\access-management\policymanifest-ops.yaml = deploy\crhelm\templates\access-management\policymanifest-ops.yaml
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.ServiceClient", "svc\src\Platform.Catalog.ServiceClient\Platform.Catalog.ServiceClient.csproj", "{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.ServiceClient.Tests.Unit", "svc\test\Platform.Catalog.ServiceClient.Tests.Unit\Platform.Catalog.ServiceClient.Tests.Unit.csproj", "{433E3BFA-4495-4992-B73A-85681B077326}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "templates", "templates", "{92E7C0AA-4386-4DDC-AF64-D772F482FB59}"
	ProjectSection(SolutionItems) = preProject
		deploy\pipelines\templates\tsclient-publish.yml = deploy\pipelines\templates\tsclient-publish.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "scripts", "scripts", "{87EA31D4-AF79-421D-ADD7-47A4B7C7A08C}"
	ProjectSection(SolutionItems) = preProject
		deploy\pipelines\scripts\Update-NpmPackageVersion.ps1 = deploy\pipelines\scripts\Update-NpmPackageVersion.ps1
	EndProjectSection
EndProject
Project("{54A90642-561A-4BB1-A94E-469ADEE60C69}") = "Platform.Catalog.Tests.Performance", "svc\test\Platform.Catalog.Tests.Performance\Platform.Catalog.Tests.Performance\Platform.Catalog.Tests.Performance.esproj", "{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Tests.PerformanceSetup", "svc\test\Platform.Catalog.Tests.Performance\Platform.Catalog.Tests.PerformanceSetup\Platform.Catalog.Tests.PerformanceSetup.csproj", "{002C2946-33D0-40EA-B49E-98657FEB33FE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Platform.Catalog.Sdk", "svc\src\Platform.Catalog.Sdk\Platform.Catalog.Sdk.csproj", "{8960C483-CE76-C73E-97B8-4747A093E2C5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		DebugContainers|Any CPU = DebugContainers|Any CPU
		DebugContainers|x64 = DebugContainers|x64
		DebugNoCheck|Any CPU = DebugNoCheck|Any CPU
		DebugNoCheck|x64 = DebugNoCheck|x64
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		ReleasePolaris|Any CPU = ReleasePolaris|Any CPU
		ReleasePolaris|x64 = ReleasePolaris|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{92D10AE0-DEB9-4789-9079-592869052314}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.Debug|x64.ActiveCfg = Debug|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.Debug|x64.Build.0 = Debug|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.Release|Any CPU.Build.0 = Release|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.Release|x64.ActiveCfg = Release|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.Release|x64.Build.0 = Release|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{92D10AE0-DEB9-4789-9079-592869052314}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.Debug|x64.Build.0 = Debug|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.Release|Any CPU.Build.0 = Release|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.Release|x64.ActiveCfg = Release|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.Release|x64.Build.0 = Release|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{5851E7B5-A387-43C5-9B10-31F16C8E3581}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.Debug|x64.Build.0 = Debug|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.Release|Any CPU.Build.0 = Release|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.Release|x64.ActiveCfg = Release|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.Release|x64.Build.0 = Release|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.Debug|x64.Build.0 = Debug|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.Release|Any CPU.Build.0 = Release|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.Release|x64.ActiveCfg = Release|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.Release|x64.Build.0 = Release|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.Debug|x64.Build.0 = Debug|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.Release|Any CPU.Build.0 = Release|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.Release|x64.ActiveCfg = Release|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.Release|x64.Build.0 = Release|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.Debug|x64.Build.0 = Debug|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.Release|x64.ActiveCfg = Release|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.Release|x64.Build.0 = Release|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{3A769D38-56FF-4627-B1B2-924CD1D888EE}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.Debug|x64.Build.0 = Debug|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.Release|x64.ActiveCfg = Release|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.Release|x64.Build.0 = Release|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.Debug|x64.ActiveCfg = Debug|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.Debug|x64.Build.0 = Debug|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.Release|x64.ActiveCfg = Release|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.Release|x64.Build.0 = Release|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{22D7AE98-589E-485F-B243-746751B7EDE7}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.Debug|x64.ActiveCfg = Debug|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.Debug|x64.Build.0 = Debug|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.Release|Any CPU.Build.0 = Release|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.Release|x64.ActiveCfg = Release|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.Release|x64.Build.0 = Release|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{82422988-C71A-4B23-95FC-6BE131810022}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.Debug|x64.Build.0 = Debug|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.Release|Any CPU.Build.0 = Release|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.Release|x64.ActiveCfg = Release|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.Release|x64.Build.0 = Release|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.Debug|x64.Build.0 = Debug|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.Release|Any CPU.Build.0 = Release|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.Release|x64.ActiveCfg = Release|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.Release|x64.Build.0 = Release|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.Debug|x64.Build.0 = Debug|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.Release|x64.ActiveCfg = Release|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.Release|x64.Build.0 = Release|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.Debug|x64.Build.0 = Debug|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.Release|Any CPU.Build.0 = Release|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.Release|x64.ActiveCfg = Release|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.Release|x64.Build.0 = Release|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.Debug|x64.Build.0 = Debug|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.Release|Any CPU.Build.0 = Release|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.Release|x64.ActiveCfg = Release|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.Release|x64.Build.0 = Release|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{F886220F-E9FC-4A54-B3BD-0461D74DE724}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.Debug|x64.Build.0 = Debug|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.Release|Any CPU.Build.0 = Release|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.Release|x64.ActiveCfg = Release|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.Release|x64.Build.0 = Release|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{F5B45583-2ABA-4E42-86D9-C37068691787}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.Debug|x64.Build.0 = Debug|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.Release|Any CPU.Build.0 = Release|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.Release|x64.ActiveCfg = Release|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.Release|x64.Build.0 = Release|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.Debug|x64.Build.0 = Debug|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.Release|Any CPU.Build.0 = Release|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.Release|x64.ActiveCfg = Release|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.Release|x64.Build.0 = Release|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.Debug|x64.Build.0 = Debug|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.Release|Any CPU.Build.0 = Release|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.Release|x64.ActiveCfg = Release|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.Release|x64.Build.0 = Release|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{59ACAF23-4C82-4177-BF53-9217138DE9CD}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.Debug|x64.ActiveCfg = Debug|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.Debug|x64.Build.0 = Debug|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.Release|Any CPU.Build.0 = Release|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.Release|x64.ActiveCfg = Release|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.Release|x64.Build.0 = Release|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{74691D44-064B-42F8-9A11-09D23DEFAB17}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.Debug|x64.Build.0 = Debug|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.DebugContainers|Any CPU.ActiveCfg = DebugContainers|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.DebugContainers|Any CPU.Build.0 = DebugContainers|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.DebugContainers|x64.ActiveCfg = DebugContainers|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.DebugContainers|x64.Build.0 = DebugContainers|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.Release|x64.ActiveCfg = Release|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.Release|x64.Build.0 = Release|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{DF51AFCB-695E-46FA-A046-57D54A73777E}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.Debug|x64.ActiveCfg = Debug|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.Debug|x64.Build.0 = Debug|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.Release|Any CPU.Build.0 = Release|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.Release|x64.ActiveCfg = Release|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.Release|x64.Build.0 = Release|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.Debug|x64.ActiveCfg = Debug|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.Debug|x64.Build.0 = Debug|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.Release|Any CPU.Build.0 = Release|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.Release|x64.ActiveCfg = Release|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.Release|x64.Build.0 = Release|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{433E3BFA-4495-4992-B73A-85681B077326}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Debug|x64.Build.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Debug|x64.Deploy.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugContainers|Any CPU.ActiveCfg = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugContainers|Any CPU.Build.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugContainers|Any CPU.Deploy.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugContainers|x64.ActiveCfg = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugContainers|x64.Build.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugContainers|x64.Deploy.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugNoCheck|Any CPU.ActiveCfg = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugNoCheck|Any CPU.Build.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugNoCheck|Any CPU.Deploy.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugNoCheck|x64.ActiveCfg = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugNoCheck|x64.Build.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.DebugNoCheck|x64.Deploy.0 = Debug|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Release|Any CPU.Build.0 = Release|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Release|x64.ActiveCfg = Release|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Release|x64.Build.0 = Release|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.Release|x64.Deploy.0 = Release|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9}.ReleasePolaris|x64.Deploy.0 = ReleasePolaris|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.Debug|x64.Build.0 = Debug|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.DebugContainers|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.DebugContainers|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.DebugContainers|x64.ActiveCfg = DebugNoCheck|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.DebugContainers|x64.Build.0 = DebugNoCheck|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.Release|x64.ActiveCfg = Release|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.Release|x64.Build.0 = Release|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{002C2946-33D0-40EA-B49E-98657FEB33FE}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.Debug|x64.Build.0 = Debug|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.DebugContainers|Any CPU.ActiveCfg = Debug|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.DebugContainers|Any CPU.Build.0 = Debug|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.DebugContainers|x64.ActiveCfg = Debug|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.DebugContainers|x64.Build.0 = Debug|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.DebugNoCheck|x64.ActiveCfg = DebugNoCheck|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.DebugNoCheck|x64.Build.0 = DebugNoCheck|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.Release|x64.ActiveCfg = Release|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.Release|x64.Build.0 = Release|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.ReleasePolaris|Any CPU.ActiveCfg = ReleasePolaris|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.ReleasePolaris|Any CPU.Build.0 = ReleasePolaris|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.ReleasePolaris|x64.ActiveCfg = ReleasePolaris|Any CPU
		{8960C483-CE76-C73E-97B8-4747A093E2C5}.ReleasePolaris|x64.Build.0 = ReleasePolaris|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{92D10AE0-DEB9-4789-9079-592869052314} = {3741FC07-CB73-436B-BFC8-24822E71FEA0}
		{5851E7B5-A387-43C5-9B10-31F16C8E3581} = {3741FC07-CB73-436B-BFC8-24822E71FEA0}
		{193EAF41-F683-4522-AD4C-CD8620D0EE0E} = {3741FC07-CB73-436B-BFC8-24822E71FEA0}
		{177F9595-21DA-4FCA-92FE-A97F291BF9C9} = {3741FC07-CB73-436B-BFC8-24822E71FEA0}
		{2178962A-CA85-4DBB-8517-2DFE20755873} = {CE5271A5-A049-462F-9097-4C6AADEFAA6E}
		{3741FC07-CB73-436B-BFC8-24822E71FEA0} = {E96147C3-2719-427A-9BBD-AAF7FEB43767}
		{F62B7EBE-7A50-42EA-A008-BBB6F3EAD54E} = {766179F7-78A8-49E2-8227-968B514BFBD0}
		{766179F7-78A8-49E2-8227-968B514BFBD0} = {CE5271A5-A049-462F-9097-4C6AADEFAA6E}
		{1299F7FE-26A9-463E-B0BD-286C68801FB5} = {766179F7-78A8-49E2-8227-968B514BFBD0}
		{00D6E8C6-017F-4ACC-A12C-94E633D40451} = {1299F7FE-26A9-463E-B0BD-286C68801FB5}
		{F8B53A91-8328-4CEF-8381-996F4FC34DFD} = {46664E54-4441-40AF-978C-C880371E6CA2}
		{8036126D-1E73-46D0-A274-8354753B30CB} = {CE5271A5-A049-462F-9097-4C6AADEFAA6E}
		{A45916C2-DA35-45EB-BD81-3CC310DD902B} = {1299F7FE-26A9-463E-B0BD-286C68801FB5}
		{9204229F-C59D-42B8-AFC5-DFC25AEFA740} = {3741FC07-CB73-436B-BFC8-24822E71FEA0}
		{DA9883D0-07FB-4D3C-B8E9-FE71988551B1} = {766179F7-78A8-49E2-8227-968B514BFBD0}
		{CD66F662-1927-4C87-9897-873544F62D44} = {DA9883D0-07FB-4D3C-B8E9-FE71988551B1}
		{105F7C5B-1C12-476C-AC38-D558B52FDE2B} = {CD66F662-1927-4C87-9897-873544F62D44}
		{F1016DA1-7052-467A-80BA-2D64D8FB5B55} = {CD66F662-1927-4C87-9897-873544F62D44}
		{169C759D-6F54-44C4-871C-77FFBD1E8138} = {8036126D-1E73-46D0-A274-8354753B30CB}
		{3A769D38-56FF-4627-B1B2-924CD1D888EE} = {3741FC07-CB73-436B-BFC8-24822E71FEA0}
		{FBFF5EDC-D1AE-4C87-B6BC-D5B5407EB0FF} = {46664E54-4441-40AF-978C-C880371E6CA2}
		{E8BD96AD-77B5-4D3F-AE24-85EFFD7FC0D2} = {46664E54-4441-40AF-978C-C880371E6CA2}
		{9BCDE2DA-7768-4155-9F48-85E2056602A4} = {46664E54-4441-40AF-978C-C880371E6CA2}
		{AA7956F4-EEE4-4A9D-8B6E-52A482D05EE9} = {E8BD96AD-77B5-4D3F-AE24-85EFFD7FC0D2}
		{22D7AE98-589E-485F-B243-746751B7EDE7} = {9BCDE2DA-7768-4155-9F48-85E2056602A4}
		{82422988-C71A-4B23-95FC-6BE131810022} = {FBFF5EDC-D1AE-4C87-B6BC-D5B5407EB0FF}
		{F5614740-ABE2-4AD1-B4D0-91D5A9DDE6F3} = {FBFF5EDC-D1AE-4C87-B6BC-D5B5407EB0FF}
		{0E4B0E62-88F5-48AF-8FD4-4626917F88FB} = {FBFF5EDC-D1AE-4C87-B6BC-D5B5407EB0FF}
		{8BAEB061-3193-4D91-89D1-3FCFBA4CB2CE} = {FBFF5EDC-D1AE-4C87-B6BC-D5B5407EB0FF}
		{DEEB540B-6B56-4A38-BE0E-84424D36B01A} = {FBFF5EDC-D1AE-4C87-B6BC-D5B5407EB0FF}
		{F886220F-E9FC-4A54-B3BD-0461D74DE724} = {F8B53A91-8328-4CEF-8381-996F4FC34DFD}
		{F5B45583-2ABA-4E42-86D9-C37068691787} = {F8B53A91-8328-4CEF-8381-996F4FC34DFD}
		{C365AE2D-99E0-491B-B33E-15653B4F1A7F} = {F8B53A91-8328-4CEF-8381-996F4FC34DFD}
		{CEEDC807-9A52-4E2B-B2E9-753DF556425A} = {F8B53A91-8328-4CEF-8381-996F4FC34DFD}
		{59ACAF23-4C82-4177-BF53-9217138DE9CD} = {F8B53A91-8328-4CEF-8381-996F4FC34DFD}
		{74691D44-064B-42F8-9A11-09D23DEFAB17} = {F8B53A91-8328-4CEF-8381-996F4FC34DFD}
		{1B7FDFAD-A498-45E4-896F-8054550B9A47} = {766179F7-78A8-49E2-8227-968B514BFBD0}
		{DF51AFCB-695E-46FA-A046-57D54A73777E} = {3741FC07-CB73-436B-BFC8-24822E71FEA0}
		{1D51E2CC-C230-4155-B45B-9E0EDE5E30E5} = {F62B7EBE-7A50-42EA-A008-BBB6F3EAD54E}
		{D1A624FA-306F-4280-BCB3-BD04A96F1C9A} = {84389B99-637D-4E80-81D6-78DB7124B928}
		{F19094A8-0F69-4306-A9CA-E0BAC27E2251} = {D1A624FA-306F-4280-BCB3-BD04A96F1C9A}
		{67869168-76A5-4710-AEA8-78B3ABDC517F} = {D1A624FA-306F-4280-BCB3-BD04A96F1C9A}
		{E8BE095F-13E4-4BF3-8837-39849C9D0BCE} = {D1A624FA-306F-4280-BCB3-BD04A96F1C9A}
		{ABD4762B-F533-4308-9BC0-14778F8C4076} = {46664E54-4441-40AF-978C-C880371E6CA2}
		{E5681A99-F0FC-4A3B-B87A-A6C8497088CA} = {CD66F662-1927-4C87-9897-873544F62D44}
		{25BE4E2F-6C7A-4FC0-8E5F-D64B7F4E8215} = {3741FC07-CB73-436B-BFC8-24822E71FEA0}
		{433E3BFA-4495-4992-B73A-85681B077326} = {F8B53A91-8328-4CEF-8381-996F4FC34DFD}
		{92E7C0AA-4386-4DDC-AF64-D772F482FB59} = {F62B7EBE-7A50-42EA-A008-BBB6F3EAD54E}
		{87EA31D4-AF79-421D-ADD7-47A4B7C7A08C} = {F62B7EBE-7A50-42EA-A008-BBB6F3EAD54E}
		{AC7CFAD0-5729-4AA6-BF4E-76DC014DCBF9} = {ABD4762B-F533-4308-9BC0-14778F8C4076}
		{002C2946-33D0-40EA-B49E-98657FEB33FE} = {ABD4762B-F533-4308-9BC0-14778F8C4076}
		{8960C483-CE76-C73E-97B8-4747A093E2C5} = {3741FC07-CB73-436B-BFC8-24822E71FEA0}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F17771B2-A98B-4910-A107-3502742E7501}
	EndGlobalSection
EndGlobal
