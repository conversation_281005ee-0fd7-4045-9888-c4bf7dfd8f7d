﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
     <AssemblyName>Aveva.Platform.Catalog.Operator</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.Operator</RootNamespace>    
    <UserSecretsId>fa60f2d6-4b5a-410b-93cb-75bed3864bbe</UserSecretsId>
    <ContainerImageName>aveva-platform-catalog-operator</ContainerImageName>
    <ContainerImageTag>latest</ContainerImageTag>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702,1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702,1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleasePolaris|AnyCPU'">
    <NoWarn>1701;1702,1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugNoCheck|AnyCPU'">
    <NoWarn>1701;1702,1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Aveva.Platform.Common.Abstractions" />
    <PackageReference Include="Aveva.Platform.Common.Framework.ExceptionHandling" />
    <PackageReference Include="Aveva.Platform.Common.Monitoring.Instrumentation" />
    <PackageReference Include="KubeOps.Generator">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="KubeOps.Operator.Web" />
    <PackageReference Include="Localtunnel" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Platform.Catalog.Domain\Platform.Catalog.Domain.csproj" />
    <ProjectReference Include="..\Platform.Catalog.ServiceClient\Platform.Catalog.ServiceClient.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>
