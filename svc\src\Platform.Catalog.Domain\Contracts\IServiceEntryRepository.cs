﻿using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Domain.Queries;

namespace Aveva.Platform.Catalog.Domain.Contracts
{
    /// <summary>
    /// Interface to implement ICachedCatalog Repository.
    /// </summary>
    public interface IServiceEntryRepository
    {
        /// <summary>
        /// Gets all the ServiceEntries from Catalog memory cache.
        /// </summary>
        /// <returns>List of catalog entires.</returns>
        public Task<IEnumerable<V1ServiceEntry>> QueryAsync(ServiceEntryQuery query);

        /// <summary>
        /// Gets the ServiceEntry for a microservice.
        /// </summary>
        /// <returns>Service entry for a microservice.</returns>
        public Task<V1ServiceEntry?> GetByIdAsync(string id);

        /// <summary>
        /// Clears the cache from Catalog service memory.
        /// </summary>
        public void ClearCache();
    }
}