kind: ServiceAccount
apiVersion: v1
metadata:
  namespace: default
  name: account-cluster-manager-fulgo

---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: role-cluster-manager-fulgo
rules:
- apiGroups: [""]
  resources: ["pods", "namespaces"]
  verbs: ["get", "watch", "list"]
- apiGroups: ["chaos-mesh.org"]
  resources: [ "*" ]
  verbs: ["get", "list", "watch", "create", "delete", "patch", "update"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: bind-cluster-manager-fulgo
subjects:
- kind: ServiceAccount
  name: account-cluster-manager-fulgo
  namespace: default
roleRef:
  kind: ClusterRole
  name: role-cluster-manager-fulgo
  apiGroup: rbac.authorization.k8s.io
