// <auto-generated/>
#pragma warning disable CS0618
using Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Api.Api.Account
{
    /// <summary>
    /// Builds and executes requests for operations under \api\account
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class AccountRequestBuilder : BaseRequestBuilder
    {
        /// <summary>Gets an item from the Aveva.Platform.Catalog.Client.V2.Api.api.account.item collection</summary>
        /// <param name="position">The unique identifier of the account. This ID represents the specific account that will access or own the resources being managed.</param>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.WithAccountItemRequestBuilder"/></returns>
        public global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.WithAccountItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("accountId", position);
                return new global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.Item.WithAccountItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.AccountRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public AccountRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/account", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::Aveva.Platform.Catalog.Client.V2.Api.Api.Account.AccountRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public AccountRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/account", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
