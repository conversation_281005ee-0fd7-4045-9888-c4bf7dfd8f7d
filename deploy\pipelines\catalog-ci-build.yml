trigger:
  branches:
    include:
    - main

resources:
  repositories:
    # Platform-deployment resources
    - repository: 'platform-deployment'
      type: git
      name: 'platform-deployment'
      ref: refs/heads/main

    - repository: 'platform-deployment.perftest'
      type: git
      name: 'platform-deployment.perftest'
      ref: refs/heads/main

    - repository: CodeSigning
      type: git
      name: DevOps/devops.codesigning.templates
      ref: 'refs/heads/main'

    - repository: ArchitectureRepo
      type: git
      name: Architecture/Architecture
      ref: refs/heads/main
    
    - repository: OpsGuildPipelineTools
      type: git
      name: DevOps/opsguild.pipeline-tools
      ref: refs/heads/main

parameters:
- name: promotePackageToDevReady
  type: boolean
  default: true

- name: pipelineType
  type: string
  values:
    - 'ci'
    - 'gated'
    - 'security'
  default: 'ci'

- name: unitTests
  type: object
  default:
    - unitTestFilePath: '**/*.Tests.Unit/*.Tests.Unit.csproj' 
      unitTestFilter: 'Category=Unit' 
      
- name: integrationTests
  type: object
  default:
    - integrationTestName: Catalog
      integrationTestFilePath: '**/*.Integration.Kube.csproj'
      integrationTestDLL: 'Platform.Catalog.Tests.Integration.Kube.dll'
      integrationTestFilter: ''
      includeInPackage: true
      requireKubeCluster: true
    - integrationTestFilePath: '**/*.Tests.Integration/*.Tests.Integration.csproj' 
      integrationTestFilter: 'Tag!=RequiresCosmosEmulator' 

- name: packagedContractTests
  type: object
  default:
    - contractClient: 'Aveva.Platform.Authentication.Sdk.S2SCommon.ContractTests'
      contractTestFilter: 'DisplayName~ServiceToServiceAuthenticationContractTests'
    - contractClient: 'Aveva.Platform.Authorization.Sdk.ContractTests'
      contractTestFilter: 'DisplayName~AuthorizationSdkContractTests'

- name: contractTests
  type: object
  default:
    - contractTestFilePath: '**/Platform.Catalog.ContractTests.csproj' 
      contractTestFilter: ''

- name: deploymentTests
  type: object
  default:
    - testDLL: 'Aveva.Platform.Catalog.Tests.Deployment.dll'
      projectName: 'Platform.Catalog.Tests.Deployment'
      testFilter: '' # This is the string filter used to filter which unit tests should run. To see how to structure this filter, see this xUnit documentation on formatting the expression.
      environment: '*' # The environment where this test will run. Can be multiple environments with comma separated values.

- name: performanceTests
  type: object
  default:
    - performanceTestName: 'CatalogPerformanceTests' # The test identifier.
      performanceTestFilePath: 'svc/test/Platform.Catalog.Tests.Performance/Platform.Catalog.Tests.Performance' # The file path to the performance tests project.
      performanceTSConfigPath: 'svc/test/Platform.Catalog.Tests.Performance/Platform.Catalog.Tests.Performance/tsconfig.json' # The file path to the tsconfig file for the performance tests.

- name: containerProperties
  type: object
  default:
    - imageName: 'aveva-platform-catalog-api'
      projectFilePath: 'svc/src/Platform.Catalog' # The file path to the project for this container.  Must contain .csproj and Dockerfile
    - imageName: 'aveva-platform-catalog-operator'
      projectFilePath: 'svc/src/Platform.Catalog.Operator' # The file path to the project for this container.  Must contain .csproj and Dockerfile
      workingDirectoryOverride: 'operator'
    - imageName: 'aveva-platform-catalog-events'
      projectFilePath: 'svc/src/Platform.Catalog.Events'


- name: polarisFilePath
  default: 'deploy/pipelines/polaris.yml'

- name: ciBuildTags # can delete
  type: object
  default:
    - 'Catalog-CI'

- name: webhookEnvironmentTypeFilter
  type: string
  default: 'development, production'


- name: waitForIssues
  type: boolean
  default: true
- name: promotePackage
  type: boolean
  default: true
- name: securityPackageVersion
  displayName: Version of the package to check
  type: string
  default: 'ignore for CI builds'

variables:
# The file path to the sln file for the main project.
# This is a file path relative to the top level of the current repo.
- name: buildFilePath
  value: '.'
- name: buildFileName
  value: 'platform-catalog.sln'
- name: nugetConfigFilePath
  value: 'nuget.config'
- name: dotNetVersion
  value: '8.x'
- name: serviceHelmChartFolderPath
  value: 'deploy/helm'
- name: serviceCRHelmChartFolderPath
  value: 'deploy/crhelm'
- name: performanceSetupPath
  value: 'svc/test/Platform.Catalog.Tests.Performance/Platform.Catalog.Tests.PerformanceSetup'
- name: serviceClientCodePath
  value: 'lib/Aveva.Platform.Library.Catalog/svc/src/Aveva.Platform.Catalog.Client'
- name: codebaseName
  value: 'Platform Catalog'

stages:
- ${{ if eq(parameters.pipelineType, 'gated')}}:
  - template: pipelines/v2/gated-build.yml@platform-deployment
    parameters:      
      unitTests: ${{ parameters.unitTests }}
      nugetConfigFilePath: ${{ variables.nugetConfigFilePath }}
      dotNetVersion: ${{ variables.dotNetVersion }}
      buildFilePath: ${{ variables.buildFilePath }}
      buildFileName: ${{ variables.buildFileName }}
      binskimPath: '**/src/**/Aveva.Platform.*Catalog*.dll'
      containerPropertiesForValidation: ${{ parameters.containerProperties }}
      serviceClientCodePath: ${{ variables.serviceClientCodePath }}      
      helmChartsForValidation: 
      - type: service
        path: ${{ variables.serviceHelmChartFolderPath }}
        valuesFilePath: 'devonebox.values.yaml'
      - type: cr
        path: ${{ variables.serviceCRHelmChartFolderPath }}
        valuesFilePath: 'values.yaml'

- ${{ if eq(parameters.pipelineType, 'ci') }}:
  - template: pipelines/v2/ci-build.yml@platform-deployment
    parameters:      
      unitTests: ${{ parameters.unitTests }}
      integrationTests: ${{ parameters.integrationTests }}
      contractTests: ${{ parameters.contractTests }}
      packagedContractTests: ${{ parameters.packagedContractTests }}
      containerProperties: ${{ parameters.containerProperties }}
      buildTags: ${{ parameters.ciBuildTags }}
      polarisFilePath: ${{ parameters.polarisFilePath }}
      waitForIssues: ${{ parameters.waitForIssues }}
      buildFilePath: ${{ variables.buildFilePath }}
      buildFileName: ${{ variables.buildFileName }}
      nugetConfigFilePath: ${{ variables.nugetConfigFilePath }}
      dotNetVersion: ${{ variables.dotNetVersion }}
      serviceHelmChartFolderPath: ${{variables.serviceHelmChartFolderPath}}
      serviceCRHelmChartFolderPath: ${{ variables.serviceCRHelmChartFolderPath }}
      deploymentTests: ${{ parameters.deploymentTests }}
      promotePackage: ${{ parameters.promotePackage }}
      promotePackageToDevReady: ${{ parameters.promotePackageToDevReady }}
      webhookForDeploymentFailure: 'https://aveva.webhook.office.com/webhookb2/8bcd7273-43be-46d9-8f53-c6be17d78b91@425a5546-5a6e-4f1b-ab62-23d91d07d893/IncomingWebhook/511d72c6a5a84fb3a04abe06ba0bd318/10d5c349-2336-4b46-a9d2-0524f7cb40e3'
      webhookEnvironmentTypeFilter: ${{ parameters.webhookEnvironmentTypeFilter }}
      binskimPath: '**/src/**/Aveva.Platform.*Catalog*.dll'
      codebaseName: ${{ variables.codebaseName }}
      useWorkloadIdentity: true
      additionalKubeClusterSetupSteps:
      - bash: |
          helm repo add bitnami https://charts.bitnami.com/bitnami
          helm upgrade --install rabbitmq bitnami/rabbitmq --set auth.username=fish,auth.password=bowl,auth.erlangCookie=secretcookie --namespace rabbitmq --create-namespace --wait
          kubectl get pods -n rabbitmq
        displayName: Install Rabbitmq
      performanceTests: ${{ parameters.performanceTests }}
      performanceSetupPath: ${{ variables.performanceSetupPath }}      

- ${{ elseif eq(parameters.pipelineType, 'security') }}:
  - template: pipelines/v2/security-build.yml@platform-deployment
    parameters:
      buildFilePath: '${{ variables.buildFilePath }}/${{ variables.buildFileName }}'
      nugetConfigFilePath: ${{ variables.nugetConfigFilePath }}
      blackDuckProjectName: 'platform-catalog'
      blackDuckVersionName: 'latest'
      useSingleVersion: true
      dotNetVersion: ${{ variables.dotNetVersion }}
      containerProperties: ${{ parameters.containerProperties }}
      runContainerScan: true
      ${{ if ne(replace(parameters.securityPackageVersion, ' ', ''), '') }}:
        securityPackageVersion: "${{ parameters.securityPackageVersion }}"
      additionalDetectParams: '--detect.detector.search.depth=2 --detect.timeout=600'
