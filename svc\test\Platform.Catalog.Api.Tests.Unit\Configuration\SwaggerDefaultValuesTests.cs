﻿using Asp.Versioning.ApiExplorer;
using Aveva.Platform.Catalog.Api.Configuration;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.OpenApi.Models;
using Moq;
using Shouldly;
using Swashbuckle.AspNetCore.SwaggerGen;
using Xunit;

namespace Aveva.Platform.Catalog.Api.Tests.Unit.Configuration;

/// <summary>
/// <see cref="SwaggerDefaultValues"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "App")]
[Trait("Category", "Unit")]
[Trait("Category", "App.Unit")]
[Trait("Tag", "Configuration")]
public class SwaggerDefaultValuesTests
{
    #region Test Cases

    [Fact]
    public void SwaggerDefaultValues_Apply_NullOperation_Throws()
    {
        // Arrange
        OpenApiOperation? operation = null;
        OperationFilterContext context = new OperationFilterContext(
            new ApiDescription(),
            new Mock<ISchemaGenerator>().Object,
            new SchemaRepository(),
            typeof(object).GetMethod(nameof(object.ToString)));
        SwaggerDefaultValues subject = new SwaggerDefaultValues();

        // Act & Assert
        Should.Throw<ArgumentNullException>(() => subject.Apply(operation!, context)).ParamName.ShouldBe(nameof(operation));
    }

    [Fact]
    public void SwaggerDefaultValues_Apply_NullContext_Throws()
    {
        // Arrange
        OpenApiOperation operation = new OpenApiOperation();
        OperationFilterContext? context = null;
        SwaggerDefaultValues subject = new SwaggerDefaultValues();

        // Act & Assert
        Should.Throw<ArgumentNullException>(() => subject.Apply(operation, context!)).ParamName.ShouldBe(nameof(context));
    }

    [Fact]
    public void SwaggerDefaultValues_Apply_RemovesUnsupportedResponseType()
    {
        // Arrange
        ApiDescription apiDescription = new ApiDescription()
        {
            ActionDescriptor = new ActionDescriptor(),
        };
        apiDescription.SupportedResponseTypes.Add(new ApiResponseType() { Type = typeof(string), StatusCode = 200 });

        // string is only supported type
        apiDescription.SupportedResponseTypes[0].ApiResponseFormats.Add(new ApiResponseFormat() { MediaType = "string" });
        apiDescription.ParameterDescriptions.Add(new ApiParameterDescription()
        {
            Name = "Id",
            ModelMetadata = new ApiVersionModelMetadata(new Mock<IModelMetadataProvider>().Object, "description"),
            DefaultValue = string.Empty,
        });
        OpenApiOperation operation = new OpenApiOperation();
        operation.Responses.Add("200", new OpenApiResponse()
        {
            Content = new Dictionary<string, OpenApiMediaType>()
            {
                // Supported response type
                { "string",  new OpenApiMediaType() },

                // Unsuported response type which will be removed
                { "int",  new OpenApiMediaType() },
            },
        });
        operation.Parameters.Add(new OpenApiParameter() { Name = "Id", Schema = new OpenApiSchema() { Type = "string" } });
        OperationFilterContext context = new OperationFilterContext(
            apiDescription,
            new Mock<ISchemaGenerator>().Object,
            new SchemaRepository(),
            typeof(object).GetMethod(nameof(object.ToString)));
        SwaggerDefaultValues subject = new SwaggerDefaultValues();

        // Act
        subject.Apply(operation, context);

        // Assert
        operation.Responses.ShouldSatisfyAllConditions(
            responses => responses.Count.ShouldBe(1),
            responses => responses["200"].Content.TryGetValue("string", out _).ShouldBeTrue(),
            responses => responses["200"].Content.TryGetValue("int", out _).ShouldBeFalse());
    }

    #endregion Test Cases
}