#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS base

RUN echo "Removing non-essential libraries" \
   && apk --purge del \
   libc-utils \
   scanelf \
   apk-tools

WORKDIR /app
COPY svc/src/Platform.Catalog.Events/publish .

# Creates a non-root user with an explicit UID and adds permission to access the /app folder
RUN adduser -u 5678 --disabled-password --gecos "" appuser && chown -R appuser /app
USER 5678
EXPOSE 8080
ENTRYPOINT ["dotnet", "/app/Aveva.Platform.Catalog.Events.dll"]