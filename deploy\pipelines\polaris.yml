version: "1"
project:
  name: ${scm.git.repo}
  branch: ${scm.git.branch}
  revision:
    name: ${scm.git.commit}
    date: ${scm.git.commit.date}
capture:
  build:
    cleanCommands:
    - shell: [dotnet, clean, 'platform-catalog.sln', '/p:Configuration=ReleasePolaris', '/p:Platform=Any CPU']
    buildCommands:
      - shell: [dotnet, build, 'platform-catalog.sln', '/p:Configuration=ReleasePolaris', '/p:Platform=Any CPU']
    coverity:
      skipFiles: 
      - test
      - tools
      - samples
  fileSystem:
    javascript:
      files:
      - directory: ${project.projectDir}
      - excludeRegex: .*[.]json|^.*test.*$|^.*tools.*$|^.*samples.*$
analyze:
  mode: central
  coverity:
    caching:
      jars: disable
      tu-caching: disable
      rws: disable
      pfi: disable
      dlls: disable
install:
  coverity:
    version: default
serverUrl: https://osisoft.polaris.synopsys.com