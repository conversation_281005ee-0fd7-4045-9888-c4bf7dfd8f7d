import { OpsClient, ApiClient, checkApiResponse } from "@platform/performance-libs";
import { createClientCredentialClientWithRoleAssignment } from "@platform/performance-libs";
import { RootRequest, HttpVerb } from "@platform/performance-libs";
import { K6TestBase } from '@platform/performance-libs';
import { Api as ApiAccessMgmt } from "@platform/aveva.platform.accessmgmt.tsclient";
import { check, fail } from "k6";
import { Logger } from "@platform/performance-libs";
import { LogLevel } from "@platform/performance-libs";
import { Api as v2CatalogApiModels } from "@platform/aveva.platform.catalog.tsclient";
import { Ops as OpsIdentity } from "@platform/aveva.platform.identity.tsclient";
import { v2ApiGetServicesWithServiceID } from "../utils/common/servicesHelper";
import { getAssignmentId, assignIdpToAccount } from "../utils/common/authHelper";
import { SharedArray } from "k6/data";
import * as Papa from "papaparse";

export class apiGetAllServiceBase extends K6TestBase {
    opsClient: OpsClient;
    apiClient: ApiClient;
    logger: Logger;
    csvData: any;

    constructor() {
        super();
        this.opsClient = new OpsClient();
        this.apiClient = new ApiClient();

        //URL information for tests, constructs base url for both routes and token end point url for api route
        this.opsClient.config.baseUrl = `https://${__ENV.ENVPREFIX}.${__ENV.DNS}`;
        this.apiClient.config.baseUrl = `https://${__ENV.ENVPREFIX}.${__ENV.DNS}`;
        this.opsClient.config.opsTokenEndpoint = `https://login.microsoftonline.com/${__ENV.TENANTID}/oauth2/v2.0/token`;

        this.csvData = new SharedArray("PerfTestData", function () {
            // Load CSV file and parse it using Papa Parse
            //performance_setup.csv is generated from "PerformanceSetup" C# project and contains the seeding details
            //The below example is assuming the csv has data with headers accountName and accountGuid
            return Papa.parse(open(`../../performance_setup.csv`), {
                header: true,
                comments: "#"
            }).data;
        });

        //Using the first account guid received from performance_setup.csv file for api token url
        this.apiClient.config.apiTokenEndpoint = `https://identity.${__ENV.ENVPREFIX}.${__ENV.DNS}/account/${this.csvData[0].accountGuid}/authentication/connect/token`;

        this.logger = new Logger(
            LogLevel[__ENV.LOG_LEVEL as keyof typeof LogLevel]
        );
    }

    sharedTestSetup(): any {
        this.logger.info("Setup of PerformanceServicesTest");

        let clientDetailsObj: any;

        const dataItems: string[] = [];

        for (let i = 0; i < this.csvData.length; i++) {
            dataItems.push(this.csvData[i].accountName);
            dataItems.push(this.csvData[i].accountGuid);
        }

        this.logger.info(`Base URL is - ${this.opsClient.config.baseUrl}`);
        this.logger.info(`Ops Token generation URL is - ${this.opsClient.config.opsTokenEndpoint}`);
        this.logger.info(`Api Token generation URL is - ${this.apiClient.config.apiTokenEndpoint}`);

        clientDetailsObj = createClientCredentialClientWithRoleAssignment(dataItems[0], dataItems[1], "Account Administrator", this.opsClient, this.apiClient);
        this.logger.info(`Api Token generation URL is - ${this.apiClient.config.apiTokenEndpoint}`);

        var data = {
            accountName: dataItems[0],
            accountGuid: dataItems[1],
            clientCredentialClientId: clientDetailsObj.clientCredentialClientId,
            clientCredentialClientSecret: clientDetailsObj.clientCredentialClientSecret,
            accountAdminRoleGuid: clientDetailsObj.roleGuid,
            adminRoleAssignmentId: clientDetailsObj.roleAssignmentGuid,
            vuOpsBearerObject: this.opsClient.bearerToken,
            vuApiBearerObject: this.apiClient.bearerToken,
            apiClient: this.apiClient,
            opsClient: this.opsClient,
            logger: this.logger
        };

        getAssignmentId(data);
        assignIdpToAccount(data);

        //Return object dataholder for default scenario and tear down to use
        return data;
    }
    defaultScenarioTestIteration(data: any): void {
        const lifecycle: v2CatalogApiModels.Lifecycle = {
            trigger: "Account",
            instanceMode: "Shared",
        }
        var emptyArray: string[];
        const responseExpected: v2CatalogApiModels.ServiceResponse = {
            id: "catalog",
            displayName: "Catalog services",
            hostingType: "Environment",
            iconUrl: "https://cdn.aveva.design/icons/svg/outlined/32/content/content--library.svg",
            description: "Catalog services",
            lifecycle: lifecycle,
            tags: emptyArray,
        };

        //construct token end point url for api route
        this.apiClient.config.apiTokenEndpoint = `https://identity.${__ENV.ENVPREFIX}.${__ENV.DNS}/account/${data.accountGuid}/authentication/connect/token`;

        try {
            //It is mandatory for this object to be reassigned with the data that is manipulated inside the sharedTestSetup, otherwise the bearer token object shall be undefined and token will not be reused
            this.opsClient.bearerToken = data.vuOpsBearerObject;
            this.apiClient.bearerToken = data.vuApiBearerObject;

            const getResponse = v2ApiGetServicesWithServiceID(this.apiClient, data.accountGuid, "catalog");

            if (!getResponse) {
                fail(`Attempt to retrieve services failed`);
            }
            if (getResponse.id == "catalog") {
                check(getResponse, {
                        [`Catalog should have expected id`]: (c) => c.id === responseExpected.id,
                        [`Catalog should have expected displayName`]: (c) => c.displayName === responseExpected.displayName,
                        [`Catalog should have expected hostingType`]: (c) => c.hostingType === responseExpected.hostingType,
                        [`Catalog should have expected iconUrl`]: (c) => c.iconUrl === responseExpected.iconUrl,
                        [`Catalog should have expected description`]: (c) => c.description === responseExpected.description,
                        [`Catalog should have expected lifecycle trigger`]: (c) => c.lifecycle.trigger === responseExpected.lifecycle.trigger,
                        [`Catalog should have expected lifecycle instance mode`]: (c) => c.lifecycle.instanceMode === responseExpected.lifecycle.instanceMode,
                });
            }
        } catch (e: any) {
            this.logger.error(`There was some exception. ${e}`);
        }
    }

    sharedTestTeardown(_data: any): void {
        this.logger.info(`No necessary teardown for PerformanceServicesTest at ${new Date()}`);
    }

    private getAssignmentId(data: any): void {
        // ******** CHECK IF ROLE ASSIGNMENT CREATED AS PART OF PRESETUP IS AVAILABLE FROM GET CALL ********
        this.apiClient.config.apiClientId = data.clientCredentialClientId;
        this.apiClient.config.apiClientSecret = data.clientCredentialClientSecret;

        const request = new RootRequest(HttpVerb.GET, `/account/${data.accountGuid}/accessMgmt/v1/roles/${data.accountAdminRoleGuid}/assignments/${data.adminRoleAssignmentId}`);
        const response = this.apiClient.request(request);

        checkApiResponse(response, 200, request, "Get Role Assignment by Id for given Account");

        if (response.status !== 200) {
            this.logger.error(`Get Role Assignment by Id for given Account error: '${response.body}' (${response.status})`);
        }

        // Parse the response body as JSON and type it
        let roleAssignResponseBody: ApiAccessMgmt.RoleAssignmentResponse = JSON.parse(response.body);

        if (roleAssignResponseBody.id == data.adminRoleAssignmentId) {
            this.logger.info(`Successfully found the admin role assignment id - ${roleAssignResponseBody.id} in returned response`);
        } else {
            this.logger.error("Failed to find the admin role assignment id in returned response");
        }
    }

    private assignIdpToAccount(data: any): void {
        // ******** CHECK IF MSA IDENTITY PROVIDER IS ALREADY ADDED ********        
        this.logger.info("Checking and adding MSA IDP to account " + data.accountGuid);

        const request = new RootRequest(HttpVerb.GET, `/account/${data.accountGuid}/identity/v1/IdentityProviders`);
        const response = this.opsClient.request(request);

        checkApiResponse(response, 200, request, "Get IDP for account");

        // Parse the response body as JSON and type it        
        let providersResponse: OpsIdentity.IdentityProviderCollectionResponse = JSON.parse(response.body);

        let index = providersResponse.items.findIndex(item => item.name == "Microsoft Account");

        if (index == -1) {
            const addIdpBody: OpsIdentity.IdentityProviderCreateRequest = {
                name: "Microsoft Account",
                type: "a707f5d7-23cc-4e70-817a-a6a3cbcf4a26",
                emailValidationRequired: false
            };

            const request = new RootRequest(HttpVerb.POST, `/account/${data.accountGuid}/identity/v1/IdentityProviders`, addIdpBody);
            const response = this.opsClient.request(request);

            checkApiResponse(response, 201, request, "Add IDP to existing account");

            // Parse the response body as JSON and type it            
            let addIdpResponse: OpsIdentity.IdentityProviderResponse = JSON.parse(response.body);

            if (addIdpResponse.name == "Microsoft Account") {
                this.logger.info(`Successfully assigned IdP ${addIdpResponse.name}..`);
            } else {
                this.logger.info(`Unable to add IdP to account, exiting..`);
            }

        } else {
            this.logger.info("Idp already assigned to the account, skipping the same!");
        }
    }
}