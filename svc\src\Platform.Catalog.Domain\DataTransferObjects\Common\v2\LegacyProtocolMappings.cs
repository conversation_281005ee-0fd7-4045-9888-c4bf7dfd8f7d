﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2
{
    /// <summary>
    /// Defines the mapping between a service and corresponding legacy Solution and Capability Management (SCM) solution definition. This mapping enables backward compatibility with systems that use the older SCM protocol.
    /// </summary>
    public class LegacyProtocolMappings
    {
        /// <summary>
        /// The mapping of geographies to the legacy declared SCM solution regions. Keys represent geography IDs in the current system, and values represent the corresponding region identifiers in the legacy SCM system.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public Dictionary<string, string>? Geographies { get; init; } = new Dictionary<string, string>();
#pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// The mapping of the service dependencies to legacy SCM integration definitions. Keys represent dependency identifiers in the current system, and values contain the mapping details for connecting to the legacy SCM integration system.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public Dictionary<string, LegacyProtocolDependencyMapping>? Dependencies { get; init; } = new Dictionary<string, LegacyProtocolDependencyMapping>();
#pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// The mappings of applications to SCM capability definitions. Each entry defines how an application in the current system relates to a capability definition in the legacy SCM system.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public List<LegacyProtocolApplicationMapping>? Applications { get; init; } = new List<LegacyProtocolApplicationMapping>();
#pragma warning restore CA2227 // Collection properties should be read only

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is LegacyProtocolMappings item
                && ((item.Dependencies == null && Dependencies == null) || (item.Dependencies != null && Dependencies != null && item.Dependencies.SequenceEqual(Dependencies)))
                && ((item.Geographies == null && Geographies == null) || (item.Geographies != null && Geographies != null && item.Geographies.SequenceEqual(Geographies)))
                && ((item.Applications == null && Applications == null) || (item.Applications != null && Applications != null && item.Applications.SequenceEqual(Applications)));
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}