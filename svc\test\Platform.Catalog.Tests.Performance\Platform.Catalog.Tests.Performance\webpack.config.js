const path = require("path");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const CopyPlugin = require("copy-webpack-plugin");
// const GlobEntries = require('webpack-glob-entries');
const {
    MultipleBundlesPlugin,
    globEntries,
} = require("multiple-bundles-webpack-plugin");
const webpack = require("webpack");
const entries = {
    //...globEntries(["./dist/tests/fake_test/*.js"]),
    //...globEntries(["./dist/tests/GeoInstanceToDo/*.js"]),
    //...globEntries(["./dist/tests/AccountMgmt/*.js"]),
    //...globEntries(["./dist/tests/RoleAssignments/*.js"])
    ...globEntries(["./dist/tests/**/*.js"])
};

module.exports = {
    mode: "production",
    entry: entries,
    output: {
        path: path.join(__dirname, "dist/transformed"),
        libraryTarget: "commonjs",
        filename: "[name].js",
    },
    resolve: {
        extensions: [".ts", ".js"],
    },
    module: {
        rules: [
            {
                test: /\.ts$/,
                use: "babel-loader",
                exclude: /node_modules/,
            },
            {
                test: /\.ejs/,
                type: "asset/source",
            }
        ],
    },
    target: "web",
    externals: /^(k6|https?\:\/\/)(\/.*)?/,
    // Generate map files for compiled scripts
    devtool: "source-map",
    stats: {
        colors: true,
    },
    plugins: [
        new CleanWebpackPlugin(),
        // Copy assets to the destination folder
        // see `src/post-file-test.ts` for an test example using an asset
        new CopyPlugin({
            patterns: [
                {
                    from: path.resolve(__dirname, "assets"),
                    noErrorOnMissing: true,
                },
            ],
        }),
        new webpack.ProvidePlugin({
            // Automatically load modules instead of having to import or require them everywhere
            URLSearchParams: ['url', 'URLSearchParams'],
            fetch: ['whatwg-fetch', 'default']
        }),
    ],
    optimization: {
        // Don't minimize, as it's not used in the browser
        minimize: false,
        // runtimeChunk: 'single',
        // splitChunks: {
        //     chunks: 'all',
        // },
    },
};
