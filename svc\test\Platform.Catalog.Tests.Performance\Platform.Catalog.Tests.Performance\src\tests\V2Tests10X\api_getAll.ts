import { K6TestBase } from "@platform/performance-libs";
import { apiGetAllBase } from "../../common/apiV2_getAll";

export class apiGetAll10X extends K6TestBase {
    baseTestClass: apiGetAllBase;
    constructor() {
        super();
        this.baseTestClass = new apiGetAllBase();
    }

    defaultScenarioTestIteration(data: any): void {
        this.baseTestClass.defaultScenarioTestIteration(data);
    }

    sharedTestSetup(): any {
        return this.baseTestClass.sharedTestSetup();
    }

    sharedTestTeardown(data: any): void {
        this.baseTestClass.sharedTestTeardown(data);
    }
}
