﻿using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Watchers;
using k8s;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Aveva.Platform.Catalog.Tests.Unit.Watcher
{
    [Trait("Category", "App")]
    [Trait("Category", "Unit")]
    [Trait("Category", "App.Unit")]
    public class V1ServiceEntryWatcherTests
    {
        [Fact]
        public async Task V1ServiceEntryWatcher_Should_Run_ExecuteAsync()
        {
            // Arrange - Create the necessary mocks
            IServiceCollection services = new ServiceCollection();

            Mock<IKubernetes> mockK8s = new Mock<IKubernetes>();
            Mock<IServiceEntryRepository> mockRepo = new Mock<IServiceEntryRepository>();
            Mock<ILogger<V1ServiceEntryWatcher>> mockLogger = new Mock<ILogger<V1ServiceEntryWatcher>>();
            var metrics = new CatalogMetrics();
            Mock<V1ServiceEntryWatcher> mockWatcher = new Mock<V1ServiceEntryWatcher>(new object[] { mockK8s.Object, mockRepo.Object, mockLogger.Object, metrics });

            // Inject the mocked service into the service collection.
            services.AddSingleton<IHostedService>(mockWatcher.Object);

            var serviceProvider = services.BuildServiceProvider();
            var hostedService = serviceProvider.GetService<IHostedService>();

            // Act
            _ = hostedService!.StartAsync(CancellationToken.None);
            await Task.Delay(1000, cancellationToken: TestContext.Current.CancellationToken).ConfigureAwait(true);
            _ = hostedService.StopAsync(CancellationToken.None);

            // Assert
            var serviceEntryService = serviceProvider.GetService<IHostedService>() as V1ServiceEntryWatcher;
            Assert.NotNull(serviceEntryService!.ExecuteTask);
            Assert.True(serviceEntryService.ExecuteTask.IsCompleted);
        }
    }
}