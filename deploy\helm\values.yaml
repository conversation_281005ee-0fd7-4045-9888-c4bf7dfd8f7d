daprId:
  version: 1
  serviceId: catalog
  lifecycleTrigger: None
  
# -- Catalog Api service Configuration
api:
  runAsNonRoot: true
  port: 8080
  image:
    name: aveva-platform-catalog-api
    pullPolicy: IfNotPresent
  env:
    Instrumentation__DebugEnabled: "false"
  scaling:
    maxReplicas: 10
    cpuUtilizationPercentage: 50
    memoryUtilizationPercentage: 60
  livenessProbe:
    initialDelaySeconds: 5
    periodSeconds: 60
    timeoutSeconds: 10
  readinessProbe:
    initialDelaySeconds: 5
    periodSeconds: 60
    timeoutSeconds: 10
  startupProbe:
    initialDelaySeconds: 5
    timeoutSeconds: 10

# -- Catalog events service Configuration
events:
  runAsNonRoot: true
  port: 8080
  image:
    name: aveva-platform-catalog-events
    pullPolicy: IfNotPresent
  env:
    Instrumentation__DebugEnabled: "false"
  scaling:
    minReplicas: 1
    maxReplicas: 2
    cpuUtilizationPercentage: 50
    memoryUtilizationPercentage: 60
  livenessProbe:
    initialDelaySeconds: 5
    periodSeconds: 10
    timeoutSeconds: 10
  readinessProbe:
    initialDelaySeconds: 5
    periodSeconds: 30
    timeoutSeconds: 10
  startupProbe:
    initialDelaySeconds: 5
    timeoutSeconds: 10

# -- Catalog Operator service Configuration
operator:
  name: "catalog-operator"
  timeoutSeconds: 30 #Keeping it at 30 seconds to avoid failure in EDA pipeline CR deployment
  runAsNonRoot: true
  onConnectNamespace: "on-connect"
  dapr:
    sidecarCpuRequest: "100m"
    sidecarCpuLimit: "300m"
    sidecarMemoryRequest: "250Mi"
    sidecarMemoryLimit: "1000Mi"
  image:
    name: aveva-platform-catalog-operator
    pullPolicy: IfNotPresent
    tag: latest
  env:
    Instrumentation__DebugEnabled: "false"
  resources:
    limits:
      cpu: 100m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 64Mi

image:
  pullSecret:

buildVariables:
  tag: latest
