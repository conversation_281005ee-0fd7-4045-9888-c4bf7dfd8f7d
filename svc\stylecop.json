﻿{
  "$schema": "https://raw.githubusercontent.com/DotNetAnalyzers/StyleCopAnalyzers/master/StyleCop.Analyzers/StyleCop.Analyzers/Settings/stylecop.schema.json",
  "settings": {
    "orderingRules": {
      "usingDirectivesPlacement": "preserve"
    },
    "readabilityRules": {
      "allowBuiltInTypeAliases": true
    },
    "maintainabilityRules": {
      "topLevelTypes": [
        "class",
        "struct",
        "interface",
        "enum"
      ]
    },
    "namingRules": {
      "allowedHungarianPrefixes": [
        "op",
        "na",
        "eu",
        "af",
        "ai",
        "db",
        "dr",
        "ef",
        "ex",
        "id",
        "ip",
        "my",
        "ns",
        "ok",
        "pi"
      ]
    },
    "layoutRules": {
      "newlineAtEndOfFile": "omit"
    },
    "documentationRules": {
      "documentInternalElements": false
    }
  }
}