/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { createServiceResponseFromDiscriminatorValue, type ServiceResponse } from '../../../../../../../models/index.js';
// @ts-ignore
import { type BaseRequestBuilder, type Parsable, type ParsableFactory, type RequestConfiguration, type RequestInformation, type RequestsMetadata } from '@microsoft/kiota-abstractions';

/**
 * Builds and executes requests for operations under /api/account/{accountId}/catalog/v2/services/{id}
 */
export interface ServicesItemRequestBuilder extends BaseRequestBuilder<ServicesItemRequestBuilder> {
    /**
     * Gets a specific catalog service entry for an account by its unique `id`. This endpoint returns detailed information about a single service only if it is made available to your account.
     * @param requestConfiguration Configuration for the request such as headers, query parameters, and middleware options.
     * @returns {Promise<ServiceResponse>}
     */
     get(requestConfiguration?: RequestConfiguration<object> | undefined) : Promise<ServiceResponse | undefined>;
    /**
     * Gets a specific catalog service entry for an account by its unique `id`. This endpoint returns detailed information about a single service only if it is made available to your account.
     * @param requestConfiguration Configuration for the request such as headers, query parameters, and middleware options.
     * @returns {RequestInformation}
     */
     toGetRequestInformation(requestConfiguration?: RequestConfiguration<object> | undefined) : RequestInformation;
}
/**
 * Uri template for the request builder.
 */
export const ServicesItemRequestBuilderUriTemplate = "{+baseurl}/api/account/{accountId}/catalog/v2/services/{id}";
/**
 * Metadata for all the requests in the request builder.
 */
export const ServicesItemRequestBuilderRequestsMetadata: RequestsMetadata = {
    get: {
        uriTemplate: ServicesItemRequestBuilderUriTemplate,
        responseBodyContentType: "application/json",
        adapterMethodName: "send",
        responseBodyFactory:  createServiceResponseFromDiscriminatorValue,
    },
};
/* tslint:enable */
/* eslint-enable */
