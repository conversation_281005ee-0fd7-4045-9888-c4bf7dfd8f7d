﻿using Aveva.Platform.Authentication.Sdk.Server.Extensions;
using Aveva.Platform.Authorization.Sdk;
using Aveva.Platform.Authorization.Sdk.Domain.Models;
using Aveva.Platform.Catalog.Domain;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Domain.Serialization;
using Aveva.Platform.Catalog.Events.Publisher;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.Common.Abstractions.Extensions;
using Aveva.Platform.Common.Framework.Abstractions.Configuration;
using Aveva.Platform.Common.Framework.AspNetCore.Configuration;
using Aveva.Platform.Common.Framework.AspNetCore.Routing.Extensions;
using Aveva.Platform.Common.Framework.ExceptionHandling.Extensions;
using Aveva.Platform.Common.Monitoring.Instrumentation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Aveva.Platform.Catalog.Events.Configuration;

/// <summary>
/// Register services for the project.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="ServiceRegistrationModule"/> class.
/// </remarks>
/// <param name="configuration">The configuration.</param>
/// <exception cref="ArgumentNullException">configuration is <c>null</c>.</exception>
public sealed class ServiceRegistrationModule(IConfiguration configuration) : IServiceRegistrationModule
{
    #region Private Fields

    private readonly IConfiguration _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

    #endregion Private Fields

    #region Public Methods

    /// <inheritdoc/>
    public void AddServices(IServiceCollection services)
    {
        // Configure the application settings.
        services
            .AddOptions<ApplicationOptions>()
            .Bind(_configuration.GetSection(ApplicationOptions.DefaultConfigurationSectionName));

        // Define the default JSON serialization settings used by the API.
        services.ConfigureHttpJsonOptions(options => options.SerializerOptions.ConfigureDefaults(enumsAsStrings: true));

        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen();
        services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();
        services.AddLogging();

        // Enable API versioning.
        services
            .AddApiVersioning((Asp.Versioning.ApiVersioningOptions options) =>
            {
                options.ReportApiVersions = true;
            })
            .AddApiExplorer(options =>
            {
                options.GroupNameFormat = "'v'VVV";
                options.FormatGroupName = (group, version) => $"{group}";
                options.SubstituteApiVersionInUrl = true;
            });

        services.AddOtelConfiguration(_configuration);
        services.AddOpenTelemetry()
        .WithTracing(tracerProviderBuilder =>
        {
            // Add a custom Activity Source for traces
            tracerProviderBuilder.AddSource(CatalogTraceSource.EventsTraceName);
        })
        .WithMetrics(meterProviderBuilder =>
        {
            // Add a custom Meter for metrics
            meterProviderBuilder.AddMeter(EventMetrics.MeterName);
        });

        // Add health checks for the service and the Dapr side car.
        services
            .AddHealthChecks()
            .AddCheck("self", () => HealthCheckResult.Healthy())
            .AddDapr();

        // Add route handlers that implement minimal API endpoints.
        services.AddRouteHandlerServices();

        // Add exception handling services.
        services.AddExceptionHandlingServices();

        // Add formatted error response support (for reporting formatted error information back to API consumers).
        services.AddErrorResponseServices();

        // Add Identity service SDKs.
        services.AddPlatformAuthentication(options =>
        {
            _configuration.GetSection("Authentication").Bind(options);
        });

        var aspNetCoreEnvironment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

        if (string.Equals(aspNetCoreEnvironment, Environments.Development, StringComparison.OrdinalIgnoreCase))
        {
            services.AddPlatformAuthorization(ServiceId.Parse(CatalogConstants.ServiceId), authorizationOptions =>
            {
                authorizationOptions.EnableAuthorizationBypass = true;
            });
        }
        else
        {
            services.AddPlatformAuthorization(ServiceId.Parse(CatalogConstants.ServiceId), authorizationOptions =>
            {
                authorizationOptions.MemoryCacheEnabled = true;
                authorizationOptions.EnableImplicitAuthorization = true;
            });
        }

        services.AddAuthorization(options =>
        {
            options.FallbackPolicy = new AuthorizationPolicyBuilder()
                .RequireAuthenticatedUser()
                .Build();
        });

        services.AddDaprClient(builder => builder.UseJsonSerializationOptions(JsonSerializationOptions.Options));
        services.AddEventBus(options => options.SerializerOptions = JsonSerializationOptions.Options);
        services.AddTransient<IEventPublisherFactory, EventPublisherFactory>();
        services.AddSingleton<ICatalogClient, CatalogClient>();
    }

    #endregion Public Methods
}