﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.Tests.Deployment</AssemblyName>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>

    <OutputType>Library</OutputType>
    <UserSecretsId>catalog-deployment-tests-de4cbd07-94d3-48da-9354-9397f958450d</UserSecretsId>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
    <OutputType>exe</OutputType>
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="xunit.runner.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aveva.Platform.Common.Testing.Support" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Polly" />
    <PackageReference Include="Shouldly" />
    <PackageReference Include="xunit.v3" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\lib\Aveva.Platform.Library.Catalog\svc\src\Aveva.Platform.Catalog.Client\Platform.Library.Catalog.Client.csproj" />
    <ProjectReference Include="..\..\src\Platform.Catalog.Api\Platform.Catalog.Api.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="testsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
    <RunSettingsFilePath>$(MSBuildProjectDirectory)\local.runsettings</RunSettingsFilePath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
