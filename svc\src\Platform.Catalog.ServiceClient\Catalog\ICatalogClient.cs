﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;

namespace Aveva.Platform.Catalog.ServiceClient.Catalog
{
    /// <summary>
    /// Interface for Catalog API service client.
    /// </summary>
    public interface ICatalogClient
    {
        /// <summary>
        /// Gets all the ServiceEntries from Catalog memory cache.
        /// </summary>
        /// <returns>List of catalog entires.</returns>
        public Task<ServiceCollectionResponse?> GetAllAsync();
    }
}