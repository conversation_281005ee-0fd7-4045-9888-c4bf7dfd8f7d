﻿using System.Net;
using System.Net.Sockets;
using System.Text;
using k8s;
using k8s.Models;

namespace Aveva.Platform.Catalog.Tests.Integration.Kube.Common;

internal sealed class PortForwarder : IDisposable
{
    private readonly IKubernetes _client;
    private readonly V1Pod _pod;
    private readonly int _sourcePort;
    private readonly int _targetPort;

    private Socket? _listener;
    private Socket? _handler;
    private CancellationTokenSource? _cancellationTokenSource;

    public PortForwarder(IKubernetes client, V1Pod pod, int sourcePort, int targetPort)
    {
        _client = client;
        _pod = pod;
        _sourcePort = sourcePort;
        _targetPort = targetPort;
    }

    public async Task Forward()
    {
        var webSocket = await _client.WebSocketNamespacedPodPortForwardAsync(_pod.Metadata.Name, _pod.Metadata.NamespaceProperty, new int[] { _sourcePort }, "v4.channel.k8s.io").ConfigureAwait(false);
        _cancellationTokenSource = new CancellationTokenSource();
        _ = Task.Run(
            async () =>
        {
            using var demux = new StreamDemuxer(webSocket, StreamType.PortForward);
            demux.Start();

            var stream = demux.GetStream((byte?)0, (byte?)0);

            var ipAddress = IPAddress.Loopback;
            var localEndPoint = new IPEndPoint(ipAddress, _targetPort);
            _listener = new Socket(ipAddress.AddressFamily, SocketType.Stream, ProtocolType.Tcp);
            _listener.Bind(localEndPoint);
            _listener.Listen(100);

            var accept = Task.Run(() => AcceptAsync(stream, _cancellationTokenSource.Token), _cancellationTokenSource.Token);
            var copy = Task.Run(() => Copy(stream, _cancellationTokenSource.Token), _cancellationTokenSource.Token);
            await accept.ConfigureAwait(false);
            await copy.ConfigureAwait(false);
        },
            _cancellationTokenSource.Token);
    }

    public void Close()
    {
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();
        _handler?.Close();
        _listener?.Close();
        _handler?.Dispose();
        _listener?.Dispose();
    }

    public void Dispose()
    {
        Close();
    }

    private async Task AcceptAsync(Stream stream, CancellationToken cancellationToken)
    {
        while (true)
        {
            if (cancellationToken.IsCancellationRequested)
            {
                return;
            }

            if (_listener == null)
            {
                break;
            }

            _handler = await _listener.AcceptAsync(cancellationToken).ConfigureAwait(false);
            var buffer = new byte[4096];
            while (true)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    return;
                }

                int bytesRec = _handler.Receive(buffer);
                await stream.WriteAsync(buffer.AsMemory(0, bytesRec), cancellationToken).ConfigureAwait(false);
                if (bytesRec == 0 || Encoding.ASCII.GetString(buffer, 0, bytesRec).IndexOf("<EOF>", StringComparison.OrdinalIgnoreCase) > -1)
                {
                    break;
                }
            }
        }
    }

    private void Copy(Stream stream, CancellationToken cancellationToken)
    {
        var buffer = new byte[4096];
        while (true)
        {
            if (cancellationToken.IsCancellationRequested)
            {
                return;
            }

            var read = stream.Read(buffer, 0, 4096);
            try
            {
                _handler?.Send(buffer, read, 0);
            }
            catch
            {
            }
        }
    }
}