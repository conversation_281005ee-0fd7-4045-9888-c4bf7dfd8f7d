﻿using System.ComponentModel.DataAnnotations;

namespace Aveva.Platform.Catalog.Domain.Models
{
    /// <summary>
    /// V1Lifecycle.
    /// </summary>
    public class V1Lifecycle
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="V1Lifecycle"/> class.
        /// </summary>
#pragma warning disable CS8618 // Required for serialization
        public V1Lifecycle()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="V1Lifecycle"/> class.
        /// </summary>
        public V1Lifecycle(V1Trigger trigger, V1IntegrationProtocol? protocol, string providerId, V1InstanceMode? instanceMode, V1ProtocolOptions? protocolOptions, bool fulfillmentRequired)
        {
            Trigger = trigger;
            Protocol = protocol;
            ProviderId = providerId;
            InstanceMode = instanceMode;
            ProtocolOptions = protocolOptions;
            FulfillmentRequired = fulfillmentRequired;
        }

        /// <summary>
        /// Gets or sets trigger.
        /// </summary>
        [Required]
        public V1Trigger Trigger { get; set; }

        /// <summary>
        /// Gets or sets protocol.
        /// </summary>
        public V1IntegrationProtocol? Protocol { get; set; }

        /// <summary>
        /// Gets or sets provider id.
        /// </summary>
        public string? ProviderId { get; set; }

        /// <summary>
        /// Gets or sets instance mode.
        /// </summary>
        public V1InstanceMode? InstanceMode { get; set; }

        /// <summary>
        /// Gets or sets protocol options.
        /// </summary>
        public V1ProtocolOptions? ProtocolOptions { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether manual provisioning is required for this service.
        /// </summary>
        public bool FulfillmentRequired { get; set; }

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is V1Lifecycle item
                && string.Equals(item.Trigger.ToString(), Trigger.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.Protocol.ToString(), Protocol.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.ProviderId?.ToString(), ProviderId?.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.InstanceMode.ToString(), InstanceMode.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && ((item.ProtocolOptions == null && ProtocolOptions == null) || (item.ProtocolOptions != null && item.ProtocolOptions.Equals(ProtocolOptions)));
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}