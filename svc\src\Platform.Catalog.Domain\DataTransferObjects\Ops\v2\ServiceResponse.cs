﻿using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;

namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2
{
    /// <summary>
    /// Represents a catalog service entry with detailed information about the service and its capabilities.
    /// </summary>
    public class ServiceResponse
    {
        #region Public Properties

        /// <summary>
        /// The unique identifier for this service. This ID is used to reference the service in other operations.
        /// </summary>
        public string? Id { get; init; }

        /// <summary>
        /// The user-friendly name of the service displayed in user interfaces. This is the primary label used to identify the service to end users.
        /// </summary>
        public string? DisplayName { get; init; }

        /// <summary>
        /// A URL pointing to the service's icon image stored in the AVEVA CDN. When using this URL, implement proper cache control to ensure users see updated icons when they change. Browsers should be configured to periodically check for updates rather than caching indefinitely.
        /// </summary>
        public Uri? IconUrl { get; init; }

        /// <summary>
        /// A detailed description of the service explaining its purpose, capabilities, and key features. This text helps users understand what the service does and when to use it.
        /// </summary>
        public string? Description { get; init; }

        /// <summary>
        /// Other services that this service depends on to function properly. Keys represent the dependency identifier, and values contain details about the dependency relationship including cardinality and configuration requirements.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public Dictionary<string, Dependency>? Dependencies { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only

        /// <summary>
        /// Applications associated with this service. Each application represents a discrete component or interface that provides specific functionality within the service.
        /// </summary>
        public List<Application>? Applications { get; init; }

        /// <summary>
        /// Geographic regions where this service can be provisioned. Only applicable and required for services with `External` hosting type. Each geography represents a distinct region where the service can be deployed and accessed. For non-external hosting types, this collection should be empty.
        /// </summary>
        public List<Geography>? Geographies { get; init; }

        /// <summary>
        /// The classification category for this service. Categories help organize services into logical groups based on their purpose or domain.
        /// </summary>
        public Category? Category { get; init; }

        /// <summary>
        /// The deployment scope of the service. Valid values include:
        /// `Environment` (available in all regions and geographies),
        /// `Geography` (available in all regions within a specific geography),
        /// `Regional` (available in a specific region within a specific geography),
        /// `External` (hosted outside the platform but accessible through platform integration).
        /// </summary>
        public string? HostingType { get; init; }

        /// <summary>
        /// Terms used to categorize and filter the service. Tags provide additional classification beyond the primary category and help users discover related services.
        /// </summary>
        public List<string>? Tags { get; init; }

        /// <summary>
        /// Configuration that determines how service instances are created, managed, and terminated. This includes information about provisioning approach, resource allocation, and integration patterns.
        /// </summary>
        public Lifecycle? Lifecycle { get; init; }

        /// <summary>
        /// Provisioning constraints and availability information for this service. This includes details about service visibility in the catalog and provisioning limits. The `Enabled` property indicates whether the service is visible in catalog listings, and the `Limit` property specifies the maximum number of instances an account can have (with default value 10 when null).
        /// </summary>
        public ServiceAvailability? Availability { get; set; }

        /// <summary>
        /// External identity providers associated with this service. These identities enable integration with third-party authentication and authorization systems.
        /// </summary>
        public List<ExternalIdentity>? ExternalIdentities { get; init; }

        #endregion Public Properties

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj is not ServiceResponse item)
            {
                return false;
            }

            var result =
                string.Equals(item.Id, Id, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.IconUrl?.ToString(), IconUrl?.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.Description, Description, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.DisplayName, DisplayName, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.HostingType, HostingType, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.Category.ToString(), Category.ToString(), StringComparison.InvariantCultureIgnoreCase)
                && ((item.Availability == null && Availability == null) || (item.Availability != null && item.Availability.Equals(Availability)))
                && ((item.Lifecycle == null && Lifecycle == null) || (item.Lifecycle != null && item.Lifecycle.Equals(Lifecycle)));

            if (Dependencies == null && item.Dependencies != null)
            {
                if (item.Dependencies.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Dependencies != null && item.Dependencies == null)
            {
                if (Dependencies.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Dependencies != null && item.Dependencies != null)
            {
                result = result && (Dependencies.Count == item.Dependencies.Count);
                var depExcept = Dependencies.Except(item.Dependencies);
                result = result && (depExcept.Any() == false);
                result = result && (item.Dependencies.Except(Dependencies).Any() == false);
                result = result && item.Dependencies.Aggregate(result, (current, dependency) => current && Dependencies[dependency.Key].Equals(dependency.Value));
            }

            if (Applications == null && item.Applications != null)
            {
                if (item.Applications.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Applications != null && item.Applications == null)
            {
                if (Applications.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Applications != null && item.Applications != null)
            {
                result = result
                         && (Applications.Count == item.Applications.Count)
                         && !(from application in Applications
                              let match = item.Applications.Find(x => x.Name!.Equals(application.Name, StringComparison.Ordinal))
                              where !application.Equals(match)
                              select application).Any();
            }

            if (Geographies == null && item.Geographies != null)
            {
                if (item.Geographies.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Geographies != null && item.Geographies == null)
            {
                if (Geographies.Count > 0)
                {
                    result = result && false;
                }
            }

            if (Geographies != null && item.Geographies != null)
            {
                result = result
                         && (Geographies.Count == item.Geographies.Count)
                         && !(from geography in Geographies
                              let match = item.Geographies.Find(x => x.Id!.Equals(geography.Id, StringComparison.Ordinal))
                              where !geography.Equals(match)
                              select geography).Any();
            }

            if (ExternalIdentities == null && item.ExternalIdentities != null)
            {
                if (item.ExternalIdentities.Count > 0)
                {
                    result = result && false;
                }
            }

            if (ExternalIdentities != null && item.ExternalIdentities == null)
            {
                if (ExternalIdentities.Count > 0)
                {
                    result = result && false;
                }
            }

            if (ExternalIdentities != null && item.ExternalIdentities != null)
            {
                result = result
                         && (ExternalIdentities.Count == item.ExternalIdentities.Count)
                         && !(from externalIdentity in ExternalIdentities
                              let match = item.ExternalIdentities.Find(x => x.Id!.Equals(externalIdentity.Id, StringComparison.Ordinal))
                              where !externalIdentity.Equals(match)
                              select externalIdentity).Any();
            }

            return result;
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return Id!.GetHashCode(StringComparison.InvariantCulture);
        }
    }
}