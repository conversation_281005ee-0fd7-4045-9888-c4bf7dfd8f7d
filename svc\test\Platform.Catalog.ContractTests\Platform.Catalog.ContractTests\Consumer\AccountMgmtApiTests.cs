﻿using System.Net;
using Aveva.Platform.AccountMgmt.Client.Ops;
using Aveva.Platform.AccountMgmt.Client.Ops.Models;
using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.ServiceClient.AccountMgmt;
using Aveva.Platform.Common.Testing.ApiContracts.Helper;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.ContractTests.Consumer;

public class AccountMgmtApiTests : IClassFixture<AccountMgmtApiTestFixture>
{
    private readonly AccountMgmtApiTestFixture _fixture;

    public AccountMgmtApiTests(AccountMgmtApiTestFixture fixture, ITestOutputHelper output)
    {
        ArgumentNullException.ThrowIfNull(fixture);

        _fixture = (AccountMgmtApiTestFixture)fixture.SetupPact(output);
    }

    [Fact]
    [Trait("Tag", "Pactflow")]
    public async Task QueryGeographiesAsync_Success()
    {
        var expected = new GeographyResponse
        {
            Id = "geo-1",
            Primary = true,
            PrimaryRegionId = "region-1",
            Regions =
            [
                new Region
                {
                    Id = "region-1",
                    Name = "region-1",
                    Primary = true,
                },
            ],
        };

        var path = $"/ops/v1/geographies";
        _fixture.PactBuilder
            .UponReceiving("A request to list geographies")
            .WithRequest(HttpMethod.Get, path)
            .WithQuery("includeExtensions", PactNet.Matchers.Match.Type(true))
            .WillRespond()
            .WithStatus(HttpStatusCode.OK)
            .WithJsonBody(new[]
            {
                new
                {
                    id = PactNet.Matchers.Match.Type("geo-1"),
                    primary = PactNet.Matchers.Match.Type(true),
                    primaryRegionId = PactNet.Matchers.Match.Type("region-1"),
                    regions = new[]
                    {
                        new
                        {
                            id = PactNet.Matchers.Match.Type("region-1"),
                            name = PactNet.Matchers.Match.Type("region-1"),
                            primary = PactNet.Matchers.Match.Type(true),
                        },
                    },
                },
            });

        await _fixture.PactBuilder.VerifyAsync(async ctx =>
        {
            using var wrapper = new ContractTestKiotaClientWrapper<Client>(ctx.MockServerUri, path);
            var kiotaClient = wrapper.Client()!;
            var client = new AccountMgmtClient(kiotaClient);

            var actual = await client.QueryGeographiesAsync().ConfigureAwait(false);

            actual.ShouldNotBeNull();
            actual.First().Id.ShouldBe(expected.Id);
            actual.First().Primary.ShouldBe(expected.Primary);
            actual.First().PrimaryRegionId.ShouldBe(expected.PrimaryRegionId);
            actual.First().Regions.ShouldBeEquivalentTo(expected.Regions);
        });
    }

    [Fact]
    [Trait("Tag", "Pactflow")]
    public async Task QueryAvailabilityAsync_Success()
    {
        var expected = new V1ServiceAvailability
        {
            Limit = 5,
        };
        var serviceId = "service-1";
        var accountId = Guid.NewGuid().ToString();

        var path = $"/ops/v1/accounts/{accountId}/serviceOverrides";
        _fixture.PactBuilder
            .UponReceiving("A request to get service overrides")
            .WithRequest(HttpMethod.Get, path)
            .WillRespond()
            .WithStatus(HttpStatusCode.OK)
            .WithJsonBody(
                new Dictionary<string, object>
            {
                { serviceId, new { limit = expected.Limit } },
            });

        await _fixture.PactBuilder.VerifyAsync(async ctx =>
        {
            using var wrapper = new ContractTestKiotaClientWrapper<Client>(ctx.MockServerUri, path);
            var kiotaClient = wrapper.Client()!;
            var client = new AccountMgmtClient(kiotaClient);

            var actual = await client.QueryAvailabilityAsync(accountId).ConfigureAwait(false);

            actual.ShouldNotBeNull();
            actual.First().Key.ShouldBe(serviceId);
            var actualValue = actual.First().Value;
            actualValue.Limit.ShouldBe(expected.Limit);
        });
    }
}