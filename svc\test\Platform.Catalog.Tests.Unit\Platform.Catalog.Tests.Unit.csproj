<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.Tests.Unit</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.Tests.Unit</RootNamespace>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
    <OutputType>exe</OutputType>
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Aveva.Platform.Common.Testing" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Shouldly" />
    <PackageReference Include="xunit.v3" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\Platform.Catalog\Platform.Catalog.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>