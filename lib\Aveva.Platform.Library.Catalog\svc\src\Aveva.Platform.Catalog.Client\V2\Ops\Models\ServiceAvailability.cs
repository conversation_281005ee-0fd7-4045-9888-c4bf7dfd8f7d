// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Ops.Models
{
    /// <summary>
    /// Defines provisioning availability and constraints for a service. This resource contains information about how a service can be discovered and provisioned to an account.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class ServiceAvailability : IParsable
    {
        /// <summary>Indicates whether the service is visible in the service catalog (`true`) or hidden (`false`).            When set to `true`, the service appears in catalog listings and can be discovered by users.            When set to `false`, the service is hidden from catalog views but may still be available through direct access.</summary>
        public bool? Enabled { get; set; }
        /// <summary>Specifies the maximum number of instances an account can have of this service.            A value of `null` indicates the default limit (10) is applied.            A value of `0` indicates the service cannot be provisioned.            Any positive integer represents the maximum number of instances allowed.</summary>
        public int? Limit { get; set; }
        /// <summary>Indicates whether an approval workflow is required (`true`) or if the service can be provisioned immediately (`false`).            When set to `true`, service provisioning must go through a request and approval process.            When set to `false`, accounts can provision the service immediately without approval.</summary>
        [Obsolete("")]
        public bool? Visible { get; set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceAvailability"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceAvailability CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::Aveva.Platform.Catalog.Client.V2.Ops.Models.ServiceAvailability();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "enabled", n => { Enabled = n.GetBoolValue(); } },
                { "limit", n => { Limit = n.GetIntValue(); } },
                { "visible", n => { Visible = n.GetBoolValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteBoolValue("enabled", Enabled);
            writer.WriteIntValue("limit", Limit);
            writer.WriteBoolValue("visible", Visible);
        }
    }
}
#pragma warning restore CS0618
