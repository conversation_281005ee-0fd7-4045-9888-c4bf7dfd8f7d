﻿using AutoMapper;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.Models;

namespace Aveva.Platform.Catalog.Domain.Mapping
{
    internal class ProtocolOptionsResolver : IValueResolver<V1Lifecycle, Lifecycle, ProtocolOptions?>
    {
        public ProtocolOptions? Resolve(V1Lifecycle source, Lifecycle destination, ProtocolOptions? destMember, ResolutionContext context)
        {
            if (source.ProtocolOptions == null)
            {
                return null;
            }

            return source.Protocol switch
            {
                V1IntegrationProtocol.Webhook => new WebhookProtocolOptions { WebhookUri = source.ProtocolOptions.WebhookUri },
                V1IntegrationProtocol.Legacy => new LegacyProtocolOptions
                {
                    SolutionDefinition = source.ProtocolOptions.SolutionDefinition,
                    Mappings = source.ProtocolOptions.Mappings != null ? context.Mapper.Map<LegacyProtocolMappings>(source.ProtocolOptions.Mappings) : null,
                },
                _ => null,
            };
        }
    }
}