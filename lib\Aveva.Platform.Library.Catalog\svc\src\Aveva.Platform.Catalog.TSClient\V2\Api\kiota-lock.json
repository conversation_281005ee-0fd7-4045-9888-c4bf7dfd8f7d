{"descriptionHash": "DB541956E7DB5B7F19ACA295A1AB8B0E9433F459FEF969A0A49094B8B4F9CFD3F8B923A47795E35E2263A6BA92CA950DBCEBA00BBB736DE0722B7E0EAC171ED1", "descriptionLocation": "../../../../../../../svc/src/Platform.Catalog/bin/Debug/net8.0/platform-catalog-public-api-v2-swagger.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.24.3", "clientClassName": "Client", "typeAccessModifier": "Public", "clientNamespaceName": "Aveva.Platform.Catalog.Client.V2.Api", "language": "TypeScript", "usesBackingStore": false, "excludeBackwardCompatible": true, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory", "Microsoft.Kiota.Serialization.Text.TextSerializationWriterFactory", "Microsoft.Kiota.Serialization.Form.FormSerializationWriterFactory", "Microsoft.Kiota.Serialization.Multipart.MultipartSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory", "Microsoft.Kiota.Serialization.Text.TextParseNodeFactory", "Microsoft.Kiota.Serialization.Form.FormParseNodeFactory"], "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}