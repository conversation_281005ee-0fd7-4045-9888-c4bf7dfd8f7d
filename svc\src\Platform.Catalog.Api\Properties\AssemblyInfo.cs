﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// Global attributes
[assembly: AssemblyTrademark("AVEVA")]

// Setting ComVisible to false makes the types in this assembly not visible to COM components. If you need to access a type in this assembly from COM, set the ComVisible
// attribute to true on that type.
[assembly: ComVisible(false)]
[assembly: CLSCompliant(false)]

// Internals can be accessed by tests and proxies.
[assembly: InternalsVisibleTo("Aveva.Platform.Catalog")]
[assembly: InternalsVisibleTo("Aveva.Platform.Catalog.Tests.Unit")]
[assembly: InternalsVisibleTo("Aveva.Platform.Catalog.Api.Tests.Unit")]
[assembly: InternalsVisibleTo("Aveva.Platform.Catalog.Api.Tests.Integration")]
[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2")]

// ***** (This file trailer is a workaround for an issue with whitespace being added at end of file by VS Code Cleanup. Feel free to delete this line.)