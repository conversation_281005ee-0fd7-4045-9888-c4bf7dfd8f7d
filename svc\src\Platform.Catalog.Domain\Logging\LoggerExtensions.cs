﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Logging;

namespace Aveva.Platform.Catalog.Domain.Logging;

[ExcludeFromCodeCoverage(Justification = "LoggerMessage logging pattern implementation")]
internal static partial class LoggerExtensions
{
    #region Internal Methods

    [LoggerMessage((int)LoggerEvents.CreateCatalogFailedValidation, LogLevel.Debug, "<PERSON><PERSON> failed due to invalid data. Validation errors: '{ValidationErrors}'")]
    internal static partial void CreateCatalogFailedValidation(this ILogger logger, string validationErrors);

    [LoggerMessage((int)LoggerEvents.CreateOrUpdateCatalogFailedValidation, LogLevel.Debug, "Create or update failed due to invalid data. Validation errors: '{ValidationErrors}'")]
    internal static partial void CreateOrUpdateCatalogFailedValidation(this ILogger logger, string validationErrors);

    [LoggerMessage((int)LoggerEvents.DeleteExistingCatalogFailed, LogLevel.Information, "Delete of an existing item failed. This could indicate a concurrency conflict. Task Id: '{CatalogId}'")]
    internal static partial void DeleteExistingCatalogFailed(this ILogger logger, string CatalogId);

    #endregion Internal Methods
}