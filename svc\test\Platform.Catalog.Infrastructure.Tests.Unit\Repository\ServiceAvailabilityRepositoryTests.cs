﻿using Aveva.Platform.Catalog.Domain.Models;
using Aveva.Platform.Catalog.Infrastructure.Repository;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Infrastructure.Tests.Unit.Repository;

/// <summary>
/// ServiceAvailabilityRepositoryTests.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Unit")]
[Trait("Category", "Infrastructure")]
[Trait("Category", "Infrastructure.Unit")]
[Trait("Tag", "Repository")]
public class ServiceAvailabilityRepositoryTests
{
    private readonly ServiceAvailabilityRepository _repository;

    public ServiceAvailabilityRepositoryTests()
    {
        _repository = new ServiceAvailabilityRepository();
    }

    #region Test Cases
    [Fact]
    public void ServiceAvailabilityRepository_GetForEntryAsync_WithEmptyAccountOverrides_GetsDefaultValues()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
        };

        // Act
        var response = _repository.GetForEntry(newEntry, new Dictionary<string, V1ServiceAvailability>());

        // Assert
        response.ShouldNotBeNull();
        response.Enabled.ShouldBe(true);
        response.Limit.ShouldBe(10);
    }

    [Fact]
    public void ServiceAvailabilityRepository_GetForEntryAsync_WithEmptyAccountOverrides_GetsServiceDefaultValues()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Availability = new V1ServiceAvailability()
            {
                Enabled = true,
                Limit = 0,
            },
        };

        // Act
        var response = _repository.GetForEntry(newEntry, new Dictionary<string, V1ServiceAvailability>());

        // Assert
        response.ShouldNotBeNull();
        response.Enabled.ShouldBe(true);
        response.Limit.ShouldBe(0);
    }

    [Fact]
    public void ServiceAvailabilityRepository_GetForEntryAsync_WithEmptyAccountOverrides_GetsAccountScopedValues()
    {
        // Arrange
        V1ServiceEntry newEntry = new V1ServiceEntry()
        {
            Id = "1",
            DisplayName = "newEntry",
            HostingType = V1HostingType.Environment,
            Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            Availability = new V1ServiceAvailability()
            {
                Enabled = false,
                Limit = 10,
            },
        };

        // Act
        var response = _repository.GetForEntry(newEntry, new Dictionary<string, V1ServiceAvailability>
                {
                    { newEntry.Id, new V1ServiceAvailability() { Enabled = true, Limit = 5 } },
                });

        // Assert
        response.ShouldNotBeNull();
        response.Enabled.ShouldBe(true);
        response.Limit.ShouldBe(5);
    }

    [Fact]
    public void ServiceAvailabilityRepository_GetForEntriesAsync_WithEmptyAccountOverrides_GetsDefaultValues()
    {
        // Arrange
        IEnumerable<V1ServiceEntry> newEntry = [
            new V1ServiceEntry()
            {
                Id = "1",
                DisplayName = "newEntry",
                HostingType = V1HostingType.Environment,
                Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            },
            new V1ServiceEntry()
            {
                Id = "2",
                DisplayName = "newEntry2",
                HostingType = V1HostingType.Environment,
                Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
            }
        ];

        // Act
        var response = _repository.GetForEntries(newEntry, new Dictionary<string, V1ServiceAvailability>());

        // Assert
        response.ShouldNotBeNull();
        response.Count.ShouldBe(2);
        response.TryGetValue(newEntry.ElementAt(0), out var entry1).ShouldBe(true);
        response.TryGetValue(newEntry.ElementAt(1), out var entry2).ShouldBe(true);
        entry1?.Enabled.ShouldBe(true);
        entry1?.Limit.ShouldBe(10);
        entry2?.Enabled.ShouldBe(true);
        entry2?.Limit.ShouldBe(10);
    }

    [Fact]
    public void ServiceAvailabilityRepository_GetForEntriesAsync_WithEmptyAccountOverrides_GetsServiceDefaultValues()
    {
        // Arrange
        IEnumerable<V1ServiceEntry> newEntry = [
            new V1ServiceEntry()
            {
                Id = "1",
                DisplayName = "newEntry",
                HostingType = V1HostingType.Environment,
                Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
                Availability = new V1ServiceAvailability()
                {
                    Enabled = false,
                    Limit = 0,
                },
            },
            new V1ServiceEntry()
            {
                Id = "2",
                DisplayName = "newEntry2",
                HostingType = V1HostingType.Environment,
                Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
                Availability = new V1ServiceAvailability()
                {
                    Enabled = false,
                    Limit = 5,
                },
            }
        ];

        // Act
        var response = _repository.GetForEntries(newEntry, new Dictionary<string, V1ServiceAvailability>());

        // Assert
        response.ShouldNotBeNull();
        response.Count.ShouldBe(2);
        response.TryGetValue(newEntry.ElementAt(0), out var entry1).ShouldBe(true);
        response.TryGetValue(newEntry.ElementAt(1), out var entry2).ShouldBe(true);
        entry1?.Enabled.ShouldBe(false);
        entry1?.Limit.ShouldBe(0);
        entry2?.Enabled.ShouldBe(false);
        entry2?.Limit.ShouldBe(5);
    }

    [Fact]
    public void ServiceAvailabilityRepository_GetForEntriesAsync_WithAccountId_GetsAccountScopedValues()
    {
        // Arrange
        IEnumerable<V1ServiceEntry> newEntry = [
            new V1ServiceEntry()
            {
                Id = "1",
                DisplayName = "newEntry",
                HostingType = V1HostingType.Environment,
                Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
                Availability = new V1ServiceAvailability()
                {
                    Enabled = false,
                    Limit = 0,
                },
            },
            new V1ServiceEntry()
            {
                Id = "2",
                DisplayName = "newEntry2",
                HostingType = V1HostingType.Environment,
                Lifecycle = new V1Lifecycle(V1Trigger.Catalog, V1IntegrationProtocol.IntegrationEvent, "provider", V1InstanceMode.Shared, null, false),
                Availability = new V1ServiceAvailability()
                {
                    Enabled = false,
                    Limit = 5,
                },
            }
        ];

        // Act
        var response = _repository.GetForEntries(newEntry, new Dictionary<string, V1ServiceAvailability>
                {
                    { newEntry.ElementAt(0).Id, new V1ServiceAvailability() { Enabled = true, Limit = 5 } },
                    { newEntry.ElementAt(1).Id, new V1ServiceAvailability() { Enabled = false, Limit = 15 } },
                });

        // Assert
        response.ShouldNotBeNull();
        response.Count.ShouldBe(2);
        response.TryGetValue(newEntry.ElementAt(0), out var entry1).ShouldBe(true);
        response.TryGetValue(newEntry.ElementAt(1), out var entry2).ShouldBe(true);
        entry1?.Enabled.ShouldBe(true);
        entry1?.Limit.ShouldBe(5);
        entry2?.Enabled.ShouldBe(false);
        entry2?.Limit.ShouldBe(15);
    }

    #endregion Test Cases
}