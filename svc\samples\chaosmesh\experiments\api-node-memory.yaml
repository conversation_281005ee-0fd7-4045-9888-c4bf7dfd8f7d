apiVersion: chaos-mesh.org/v1alpha1
kind: StressChaos
metadata:
  name: api-stress-memory-experiment
  namespace: chaos-mesh
spec:
  mode: all                          # Apply to all matching pods
  selector:
    namespaces: 
      - platform-catalog             # Target namespace
    labelSelectors:
      pod-selector: catalog-api      # Label to identify specific pods
  stressors:
    memory:
      workers: 8                     # Number of memory workers
      size: '90%'                    # Target memory usage (90%)
  duration: "2m"                    # Total duration of the experiment (25 seconds)

# Key parameters for the StressChaos experiment:
# - mode: Determines whether to target all pods or a specific set.
# - selector: Filters the pods based on namespace and labels.
# - stressors: Specifies memory stress configurations.
# - duration: The total time the stress experiment will run.
