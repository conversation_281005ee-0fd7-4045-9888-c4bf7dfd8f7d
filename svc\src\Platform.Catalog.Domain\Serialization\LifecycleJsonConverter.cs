﻿using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;

namespace Aveva.Platform.Catalog.Domain.Serialization
{
    /// <summary>
    /// JSON converter for handling serialization and deserialization of <see cref="Lifecycle"/> objects.
    /// </summary>
    public class LifecycleJsonConverter : JsonConverter<Lifecycle>
    {
        // Map protocol names to types
        private static readonly Dictionary<string, Type> ProtocolTypeMap = new(StringComparer.OrdinalIgnoreCase)
            {
                { "LEGACY", typeof(LegacyProtocolOptions) },
                { "WEBHOOK", typeof(WebhookProtocolOptions) },
            };

        // Cache public writable properties for performance
        private static readonly PropertyInfo[] LifecycleProperties = typeof(Lifecycle)
            .GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .Where(p => p.CanWrite && p.SetMethod?.IsPublic == true)
            .ToArray();

        /// <summary>
        /// Reads and converts the JSON to a <see cref="Lifecycle"/> object.
        /// </summary>
        /// <param name="reader">The reader to read the JSON from.</param>
        /// <param name="typeToConvert">The type of object to convert.</param>
        /// <param name="options">Options to control the conversion behavior.</param>
        /// <returns>The deserialized <see cref="Lifecycle"/> object.</returns>
        /// <exception cref="JsonException">Thrown when deserialization fails.</exception>
        public override Lifecycle? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            using var doc = JsonDocument.ParseValue(ref reader);
            var rootProperties = doc.RootElement.EnumerateObject()
                .ToDictionary(p => p.Name, p => p.Value, StringComparer.OrdinalIgnoreCase);

            var lifecycle = new Lifecycle();

            // Deserialize Protocol first (needed for ProtocolOptions)
            if (rootProperties.TryGetValue(nameof(Lifecycle.Protocol), out var protocolElement))
            {
                lifecycle.Protocol = protocolElement.GetString();
            }

            foreach (var prop in LifecycleProperties)
            {
                if (string.Equals(prop.Name, nameof(Lifecycle.ProtocolOptions), StringComparison.Ordinal))
                {
                    if (rootProperties.TryGetValue(nameof(Lifecycle.ProtocolOptions), out var protocolOptionsElement) &&
                        !string.IsNullOrEmpty(lifecycle.Protocol) &&
                        ProtocolTypeMap.TryGetValue(lifecycle.Protocol, out var protocolOptionsType))
                    {
                        lifecycle.ProtocolOptions = (ProtocolOptions?)JsonSerializer.Deserialize(
                            protocolOptionsElement.GetRawText(),
                            protocolOptionsType,
                            options);
                    }
                }
                else if (rootProperties.TryGetValue(prop.Name, out var element))
                {
                    var value = JsonSerializer.Deserialize(element.GetRawText(), prop.PropertyType, options);
                    prop.SetValue(lifecycle, value);
                }
            }

            return lifecycle;
        }

        /// <summary>
        /// Writes a <see cref="Lifecycle"/> object as JSON.
        /// </summary>
        /// <param name="writer">The writer to write the JSON to.</param>
        /// <param name="value">The <see cref="Lifecycle"/> object to serialize.</param>
        /// <param name="options">Options to control the serialization behavior.</param>
        /// <exception cref="ArgumentNullException">Thrown when any argument is null.</exception>
        public override void Write(Utf8JsonWriter writer, Lifecycle value, JsonSerializerOptions options)
        {
            ArgumentNullException.ThrowIfNull(writer);
            ArgumentNullException.ThrowIfNull(value);
            ArgumentNullException.ThrowIfNull(options);

            writer.WriteStartObject();

            foreach (var prop in LifecycleProperties)
            {
                var propValue = prop.GetValue(value);

                if (propValue != null)
                {
                    var propertyName = options.PropertyNamingPolicy?.ConvertName(prop.Name) ?? prop.Name;

                    if (string.Equals(prop.Name, nameof(Lifecycle.ProtocolOptions), StringComparison.Ordinal))
                    {
                        if (!string.IsNullOrEmpty(value.Protocol))
                        {
                            writer.WritePropertyName(propertyName);

                            if (ProtocolTypeMap.TryGetValue(value.Protocol, out var protocolOptionsType))
                            {
                                JsonSerializer.Serialize(writer, value.ProtocolOptions, protocolOptionsType, options);
                            }
                        }
                    }
                    else
                    {
                        writer.WritePropertyName(propertyName);
                        JsonSerializer.Serialize(writer, propValue, options);
                    }
                }
            }

            writer.WriteEndObject();
        }
    }
}