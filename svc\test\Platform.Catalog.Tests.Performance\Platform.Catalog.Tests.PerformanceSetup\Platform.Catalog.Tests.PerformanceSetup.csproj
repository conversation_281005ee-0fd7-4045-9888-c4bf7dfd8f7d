﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.Tests.PerformanceSetup</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.Tests.PerformanceSetup</RootNamespace>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
    <OutputType>exe</OutputType>
    <StartupObject>Aveva.Platform.Catalog.Tests.PerformanceSetup.PerformanceSetupFlow</StartupObject>
  </PropertyGroup>

  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UserSecretsId>f38fdf19-679d-4cf9-910d-0ca6e90899ea</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aveva.Platform.AccountMgmt.Client" />
    <PackageReference Include="Aveva.Platform.Common.Testing.Support" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
    <PackageReference Include="System.Text.Json" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>
