﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <!-- Assembly properties -->
  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.Client</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.Client</RootNamespace>
    <Configurations>Debug;Release;DebugContainers;DebugNoCheck;ReleasePolaris</Configurations>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFrameworks>net8.0</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Api\**" />
    <Compile Remove="Ops\**" />
    <EmbeddedResource Remove="Api\**" />
    <EmbeddedResource Remove="Ops\**" />
    <None Remove="Api\**" />
    <None Remove="Ops\**" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="V2\Api\" />
    <Folder Include="V2\Ops\" />
  </ItemGroup>
  <PropertyGroup>
    <Trademark>AVEVA</Trademark>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0|AnyCPU'">
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0|AnyCPU'">
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='ReleasePolaris|net8.0|AnyCPU'">
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='DebugContainers|net8.0|AnyCPU'">
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='DebugNoCheck|net8.0|AnyCPU'">
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Kiota.Abstractions" />
    <PackageReference Include="Microsoft.Kiota.Http.HttpClientLibrary" />
    <PackageReference Include="Microsoft.Kiota.Serialization.Form" />
    <PackageReference Include="Microsoft.Kiota.Serialization.Json" />
    <PackageReference Include="Microsoft.Kiota.Serialization.Multipart" />
    <PackageReference Include="Microsoft.Kiota.Serialization.Text" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>