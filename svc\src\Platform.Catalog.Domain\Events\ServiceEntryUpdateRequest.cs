﻿using Aveva.Platform.Catalog.Domain.Models;

namespace Aveva.Platform.Catalog.Domain.Events
{
    /// <summary>
    /// Represents a request to update a service entry.
    /// </summary>
    /// <param name="OldServiceEntry">The old service entry.</param>
    /// <param name="NewServiceEntry">The new service entry.</param>
    public record ServiceEntryUpdateRequest(
    V1ServiceEntry NewServiceEntry,
    V1ServiceEntry OldServiceEntry);
}