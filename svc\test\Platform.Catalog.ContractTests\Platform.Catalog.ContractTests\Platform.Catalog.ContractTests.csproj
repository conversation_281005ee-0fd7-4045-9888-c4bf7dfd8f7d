﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Aveva.Platform.Catalog.ContractTests</AssemblyName>
    <RootNamespace>Aveva.Platform.Catalog.ContractTests</RootNamespace>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
    <OutputType>exe</OutputType>
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aveva.Platform.Common.Testing.ApiContracts" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
    <PackageReference Include="Microsoft.OpenApi.Readers" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Shouldly" />
    <PackageReference Include="xunit.v3" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\src\Platform.Catalog.Domain\Platform.Catalog.Domain.csproj" />
    <ProjectReference Include="..\..\..\src\Platform.Catalog.Events\Platform.Catalog.Events.csproj" />
    <ProjectReference Include="..\..\..\src\Platform.Catalog\Platform.Catalog.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
