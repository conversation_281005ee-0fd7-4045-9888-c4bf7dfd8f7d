﻿using Aveva.Platform.Catalog.Domain.Logging;

namespace Aveva.Platform.Catalog.Logging;

/// <summary>
/// Pre-defined event identifiers for this application to use with logging.
/// </summary>
public enum LoggerEvents
{
    /// <summary>
    /// None.
    /// </summary>
    None = 0,

    /// <summary>
    /// App start up progress reporting before applying configuration.
    /// </summary>
    ApplicationStartupApplyingConfiguration = LoggerEventsConstants.ApplicationEventIdRangeStartId,

    /// <summary>
    /// App start up progress reporting after applying configuration.
    /// </summary>
    ApplicationStartupCompletedApplyingConfiguration = LoggerEventsConstants.ApplicationEventIdRangeStartId + 1,

    /// <summary>
    /// App start up configuration dianostic.
    /// </summary>
    ApplicationStartupConfigurationReport = LoggerEventsConstants.ApplicationEventIdRangeStartId + 2,

    /// <summary>
    /// App is running.
    /// </summary>
    ApplicationRunning = LoggerEventsConstants.ApplicationEventIdRangeStartId + 98,

    /// <summary>
    /// App encountered a problem starting up.
    /// </summary>
    ApplicationFailedToStartup = LoggerEventsConstants.ApplicationEventIdRangeStartId + 900,

    /// <summary>
    /// App exception handling middleware handled an application exception.
    /// </summary>
    ApplicationGeneralException = LoggerEventsConstants.ApplicationEventIdRangeStartId + 998,
}