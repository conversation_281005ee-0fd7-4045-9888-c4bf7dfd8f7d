resources:
  pipelines:
    - pipeline: platform-catalog-ci
      source: platform-catalog-ci
      trigger:
        branches:
          include:
          - refs/heads/main
        stages: 
            - CreatePackageAndPublish

  repositories:
    - repository: 'platform-deployment'
      type: git
      name: 'platform-deployment'
      ref: refs/heads/main

    - repository: CodeSigning
      type: git
      name: DevOps/devops.codesigning.templates
      ref: 'refs/heads/main'

    - repository: ArchitectureRepo
      type: git
      name: Architecture/Architecture
      ref: refs/heads/main

    - repository: pipelineTools
      type: git
      name: DevOps/opsguild.pipeline-tools
      ref: refs/heads/main

parameters:
- name: forcePublish
  type: boolean
  default: false

stages:
  - template: pipelines/v2/client-publish.yml@platform-deployment
    parameters: 
        forcePublish: ${{ parameters.forcePublish }}
        dotnetVersion: '8.x'
        buildFilePath: 'lib/Aveva.Platform.Library.Catalog/svc'
        buildFileName: '**/*.csproj'
        nugetPackageName: 'Aveva.Platform.Catalog.Client'
  - template: pipelines/v2/tsclient-publish.yml@platform-deployment
    parameters:
        forcePublish: ${{ parameters.forcePublish }}
        libDirectoryPath: 'lib/Aveva.Platform.Library.Catalog/svc/src/Aveva.Platform.Catalog.TSClient'
        npmPackageName: '@platform/aveva.platform.catalog.tsclient'
        nodeVersion: '22.12.x'