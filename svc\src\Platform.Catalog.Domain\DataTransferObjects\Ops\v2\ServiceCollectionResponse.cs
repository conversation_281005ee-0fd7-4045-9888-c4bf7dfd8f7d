﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2
{
    /// <summary>
    /// Represents a collection of service catalog entries returned by the API.
    /// </summary>
    public class ServiceCollectionResponse
    {
        /// <summary>
        /// The list of catalog services with their detailed information and configurations. Each item contains complete service details including identity, dependencies, availability settings, and lifecycle configuration.
        /// </summary>
#pragma warning disable CA2227 // Collection properties should be read only
        public List<ServiceResponse>? Items { get; set; }
#pragma warning restore CA2227 // Collection properties should be read only
    }
}