﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Api.v2;

/// <summary>
/// Defines provisioning availability and constraints for a service. This resource contains information about how a service can be provisioned to an account.
/// </summary>
public class ServiceAvailability
{
    /// <summary>
    /// Specifies the maximum number of instances an account can have of this service.
    /// A value of `null` indicates the default limit (10) is applied.
    /// A value of `0` indicates the service cannot be provisioned.
    /// Any positive integer represents the maximum number of instances allowed.
    /// </summary>
    public int? Limit { get; set; }

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        if (obj == null)
        {
            return false;
        }

        return obj is ServiceAvailability item
            && ((item.Limit == null && Limit == null) || (item.Limit != null && item.Limit.Equals(Limit)));
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
}