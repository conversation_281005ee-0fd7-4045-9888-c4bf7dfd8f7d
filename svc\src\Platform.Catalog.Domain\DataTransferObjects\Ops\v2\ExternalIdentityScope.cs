﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;

/// <summary>
/// Defines the authorization scope types available for external identities. Each scope represents a specific permission level or access role.
/// </summary>
public enum ExternalIdentityScope
{
    /// <summary>
    /// Grants access to API-level permissions. This scope allows the external identity to interact with service APIs according to the role's defined permissions (endpoints with a scope of `api/{service}/{operation}`).
    /// </summary>
    apiRole,

    /// <summary>
    /// Grants access to operations-level permissions. This scope allows the external identity to perform operational tasks such as service configuration and management (endpoints with a scope of `ops/{service}/{action}`).
    /// </summary>
    opsRole,
}