﻿namespace Aveva.Platform.Catalog.Api.Configuration
{
    /// <summary>
    /// Swagger constants.
    /// </summary>
    public static class SwaggerConstants
    {
        /// <summary>
        /// Description of accountId parameter.
        /// </summary>
        public const string AccountIdDescription = "The unique identifier of the account. This ID represents the specific account that will access or own the resources being managed.";

        /// <summary>
        /// Description of id parameter.
        /// </summary>
        public const string IdDescription = "The unique identifier of the resource being accessed or modified. This ID is used to specify which specific resource the operation will act upon.";

        /// <summary>
        /// Description of category parameter.
        /// </summary>
        public const string CategoryDescription = "The category identifier to filter services by. Available options include: `Data` (services focused on data storage, processing, and management), `Ingress` (services for data acquisition and input handling), and `null` (returns services from all categories). When provided, only returns services that belong to the specified category.";

        /// <summary>
        /// Default Swagger Descriptions.
        /// </summary>
        public static readonly Dictionary<string, string> DefaultSwaggerDescriptions = new Dictionary<string, string>()
        {
            { "accountId", AccountIdDescription },
            { "id", IdDescription },
            { "category", CategoryDescription },
        };
    }
}