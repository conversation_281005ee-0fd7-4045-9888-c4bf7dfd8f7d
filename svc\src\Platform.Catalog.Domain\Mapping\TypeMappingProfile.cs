﻿using AutoMapper;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;
using Aveva.Platform.Catalog.Domain.Models;
using ApiModels = Aveva.Platform.Catalog.Domain.DataTransferObjects.Api.v2;
using OpsModels = Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;

namespace Aveva.Platform.Catalog.Domain.Mapping;

/// <summary>
/// AutoMapper type mapping configuration profile used to configure Data Transfer Object (DTO) and domain entity type mappings needed by the API layer.
/// </summary>
/// <seealso cref="Profile"/>
/// <seealso href="https://docs.automapper.org/en/stable/Configuration.html#profile-instances"/>
public class TypeMappingProfile : Profile
{
    #region Public Constructors

    /// <summary>
    /// Initializes a new instance of the <see cref="TypeMappingProfile"/> class.
    /// </summary>
    public TypeMappingProfile()
    {
        // Define DTO-entity type mapping configurations.
        CreateMap<V1HostingType, string>().ConvertUsing(new EnumToStringConverter<V1HostingType>());
#pragma warning disable CS0618 // Type or member is obsolete
        CreateMap<V1ServiceAvailability, OpsModels.ServiceAvailability>()
            .ForMember(dest => dest.Visible, cfg => cfg
            .MapFrom(src => src.Enabled));
#pragma warning restore CS0618 // Type or member is obsolete
        CreateMap<V1ServiceAvailability, ApiModels.ServiceAvailability>();
        CreateMap<V1ExternalIdentity, OpsModels.ExternalIdentity>();
        CreateMap<V1ExternalIdentityType, OpsModels.ExternalIdentityType>();
        CreateMap<V1Scope, OpsModels.ExternalIdentityScope>();
        CreateMap<V1Lifecycle, Lifecycle>()
            .ConstructUsing(_ => new Lifecycle())
            .ForMember(dest => dest.ProtocolOptions, opt => opt.MapFrom<ProtocolOptionsResolver>());
        CreateMap<Lifecycle, V1Lifecycle>()
            .ForMember(dest => dest.ProtocolOptions, opt => opt.MapFrom<V1ProtocolOptionsResolver>());
        CreateMap<V1Application, Application>();
        CreateMap<Application, V1Application>();
        CreateMap<V1Geography, Geography>();
        CreateMap<Geography, V1Geography>();
        CreateMap<V1InstanceMode, string>().ConvertUsing(new EnumToStringConverter<V1InstanceMode>());
        CreateMap<V1Trigger, string>().ConvertUsing(new EnumToStringConverter<V1Trigger>());
        CreateMap<V1IntegrationProtocol, string>().ConvertUsing(new EnumToStringConverter<V1IntegrationProtocol>());
        CreateMap<V1CatalogDataDependencyCardinality, string>().ConvertUsing(new EnumToStringConverter<V1CatalogDataDependencyCardinality>());
        CreateMap<V1CatalogDataDependencyType, string>().ConvertUsing(new EnumToStringConverter<V1CatalogDataDependencyType>());
        CreateMap<ProtocolOptions, V1ProtocolOptions>()
            .Include<WebhookProtocolOptions, V1ProtocolOptions>()
            .Include<LegacyProtocolOptions, V1ProtocolOptions>()
            .ForAllMembers(opt => opt.Ignore());
        CreateMap<WebhookProtocolOptions, V1ProtocolOptions>()
            .ForMember(dest => dest.WebhookUri, opt => opt.MapFrom(src => src.WebhookUri))
            .ForMember(dest => dest.SolutionDefinition, opt => opt.Ignore())
            .ForMember(dest => dest.Mappings, opt => opt.Ignore());
        CreateMap<LegacyProtocolOptions, V1ProtocolOptions>()
            .ForMember(dest => dest.SolutionDefinition, opt => opt.MapFrom(src => src.SolutionDefinition))
            .ForMember(dest => dest.Mappings, opt => opt.MapFrom(src => src.Mappings))
            .ForMember(dest => dest.WebhookUri, opt => opt.Ignore());
        CreateMap<V1LegacyProtocolMappings, LegacyProtocolMappings>();
        CreateMap<LegacyProtocolMappings, V1LegacyProtocolMappings>();
        CreateMap<V1LegacyProtocolMappingsApplication, LegacyProtocolApplicationMapping>();
        CreateMap<LegacyProtocolApplicationMapping, V1LegacyProtocolMappingsApplication>();
        CreateMap<V1LegacyProtocolDependencyMapping, LegacyProtocolDependencyMapping>();
        CreateMap<LegacyProtocolDependencyMapping, V1LegacyProtocolDependencyMapping>();

        CreateMap<V1ServiceEntry, OpsModels.ServiceResponse>();
        CreateMap<V1ServiceEntry, ApiModels.ServiceResponse>();
        CreateMap<V1CatalogDataDependency, Dependency>();
        CreateMap<Dependency, V1CatalogDataDependency>();
        CreateMap<V1CatalogDataDependencyType, DependencyType>().ReverseMap();
        CreateMap<V1CatalogDataDependencyCardinality, DependencyCardinality>().ReverseMap();
        CreateMap<DependencyConfig, V1CatalogDataDependencyConfig>();
        CreateMap<V1CatalogDataDependencyConfig, DependencyConfig>();
    }

    #endregion Public Constructors
}