﻿using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Hosting;

namespace Aveva.Platform.Catalog.Api.Tests.Integration.Helpers;

/// <summary>
/// Web application factory that runs the host for integration testing.
/// </summary>
/// <typeparam name="TProgram">The type of the program (executable entry point) to host.</typeparam>
/// <seealso href="https://learn.microsoft.com/en-us/aspnet/core/test/integration-tests?view=aspnetcore-7.0"/>
/// <seealso href="https://andrewlock.net/exploring-dotnet-6-part-6-supporting-integration-tests-with-webapplicationfactory-in-dotnet-6/"/>
/// <seealso cref="WebApplicationFactory{TProgram}"/>
public class TestWebApplicationFactory<TProgram> : WebApplicationFactory<TProgram>
    where TProgram : class
{
    #region Protected Methods

    protected override IHost CreateHost(IHostBuilder builder)
    {
        return base.CreateHost(builder);
    }

    #endregion Protected Methods
}