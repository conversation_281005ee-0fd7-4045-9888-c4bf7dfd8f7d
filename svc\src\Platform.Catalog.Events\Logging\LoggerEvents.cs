﻿using Aveva.Platform.Catalog.Domain.Logging;

namespace Aveva.Platform.Catalog.Events.Logging;

/// <summary>
/// Pre-defined event identifiers for this application to use with logging.
/// </summary>
public enum LoggerEvents
{
    /// <summary>
    /// None.
    /// </summary>
    None = 0,

    /// <summary>
    /// App start up configuration dianostic.
    /// </summary>
    CatalogEventServiceStartup = LoggerEventsConstants.ServiceEventsEventRangeId + 1,

    /// <summary>
    /// App is running.
    /// </summary>
    CatalogEventServiceRunning = LoggerEventsConstants.ServiceEventsEventRangeId + 100,

    /// <summary>
    /// App encountered a problem starting up.
    /// </summary>
    CatalogEventServiceFailedToStartup = LoggerEventsConstants.ServiceEventsEventRangeId + 200,

    /// <summary>
    /// App exception handling middleware handled an application exception.
    /// </summary>
    CatalogEventServiceGeneralException = CatalogEventServiceFailedToStartup + 1,

    /// <summary>
    /// Events raised by Catalog Events Services.
    /// </summary>
    CatalogServiceEventsRangeId = LoggerEventsConstants.ServiceEventsEventRangeId + 300,

    /// <summary>
    /// Catalog ServiceEntry create event logId.
    /// </summary>
    CatalogServicEntryAddEvent = CatalogServiceEventsRangeId + 10,

    /// <summary>
    /// Catalog ServiceEntry create event error logId.
    /// </summary>
    CatalogServicEntryAddEventError = CatalogServicEntryAddEvent + 1,

    /// <summary>
    /// Catalog ServiceEntry update event logId.
    /// </summary>
    CatalogServicEntryUpdateEvent = CatalogServiceEventsRangeId + 20,

    /// <summary>
    /// Catalog ServiceEntry delete event error logId.
    /// </summary>
    CatalogServicEntryUpdateEventError = CatalogServicEntryUpdateEvent + 1,

    /// <summary>
    /// Catalog ServiceEntry delete event logId.
    /// </summary>
    CatalogServicEntryDeleteEvent = CatalogServiceEventsRangeId + 30,

    /// <summary>
    /// Catalog ServiceEntry delete event error logId.
    /// </summary>
    CatalogServicEntryDeleteEventError = CatalogServicEntryDeleteEvent + 1,

    /// <summary>
    /// The access was unauthorized.
    /// </summary>
    UnAuthorizedAccess = LoggerEventsConstants.UnAuthorizedAccess,
}