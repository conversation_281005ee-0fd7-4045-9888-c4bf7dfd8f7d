Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32014.148
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{85689D6E-2177-4718-BB33-2A243C17499B}"
	ProjectSection(SolutionItems) = preProject
		svc\docs\LICENSE.md = svc\docs\LICENSE.md
		svc\docs\PackageIcon.png = svc\docs\PackageIcon.png
		svc\docs\README.md = svc\docs\README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "samples", "samples", "{85689D6E-2177-4718-BB33-2A243C17499C}"
	ProjectSection(SolutionItems) = preProject
		svc\samples\.editorconfig = svc\samples\.editorconfig
		svc\samples\Directory.Build.props = svc\samples\Directory.Build.props
		svc\samples\README.md = svc\samples\README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{05E6D803-2543-4F66-B734-34DA0D515AF2}"
	ProjectSection(SolutionItems) = preProject
		svc\src\.editorconfig = svc\src\.editorconfig
		svc\src\Directory.Build.props = svc\src\Directory.Build.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{3FA99FAC-6C8B-4CD3-BDE5-6CF514C5A230}"
	ProjectSection(SolutionItems) = preProject
		svc\test\Directory.Build.props = svc\test\Directory.Build.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{E2660660-989E-4B3C-8EF4-D8ABE203304A}"
	ProjectSection(SolutionItems) = preProject
		svc\tools\.editorconfig = svc\tools\.editorconfig
		svc\tools\Directory.Build.props = svc\tools\Directory.Build.props
		svc\tools\README.md = svc\tools\README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".Solution Items", ".Solution Items", "{9DD682E2-18C8-40B2-B1E0-F34F12DFA4CB}"
	ProjectSection(SolutionItems) = preProject
		svc\.editorconfig = svc\.editorconfig
		svc\CodeAnalysis.props = svc\CodeAnalysis.props
		svc\Directory.Build.props = svc\Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "config", "config", "{2CD374E8-318D-4AAE-982B-70EB9689A0AB}"
	ProjectSection(SolutionItems) = preProject
		svc\CodeAnalysis.props = svc\CodeAnalysis.props
		svc\stylecop.json = svc\stylecop.json
		svc\WebSign.props = svc\WebSign.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "pipelines", "pipelines", "{FF676BCA-985E-4A1C-97FA-26503A98BF66}"
	ProjectSection(SolutionItems) = preProject
		cataloglibrary-build-test-publish.yml = cataloglibrary-build-test-publish.yml
		deploy\pipelines\publish.yml = deploy\pipelines\publish.yml
		deploy\pipelines\publish_preview_version.yml = deploy\pipelines\publish_preview_version.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "deploy", "deploy", "{5E2D3518-861E-4B22-803E-6159AAB5B710}"
	ProjectSection(SolutionItems) = preProject
		deploy\.json = deploy\.json
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Library.Catalog.Client", "svc\src\Aveva.Platform.Catalog.Client\Platform.Library.Catalog.Client.csproj", "{B58154B5-16FC-4C48-92B7-542D747A27A8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Catalog.Client.ContractTests", "svc\test\Platform.Catalog.Client.ContractTests\Platform.Catalog.Client.ContractTests.csproj", "{3E28FD9E-5375-4F59-B999-BA15350AA29C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		DebugContainers|Any CPU = DebugContainers|Any CPU
		DebugNoCheck|Any CPU = DebugNoCheck|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B58154B5-16FC-4C48-92B7-542D747A27A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B58154B5-16FC-4C48-92B7-542D747A27A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B58154B5-16FC-4C48-92B7-542D747A27A8}.DebugContainers|Any CPU.ActiveCfg = DebugContainers|Any CPU
		{B58154B5-16FC-4C48-92B7-542D747A27A8}.DebugContainers|Any CPU.Build.0 = DebugContainers|Any CPU
		{B58154B5-16FC-4C48-92B7-542D747A27A8}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{B58154B5-16FC-4C48-92B7-542D747A27A8}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{B58154B5-16FC-4C48-92B7-542D747A27A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B58154B5-16FC-4C48-92B7-542D747A27A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E28FD9E-5375-4F59-B999-BA15350AA29C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E28FD9E-5375-4F59-B999-BA15350AA29C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E28FD9E-5375-4F59-B999-BA15350AA29C}.DebugContainers|Any CPU.ActiveCfg = DebugContainers|Any CPU
		{3E28FD9E-5375-4F59-B999-BA15350AA29C}.DebugContainers|Any CPU.Build.0 = DebugContainers|Any CPU
		{3E28FD9E-5375-4F59-B999-BA15350AA29C}.DebugNoCheck|Any CPU.ActiveCfg = DebugNoCheck|Any CPU
		{3E28FD9E-5375-4F59-B999-BA15350AA29C}.DebugNoCheck|Any CPU.Build.0 = DebugNoCheck|Any CPU
		{3E28FD9E-5375-4F59-B999-BA15350AA29C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E28FD9E-5375-4F59-B999-BA15350AA29C}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{2CD374E8-318D-4AAE-982B-70EB9689A0AB} = {9DD682E2-18C8-40B2-B1E0-F34F12DFA4CB}
		{FF676BCA-985E-4A1C-97FA-26503A98BF66} = {5E2D3518-861E-4B22-803E-6159AAB5B710}
		{5E2D3518-861E-4B22-803E-6159AAB5B710} = {9DD682E2-18C8-40B2-B1E0-F34F12DFA4CB}
		{B58154B5-16FC-4C48-92B7-542D747A27A8} = {05E6D803-2543-4F66-B734-34DA0D515AF2}
		{3E28FD9E-5375-4F59-B999-BA15350AA29C} = {3FA99FAC-6C8B-4CD3-BDE5-6CF514C5A230}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F17771B2-A98B-4910-A107-3502742E7501}
	EndGlobalSection
EndGlobal
