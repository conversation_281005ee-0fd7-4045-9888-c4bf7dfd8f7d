﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2
{
    /// <summary>
    /// Defines the mapping of an application to a legacy Solution and Capability Management (SCM) capability definition. This mapping enables backward compatibility with systems that use the older SCM protocol.
    /// </summary>
    public class LegacyProtocolApplicationMapping
    {
        /// <summary>
        /// The name of the application to be mapped. This must match an application name defined in the service's application collection.
        /// </summary>
        public string? Name { get; init; }

        /// <summary>
        /// The name of the legacy SCM capability definition to which the application corresponds. This identifier is used by legacy systems to recognize and interact with the application.
        /// </summary>
        public string? CapabilityDefinition { get; init; }

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is LegacyProtocolApplicationMapping item
                && string.Equals(item.Name, Name, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.CapabilityDefinition, CapabilityDefinition, StringComparison.InvariantCultureIgnoreCase);
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}