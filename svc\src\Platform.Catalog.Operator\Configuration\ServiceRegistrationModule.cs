﻿using System.Diagnostics.CodeAnalysis;
using Aveva.Platform.Common.Framework.Abstractions.Configuration;
using Aveva.Platform.Common.Monitoring.Instrumentation;

[assembly: SuppressMessage("Naming", "CA1716:Identifiers should not match keywords", Justification = "Its an operator and this is not a library used cross language", Scope = "module")]
namespace Aveva.Platform.Catalog.Operator.Configuration;

public sealed class ServiceRegistrationModule : IServiceRegistrationModule
{
    #region Private Fields

    private readonly IConfiguration _configuration;

    #endregion Private Fields

    #region Public Constructors

    /// <summary>
    /// Initializes a new instance of the <see cref="ServiceRegistrationModule"/> class.
    /// </summary>
    /// <param name="configuration">The configuration.</param>
    /// <exception cref="ArgumentNullException">configuration is <c>null</c>.</exception>
    public ServiceRegistrationModule(IConfiguration configuration)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    #endregion Public Constructors

    #region Public Methods

    /// <inheritdoc/>
    public void AddServices(IServiceCollection services)
    {
        services.AddOtelConfiguration(_configuration);
    }

    #endregion Public Methods
}