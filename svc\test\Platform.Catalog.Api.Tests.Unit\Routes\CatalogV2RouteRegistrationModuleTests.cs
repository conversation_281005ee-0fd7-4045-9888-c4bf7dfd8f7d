﻿using Aveva.Platform.Catalog.Api.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Api.Routing;
using Aveva.Platform.Catalog.Api.Tests.Unit.Helpers;
using Aveva.Platform.Catalog.Domain.DataTransferObjects.Ops.v2;
using Aveva.Platform.Catalog.Domain.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Routing;
using Moq;
using Shouldly;
using Xunit;
using ApiModels = Aveva.Platform.Catalog.Api.DataTransferObjects.Api.v2;
using ApiResponse = Aveva.Platform.Catalog.Domain.DataTransferObjects.Api.v2;

namespace Aveva.Platform.Catalog.Api.Tests.Unit.Routes;

/// <summary>
/// <see cref="CatalogV2RouteRegistrationModule"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "Unit")]
[Trait("Category", "Api")]
[Trait("Category", "Api.Unit")]
[Trait("Tag", "Routes")]
public static class CatalogV2RouteRegistrationModuleTests
{
    #region Test Cases for Explicitly Implemented Route Handlers (for simple route implementations)

    [Fact]
    public static void CatalogV2RouteRegistrationModule_AddRoutes_NullApp_Throws()
    {
        // Arrange
        IEndpointRouteBuilder? app = null;
        CatalogV2RouteRegistrationModule subject = new CatalogV2RouteRegistrationModule();

        // Act & Assert
        Should.Throw<ArgumentNullException>(() => subject.AddRoutes(app!))
            .ParamName.ShouldBe(nameof(app));
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntriesApi_AccountDoesNotExist_ReturnsNotFound()
    {
        // Arrange
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);

        var mocks = mockFactory.SetupGetAllV2Api();

        string accountId = "accountId404";

        mocks.AccountMgmtMock
            .Setup(m => m.QueryAvailabilityAsync(accountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Dictionary<string, V1ServiceAvailability>?)null)
            .Verifiable();

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServicesApi(
            accountId,
            new ApiModels.ServiceQueryRequest(),
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<NotFound>();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntriesApi_Returns_Enumeration()
    {
        // Arrange
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);

        var mocks = mockFactory.SetupGetAllV2Api();

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServicesApi(
            accountId,
            new ApiModels.ServiceQueryRequest(),
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ApiResponse.ServiceCollectionResponse>>().ShouldSatisfyAllConditions(
          okResult => okResult!.StatusCode.ShouldBe(StatusCodes.Status200OK),
          okResult => okResult!.Value.ShouldNotBeNull(),
          okResult => okResult!.Value!.Items!.Count.ShouldBe(3));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an empty enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntriesApi_Returns_EmptyEnumeration()
    {
        // Arrange
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);

        List<V1ServiceEntry> expectedEntities = [];
        var mocks =
            mockFactory.SetupGetAllV2Api(expectedEntities);

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServicesApi(
            accountId,
            new ApiModels.ServiceQueryRequest(),
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ApiResponse.ServiceCollectionResponse>>().ShouldSatisfyAllConditions(
          okResult => okResult!.StatusCode.ShouldBe(StatusCodes.Status200OK),
          okResult => okResult!.Value.ShouldNotBeNull(),
          okResult => okResult!.Value!.Items!.Count.ShouldBe(0));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries with availability.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntriesApi_Returns_EnumerationWithAvailability()
    {
        // Arrange
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var expectedAvailability1 = new ServiceAvailability()
        {
            Enabled = true,
            Limit = 1,
        };
        var expectedAvailability2 = new ServiceAvailability()
        {
            Enabled = true,
            Limit = 2,
        };

        var expectedEntities = new V1ServiceEntry[]
        {
            new V1ServiceEntry()
            {
                Id = "1",
                DisplayName = "Name 1",
                HostingType = V1HostingType.Geography,
                Availability = new V1ServiceAvailability()
                {
                    Enabled = expectedAvailability1.Enabled,
                    Limit = expectedAvailability1.Limit,
                },
                Lifecycle = new V1Lifecycle()
                {
                    Trigger = V1Trigger.Catalog,
                },
            },
            new V1ServiceEntry()
            {
                Id = "2",
                DisplayName = "Name 2",
                HostingType = V1HostingType.Geography,
                Availability = new V1ServiceAvailability()
                {
                    Enabled = expectedAvailability2.Enabled,
                    Limit = expectedAvailability2.Limit,
                },
                Lifecycle = new V1Lifecycle()
                {
                    Trigger = V1Trigger.Catalog,
                },
            },
        };

        var mocks = mockFactory.SetupGetAllV2Api(expectedEntities.ToList());

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServicesApi(
            accountId,
            new ApiModels.ServiceQueryRequest(),
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ApiResponse.ServiceCollectionResponse>>().ShouldSatisfyAllConditions(
          okResult => okResult!.StatusCode.ShouldBe(StatusCodes.Status200OK),
          okResult => okResult!.Value.ShouldNotBeNull(),
          okResult => okResult!.Value!.Items!.Count.ShouldBe(2),
          okResult => okResult!.Value!.Items!.ElementAt(0).Availability?.Limit.ShouldBe(expectedAvailability1.Limit),
          okResult => okResult!.Value!.Items!.ElementAt(1).Availability?.Limit.ShouldBe(expectedAvailability2.Limit));

        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries with availability.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntriesApi_DoesNotReturn_EntryWithFalseEnabled()
    {
        // Arrange
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var expectedAvailability1 = new ServiceAvailability()
        {
            Enabled = false,
            Limit = 1,
        };

        var expectedEntities = new V1ServiceEntry[]
        {
            new V1ServiceEntry()
            {
                Id = "1",
                DisplayName = "Name 1",
                HostingType = V1HostingType.Geography,
                Availability = new V1ServiceAvailability()
                {
                    Enabled = expectedAvailability1.Enabled,
                    Limit = expectedAvailability1.Limit,
                },
                Lifecycle = new V1Lifecycle()
                {
                    Trigger = V1Trigger.Catalog,
                },
            },
            new V1ServiceEntry()
            {
                Id = "2",
                DisplayName = "Name 2",
                HostingType = V1HostingType.Geography,
                Lifecycle = new V1Lifecycle()
                {
                    Trigger = V1Trigger.Catalog,
                },
            },
        };

        var mocks = mockFactory.SetupGetAllV2Api(expectedEntities.ToList());

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServicesApi(
            accountId,
            new ApiModels.ServiceQueryRequest(),
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ApiResponse.ServiceCollectionResponse>>().ShouldSatisfyAllConditions(
          okResult => okResult!.StatusCode.ShouldBe(StatusCodes.Status200OK),
          okResult => okResult!.Value.ShouldNotBeNull(),
          okResult => okResult!.Value!.Items!.Count.ShouldBe(1),
          okResult => okResult!.Value!.Items!.ElementAt(0).Availability.ShouldNotBeNull(),
          okResult => okResult!.Value!.Items!.ElementAt(0).Availability?.Limit.ShouldBeNull());

        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntryByIdApi_AccountDoesNotExist_ReturnsNotFound()
    {
        // Arrange
        long identifier = 1;
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);

        var mocks = mockFactory.SetupGetAllV2Api();

        string accountId = "accountId404";

        mocks.AccountMgmtMock
            .Setup(m => m.QueryAvailabilityAsync(accountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Dictionary<string, V1ServiceAvailability>?)null)
            .Verifiable();

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServiceByIdApi(
            identifier.ToString(),
            accountId,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<NotFound>();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntryById returns a serviceResponse.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntryByIdApi_Returns_ItemAssociatedWithSpecifiedIdentifier()
    {
        // Arrange
        long identifier = 1;
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var mocks = mockFactory.SetupGetByIdV2Api(identifier.ToString(), new V1ServiceAvailability());

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServiceByIdApi(
            identifier.ToString(),
            accountId,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ApiResponse.ServiceResponse>>().ShouldSatisfyAllConditions(
            okResult => okResult!.Value!.Id.ShouldBe(identifier.ToString()));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntryById returns a serviceResponse.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntryByIdApi_Returns_ItemWithAvailability()
    {
        // Arrange
        long identifier = 1;
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var expectedAvailability = new V1ServiceAvailability()
        {
            Enabled = true,
            Limit = 1,
        };
        var mocks = mockFactory.SetupGetByIdV2Api(identifier.ToString(), expectedAvailability);

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServiceByIdApi(
            identifier.ToString(),
            accountId,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ApiResponse.ServiceResponse>>().ShouldSatisfyAllConditions(
            okResult => okResult!.Value!.Id.ShouldBe(identifier.ToString()),
            okResult => okResult!.Value!.Availability.ShouldNotBeNull(),
            okResult => okResult!.Value!.Availability?.Limit.ShouldBe(expectedAvailability.Limit));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntryById returns 404 response.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntryByIdApi_NullServiceEntry_ReturnNotFound()
    {
        // Arrange
        long identifier = 2;
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var mocks = mockFactory.SetupGetByIdV2Api(identifier.ToString(), new V1ServiceAvailability());

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServiceByIdApi(
            identifier.ToString(),
            accountId,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<NotFound>().ShouldSatisfyAllConditions(
            result => result.ShouldNotBeNull(),
            result => result!.StatusCode.ShouldBe(StatusCodes.Status404NotFound));
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntryById returns 404 response.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntryByIdApi_EnabledReturnNotFound()
    {
        // Arrange
        long identifier = 1;
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var mocks = mockFactory.SetupGetByIdV2Api(identifier.ToString(), new V1ServiceAvailability()
        {
            Enabled = false,
            Limit = 1,
        });

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServiceByIdApi(
            identifier.ToString(),
            accountId,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<NotFound>().ShouldSatisfyAllConditions(
            result => result.ShouldNotBeNull(),
            result => result!.StatusCode.ShouldBe(StatusCodes.Status404NotFound));
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntriesOps_AccountDoesNotExist_ReturnsDefaults()
    {
        // Arrange
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var mocks = mockFactory.SetupGetAllV2Ops();

        string accountId = "accountId404";

        mocks.AccountMgmtMock
            .Setup(m => m.QueryAvailabilityAsync(accountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Dictionary<string, V1ServiceAvailability>?)null)
            .Verifiable();

        ServiceQueryRequest query = new ServiceQueryRequest()
        {
            AccountId = accountId,
        };

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServicesOps(
            query,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ServiceCollectionResponse>>().ShouldSatisfyAllConditions(
          okResult => okResult!.StatusCode.ShouldBe(StatusCodes.Status200OK),
          okResult => okResult!.Value.ShouldNotBeNull(),
          okResult => okResult!.Value!.Items!.Count.ShouldBe(3));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntriesOps_WithAccountId_Returns_Enumeration()
    {
        // Arrange
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var mocks = mockFactory.SetupGetAllV2Ops();

        string accountId = "accountId";

        ServiceQueryRequest query = new ServiceQueryRequest()
        {
            AccountId = accountId,
        };

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServicesOps(
            query,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ServiceCollectionResponse>>().ShouldSatisfyAllConditions(
          okResult => okResult!.StatusCode.ShouldBe(StatusCodes.Status200OK),
          okResult => okResult!.Value.ShouldNotBeNull(),
          okResult => okResult!.Value!.Items!.Count.ShouldBe(3));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntriesOps_WithoutAccountId_Returns_Enumeration()
    {
        // Arrange
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var mocks = mockFactory.SetupGetAllV2Ops();

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServicesOps(
            new ServiceQueryRequest(),
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ServiceCollectionResponse>>().ShouldSatisfyAllConditions(
          okResult => okResult!.StatusCode.ShouldBe(StatusCodes.Status200OK),
          okResult => okResult!.Value.ShouldNotBeNull(),
          okResult => okResult!.Value!.Items!.Count.ShouldBe(3));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an empty enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntriesOps_Returns_EmptyEnumeration()
    {
        // Arrange
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);

        List<V1ServiceEntry> expectedEntities = [];
        var mocks =
            mockFactory.SetupGetAllV2Ops(expectedEntities);

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServicesOps(
            new ServiceQueryRequest(),
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ServiceCollectionResponse>>().ShouldSatisfyAllConditions(
          okResult => okResult!.StatusCode.ShouldBe(StatusCodes.Status200OK),
          okResult => okResult!.Value.ShouldNotBeNull(),
          okResult => okResult!.Value!.Items!.Count.ShouldBe(0));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries with availability.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntriesOps_Returns_EnumerationWithAvailability()
    {
        // Arrange
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var expectedAvailability1 = new ServiceAvailability()
        {
            Enabled = true,
            Limit = 1,
        };
        var expectedAvailability2 = new ServiceAvailability()
        {
            Enabled = true,
            Limit = 2,
        };

        var expectedEntities = new V1ServiceEntry[]
        {
            new V1ServiceEntry()
            {
                Id = "1",
                DisplayName = "Name 1",
                HostingType = V1HostingType.Geography,
                Availability = new V1ServiceAvailability()
                {
                    Enabled = expectedAvailability1.Enabled,
                    Limit = expectedAvailability1.Limit,
                },
                Lifecycle = new V1Lifecycle()
                {
                    Trigger = V1Trigger.Catalog,
                },
            },
            new V1ServiceEntry()
            {
                Id = "2",
                DisplayName = "Name 2",
                HostingType = V1HostingType.Geography,
                Availability = new V1ServiceAvailability()
                {
                    Enabled = expectedAvailability2.Enabled,
                    Limit = expectedAvailability2.Limit,
                },
                Lifecycle = new V1Lifecycle()
                {
                    Trigger = V1Trigger.Catalog,
                },
            },
        };

        var mocks = mockFactory.SetupGetAllV2Ops(expectedEntities.ToList());

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServicesOps(
            new ServiceQueryRequest()
            {
                AccountId = accountId,
            },
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ServiceCollectionResponse>>().ShouldSatisfyAllConditions(
          okResult => okResult!.StatusCode.ShouldBe(StatusCodes.Status200OK),
          okResult => okResult!.Value.ShouldNotBeNull(),
          okResult => okResult!.Value!.Items!.Count.ShouldBe(2),
          okResult => okResult!.Value!.Items!.ElementAt(0).Availability?.Enabled.ShouldBe(expectedAvailability1.Enabled),
          okResult => okResult!.Value!.Items!.ElementAt(0).Availability?.Limit.ShouldBe(expectedAvailability1.Limit),
          okResult => okResult!.Value!.Items!.ElementAt(1).Availability?.Enabled.ShouldBe(expectedAvailability2.Enabled),
          okResult => okResult!.Value!.Items!.ElementAt(1).Availability?.Limit.ShouldBe(expectedAvailability2.Limit));

        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntryByIdOps_WithAccountId_AccountDoesNotExist_ReturnsDefaults()
    {
        // Arrange
        long identifier = 1;
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var mocks = mockFactory.SetupGetByIdV2Ops(identifier.ToString());

        string accountId = "accountId404";

        mocks.AccountMgmtMock
            .Setup(m => m.QueryAvailabilityAsync(accountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Dictionary<string, V1ServiceAvailability>?)null)
            .Verifiable();

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServiceByIdOps(
            identifier.ToString(),
            accountId,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ServiceResponse>>().ShouldSatisfyAllConditions(
            okResult => okResult!.Value!.Id.ShouldBe(identifier.ToString()));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntryByIdOps_WithAccountId_Returns_ItemAssociatedWithSpecifiedIdentifier()
    {
        // Arrange
        long identifier = 1;
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var mocks = mockFactory.SetupGetByIdV2Ops(identifier.ToString());

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServiceByIdOps(
            identifier.ToString(),
            accountId,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ServiceResponse>>().ShouldSatisfyAllConditions(
            okResult => okResult!.Value!.Id.ShouldBe(identifier.ToString()));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntries returns an enumeration of catalog entries.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntryByIdOps_WithAccountId_NullServiceEntry_Returns_NotFound()
    {
        // Arrange
        long identifier = 2;
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var mocks = mockFactory.SetupGetByIdV2Ops(identifier.ToString());

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServiceByIdOps(
            identifier.ToString(),
            accountId,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<NotFound>().ShouldSatisfyAllConditions(
            result => result.ShouldNotBeNull(),
            result => result!.StatusCode.ShouldBe(StatusCodes.Status404NotFound));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntryById returns a serviceResponse.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntryByIdOps_Returns_ItemWithAvailability()
    {
        // Arrange
        long identifier = 1;
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var expectedAvailability = new ServiceAvailability()
        {
            Enabled = true,
            Limit = 1,
        };
        var mocks = mockFactory.SetupGetByIdV2Ops(identifier.ToString(), expectedAvailability);

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServiceByIdOps(
            identifier.ToString(),
            accountId,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ServiceResponse>>().ShouldSatisfyAllConditions(
            okResult => okResult!.Value!.Id.ShouldBe(identifier.ToString()),
            okResult => okResult!.Value!.Availability.ShouldNotBeNull(),
            okResult => okResult!.Value!.Availability?.Enabled.ShouldBe(expectedAvailability.Enabled),
            okResult => okResult!.Value!.Availability?.Limit.ShouldBe(expectedAvailability.Limit));
        mockFactory.Verify();
    }

    /// <summary>
    /// Tests that GetAccountScopedCatalogEntryById returns 404 response.
    /// </summary>
    [Fact]
    public static async Task CatalogV2RouteRegistrationModule_GetAccountScopedCatalogEntryByIdOps_VisibileFalse_Returns()
    {
        // Arrange
        var expectedAvailability = new ServiceAvailability()
        {
            Enabled = true,
            Limit = 1,
        };
        long identifier = 1;
        MockRepository mockFactory = new MockRepository(MockBehavior.Loose);
        var mocks = mockFactory.SetupGetByIdV2Ops(identifier.ToString(), new ServiceAvailability()
        {
            Enabled = true,
            Limit = 1,
        });

        string accountId = "accountId";

        // Act
        var result = await CatalogV2RouteRegistrationModule.GetAccountScopedServiceByIdOps(
            identifier.ToString(),
            accountId,
            mocks.DtoTypeMapperMock.Object,
            mocks.RepositoryMock.Object,
            mocks.ServiceAvailabilityRepositoryMock.Object,
            mocks.AuthorizationMock.Object,
            mocks.AccountMgmtMock.Object,
            mocks.LoggerMock.Object,
            mocks.MetricsMock.Object,
            CancellationToken.None).ConfigureAwait(true);

        // Assert
        result.Result.ShouldBeAssignableTo<Ok<ServiceResponse>>().ShouldSatisfyAllConditions(
            okResult => okResult!.Value!.Id.ShouldBe(identifier.ToString()),
            okResult => okResult!.Value!.Availability.ShouldNotBeNull(),
            okResult => okResult!.Value!.Availability?.Enabled.ShouldBe(expectedAvailability.Enabled),
            okResult => okResult!.Value!.Availability?.Limit.ShouldBe(expectedAvailability.Limit));
    }

    #endregion Test Cases for Explicitly Implemented Route Handlers (for simple route implementations)
}