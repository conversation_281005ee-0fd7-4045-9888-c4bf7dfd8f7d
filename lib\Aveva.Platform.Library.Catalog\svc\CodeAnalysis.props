<Project>
  <PropertyGroup Condition="Exists('$(MSBuildThisFileDirectory)stylecop.json')">
    <DoNotAutomaticallyLoadStyleCopJson>true</DoNotAutomaticallyLoadStyleCopJson>
  </PropertyGroup>

  <!-- Establish the default code analysis settings for the solution -->
  <ItemGroup Condition="Exists('$(MSBuildThisFileDirectory)stylecop.json')">
    <AdditionalFiles Include="$(MSBuildThisFileDirectory)stylecop.json" Link="stylecop.json" Visible="false" />
  </ItemGroup>

  <!-- Register the packages containing the code analyzers we'd like to use -->
  <ItemGroup>
    <PackageReference Include="Aveva.Ruleset" PrivateAssets="all" ExcludeAssets="contentfiles" />
    <PackageReference Include="VogueDeputy" PrivateAssets="all" />
  </ItemGroup>
</Project>