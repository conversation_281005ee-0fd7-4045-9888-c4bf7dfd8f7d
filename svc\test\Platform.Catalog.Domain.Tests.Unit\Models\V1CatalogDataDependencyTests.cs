﻿using Aveva.Platform.Catalog.Domain.Models;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Domain.Tests.Unit.Models;

/// <summary>
/// <see cref="V1CatalogDataDependencyTests"/> unit tests.
/// </summary>
[Trait("Category", "Unit")]
[Trait("Category", "Domain")]
[Trait("Category", "Domain.Unit")]
public static class V1CatalogDataDependencyTests
{
    #region Test Cases

    [Fact]
    public static void V1CatalogDataDependencyTests_Equals_True()
    {
        // Arrange
        var first = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
        };

        var second = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
        };

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1CatalogDataDependencyTests_Equals_False()
    {
        // Arrange
        var first = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
        };

        var second = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = null,
            Type = V1CatalogDataDependencyType.Optional,
        };

        // Assert
        first.Equals(second).ShouldBeFalse();
    }

    [Fact]
    public static void V1CatalogDataDependencyTests_Equals_Null_False()
    {
        // Arrange
        V1CatalogDataDependency first = new V1CatalogDataDependency()
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
        };

        // Assert
        first.Equals(null).ShouldBeFalse();
    }

    [Fact]
    public static void V1CatalogDataDependencyTests_NullEmptyConfig_Equals_True()
    {
        // Arrange
        var first = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
        };

        var second = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
            Config = new Dictionary<string, V1CatalogDataDependencyConfig>(),
        };

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1CatalogDataDependencyTests_Config_Equals_True()
    {
        // Arrange
        var first = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
            Config = new Dictionary<string, V1CatalogDataDependencyConfig>
            {
                { "test1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Min = 1, Max = 100 } },
                { "test2", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Min = 0, Max = 10 } },
            },
        };

        var second = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
            Config = new Dictionary<string, V1CatalogDataDependencyConfig>
            {
                { "test1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Min = 1, Max = 100 } },
                { "test2", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Min = 0, Max = 10 } },
            },
        };

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1CatalogDataDependencyTests_NullConfig_Equals_True()
    {
        // Arrange
        var first = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
            Config = null,
        };

        var second = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
            Config = new Dictionary<string, V1CatalogDataDependencyConfig>(),
        };

        // Assert
        first.Equals(second).ShouldBeTrue();
    }

    [Fact]
    public static void V1CatalogDataDependencyTests_Config_Equals_False()
    {
        // Arrange
        var first = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
            Config = new Dictionary<string, V1CatalogDataDependencyConfig>
            {
                { "test1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Min = 1, Max = 100 } },
                { "test2", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Min = 0, Max = 10 } },
            },
        };

        var second = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
            Config = new Dictionary<string, V1CatalogDataDependencyConfig>
            {
                { "test1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Min = 1, Max = 100 } },
                { "test2", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Min = 0, Max = 100 } },
            },
        };

        // Assert
        first.Equals(second).ShouldBeFalse();
    }

    [Fact]
    public static void V1CatalogDataDependencyTests_NullConfig_Equals_False()
    {
        // Arrange
        var first = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
            Config = null,
        };

        var second = new V1CatalogDataDependency
        {
            Cardinality = V1CatalogDataDependencyCardinality.One,
            Colocated = true,
            Type = V1CatalogDataDependencyType.Optional,
            Config = new Dictionary<string, V1CatalogDataDependencyConfig>
            {
                { "test1", new V1CatalogDataDependencyConfig { Label = "label1", Help = "help1", Min = 1, Max = 100 } },
                { "test2", new V1CatalogDataDependencyConfig { Label = "label2", Help = "help2", Min = 0, Max = 100 } },
            },
        };

        // Assert
        first.Equals(second).ShouldBeFalse();
    }
    #endregion Test Cases
}