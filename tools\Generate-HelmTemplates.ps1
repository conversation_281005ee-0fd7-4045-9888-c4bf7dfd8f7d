param (
  [Parameter(Position=0)]
  [ValidateSet('pre','post')]
  [System.String]$Prefix
)

$HelmChartPath = Resolve-Path $(Join-Path $PSScriptRoot "..\deploy\helm")
$TempFolderPath = $ExecutionContext.SessionState.Path.GetUnresolvedProviderPathFromPSPath($(Join-Path $PSScriptRoot "..\.temp"))
New-Item -Path $TempFolderPath -ItemType Directory -Force | Out-Null
helm dependency update $HelmChartPath
Write-Host "helm template platform-catalog $HelmChartPath -f ""$HelmChartPath\local.values.yaml"" -n platform-catalog"
helm template platform-catalog $HelmChartPath -f "$HelmChartPath\local.values.yaml" -n platform-catalog | Out-File "$TempFolderPath\$Prefix-local.yaml"
Write-Host "helm template platform-catalog $HelmChartPath -f ""$HelmChartPath\eda.values.yaml"" -n platform-catalog"
helm template platform-catalog $HelmChartPath -f "$HelmChartPath\eda.values.yaml" -n platform-catalog | Out-File "$TempFolderPath\$Prefix-pipeline.yaml"
Write-Host "helm template platform-catalog $HelmChartPath -f ""$HelmChartPath\devonebox.values.yaml"" -n platform-catalog"
helm template platform-catalog $HelmChartPath -f "$HelmChartPath\devonebox.values.yaml" -n platform-account-mgmt | Out-File "$TempFolderPath\$Prefix-devonebox.yaml"