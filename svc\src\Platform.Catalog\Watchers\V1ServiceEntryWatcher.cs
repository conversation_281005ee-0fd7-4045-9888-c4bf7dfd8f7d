﻿using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Aveva.Platform.Catalog.Api.Logging;
using Aveva.Platform.Catalog.Domain;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.Infrastructure.Entities;
using k8s;

namespace Aveva.Platform.Catalog.Watchers;

/// <summary>
/// Watcher class to monitor the events happening on ServiceEntry CRD.
/// </summary>
public class V1ServiceEntryWatcher : BackgroundService
{
    private readonly IKubernetes _kubernetes;
    private readonly IServiceEntryRepository _repository;
    private readonly ILogger<V1ServiceEntryWatcher> _logger;
    private readonly CatalogMetrics _metrics;
    private Watcher<V1K8sServiceEntry>? _watcher;
    private bool _disposedValue;
    private GenericClient _genericClient;

    /// <summary>
    /// Initializes a new instance of the <see cref="V1ServiceEntryWatcher"/> class.
    /// </summary>
    /// <param name="kubernetes">Kubernetes instance.</param>
    /// <param name="repository">Cached Caalog repository.</param>
    /// <param name="logger">Logger.</param>
    /// <param name="metrics">CatalogMetrics.</param>
    public V1ServiceEntryWatcher(
        IKubernetes kubernetes,
        IServiceEntryRepository repository,
        ILogger<V1ServiceEntryWatcher> logger,
        CatalogMetrics metrics)
    {
        _kubernetes = kubernetes;
        _repository = repository;
        _logger = logger;
        _genericClient = new GenericClient(_kubernetes, CatalogConstants.Group, CatalogConstants.V1, CatalogConstants.ServiceEntry.Plural);
        _metrics = metrics;
    }

    /// <summary>
    /// Dispose CachedCatalogRepository class.
    /// </summary>
    public new void Dispose()
    {
        // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        Dispose(disposing: true);
    }

    /// <summary>
    /// Disposes managed and unmanaged resources used by CachedCatalogREpository class.
    /// </summary>
    /// <param name="disposing">boolean.</param>
    internal void Dispose(bool disposing)
    {
        if (!_disposedValue)
        {
            _genericClient?.Dispose();
            _watcher?.Dispose();
            _disposedValue = true;
        }
    }

    /// <summary>
    /// Method to add watch on CRD.
    /// </summary>
    protected void AddWatcher()
    {
        using var activity = CatalogTraceSource.WatcherTrace.StartActivity("catalog.k8sWatcher.event", ActivityKind.Client);
        {
            activity?.AddTag("catalog.watcher.event_type", "Added");
            activity?.AddEvent(new ActivityEvent($"Adding {nameof(V1ServiceEntryWatcher)}."));
            _genericClient = new GenericClient(_kubernetes, CatalogConstants.Group, CatalogConstants.V1, CatalogConstants.ServiceEntry.Plural);
            _watcher = _genericClient.Watch<V1K8sServiceEntry>(onEvent: OnEventHandler, onError: OnErrorHandler, onClosed: OnClosedHandler);
            _metrics.RecordWatcherStatus(1);
        }
    }

    /// <summary>
    /// Background services execution task.
    /// Adds a watcher to ServiceEntry-v1 CRD.
    /// </summary>
    /// <param name="stoppingToken">cancellation token.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [SuppressMessage("Performance", "CA1848:Use the LoggerMessage delegates", Justification = "LoggerMessage using the existing approach. Will explore it later.")]
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation($"Executing {nameof(V1ServiceEntryWatcher)} configuration.");
        AddWatcher();
        await Task.CompletedTask.ConfigureAwait(false);
    }

    private void OnEventHandler(WatchEventType type, V1K8sServiceEntry entry)
    {
        using var activity = CatalogTraceSource.WatcherTrace.StartActivity("catalog.serviceentry.event", ActivityKind.Server);
        {
            activity?.AddTag("catalog.serviceentry.event_type", type.ToString());
            activity?.AddTag("catalog.serviceentry_id", entry!.Spec!.Id);
            activity?.AddEvent(new ActivityEvent($"{nameof(V1ServiceEntryWatcher)} received {type} for {entry!.Spec!.DisplayName} resource."));
            _repository.ClearCache();
            _metrics.RecordWatcherStatus(1);
        }
    }

    private void OnErrorHandler(Exception ex)
    {
        using var activity = CatalogTraceSource.WatcherTrace.StartActivity("catalog.k8sWatcher.event", ActivityKind.Server);
        {
            activity?.AddTag("catalog.watcher.event_type", "Error");
            activity?.SetStatus(ActivityStatusCode.Error);
            activity?.AddEvent(new ActivityEvent($"{nameof(V1ServiceEntryWatcher)} Error. {ex.Message}"));
            activity?.AddException(ex);
            _logger.ServiceEntryWatcherError($"Exception in {nameof(V1ServiceEntryWatcher)} : {ex}");
        }
    }

    private void OnClosedHandler()
    {
        using var activity = CatalogTraceSource.WatcherTrace.StartActivity("catalog.k8sWatcher.event", ActivityKind.Server);
        {
            _metrics.RecordWatcherStatus(0);
            activity?.AddTag("catalog.watcher.event_type", "Closed");
            activity?.AddEvent(new ActivityEvent($"{nameof(V1ServiceEntryWatcher)} closed."));
            _logger.ServiceEntryWatcherError($"{nameof(V1ServiceEntryWatcher)} is closed.");
        }

        AddWatcher();
    }
}