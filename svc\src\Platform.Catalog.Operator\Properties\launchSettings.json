{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "liveness", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5000"}, "https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "liveness", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:5001;http://localhost:5000"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "liveness", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "https://localhost:5001", "sslPort": 44356}}}