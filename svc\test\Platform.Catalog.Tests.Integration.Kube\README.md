# How to Run the Integration Tests Locally

1. Update the IntegrationTestOptions so the Excutable's point to the correct location on your machine. You can get the locations using Get-Command.
1. Ensure your Minikube is Running and is your current kubectl context.
1. Run Deploy-Local to publish the requisite local helm charts and images to your minikube.
1. Run the Tests

> Note: There are a number of pre-requisites for the tests please see IntegrationTestOptions for things you may need to tweak for your environment.