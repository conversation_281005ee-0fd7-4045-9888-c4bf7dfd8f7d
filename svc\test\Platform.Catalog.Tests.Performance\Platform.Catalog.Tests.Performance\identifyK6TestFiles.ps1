Write-Host "Identifying K6 Test Files..."

# The path to "deploy>pipelines>variables" can be at any depth depending on how the project is structured.
# It is mandatory to verify the below relative path to the variables folder and update as necessary.
$folderPath = "../../../../deploy/pipelines/variables"
$filePath = Join-Path $folderPath "k6TestFiles.yml"

$testsDirectory = "/dist/tests"

$k6TestFileCount = 0
$testServiceNames = @{}
$testsDict = @{}

$distTests = Get-ChildItem -Path ("." + $testsDirectory) | Get-ChildItem -Filter *.js -Recurse -File
foreach ($testFileInfo in $distTests)
{
    $testScriptName = $testFileInfo.Name
    $testScriptName = $testScriptName.Remove($testScriptName.IndexOf(".js", 3))

    $testScriptDirectory = $testFileInfo.DirectoryName.Replace("\", "/")
    $testsDirIndex = $testScriptDirectory.IndexOf($testsDirectory)
    $testServiceName = $testScriptDirectory.Remove(0, $testsDirIndex + $testsDirectory.Length + 1)
    # If the test two+ directories deep, we need to know what only the first directory is
    $rootTestServiceName = $testServiceName.Split("/")[0]

    $testCaseArrString = ""
    if (Test-Path -Path "$testScriptDirectory/$testScriptName.data.json") {
        $dataJsonFile = Get-ChildItem -Path "$testScriptDirectory/$testScriptName.data.json"
        $dataJson = Get-Content $dataJsonFile | ConvertFrom-Json
        if (($dataJson.psobject.properties.name).Contains("testCases")) {
            $testCases = $dataJson.testCases
            $testCaseArrString += ",testcases:"
            for ($i = 0; $i -lt $testCases.Length; $i++) {
                if ($i -ne 0) {
                    $testCaseArrString += ","
                }
                $testCaseArrString += "$i"
            }
        }
    }

    $testFileEntry = ($testServiceName + "/" + $testScriptName + "," + $testServiceName.Replace("/", "_") + "," + $testScriptName + $testCaseArrString)
    $testServiceNames[$rootTestServiceName] += @($testFileEntry)

    $testsDictKey = $testServiceName + "/" + $testScriptName
    $testsDict[$testsDictKey] = $testFileEntry

    $k6TestFileCount++
}

$testServiceNamesSorted = $testServiceNames.GetEnumerator() | Sort-Object Name

# Here I need to iterate over the services and add them to the string for writing to the YAML file
$yaml = ("#this file is auto-generated by identifyK6TestFiles.ps1, do not manually change it.`nvariables:")
$FullSuiteTestFileDefinitions = "`""
# foreach ($testServiceName in $sortedTestServiceNames.Keys) {
foreach ($testServiceName in $testServiceNamesSorted) {
    # Write-Host ("`ttestServiceName " + $($testServiceName.Name))
    $k6ServiceTestFileDefinitions = ""

    $orderFilePath = ".$testsDirectory/$($testServiceName.Name)/.order"
    if ( Test-Path -Path $orderFilePath -PathType Leaf ) {
        $orderedTests = [System.IO.File]::ReadAllLines($orderFilePath)
        if ( $orderedTests.Length -ne $($testServiceName.Value.Length) ) {
            Write-Error "The .order file for the '$testServiceName' service has $($orderedTests.Length) tests, but there should be $($testServiceNames[$testServiceName].Length) tests.  Exiting now!"
            exit 1
        }

        foreach($testName in $orderedTests) {
            $key = $($testServiceName.Name) + "/" + $testName
            if ( $testsDict.ContainsKey($key) -eq $False ) {
                Write-Error "Test name '$testName' listed in order file '$orderFilePath' does not exist in the dist directory. Exiting now!"
                exit 1
            }

            $serviceTest = $testsDict[$key]
            if ($k6ServiceTestFileDefinitions -ne "") {
                $serviceTest = (";" + $serviceTest)
            }
            $k6ServiceTestFileDefinitions = ($k6ServiceTestFileDefinitions + $serviceTest)
        }
    }
    else {
        foreach ($serviceTest in $($testServiceName.Value)){
            if ($k6ServiceTestFileDefinitions -ne "") {
                $serviceTest = (";" + $serviceTest)
            }
            $k6ServiceTestFileDefinitions = ($k6ServiceTestFileDefinitions + $serviceTest)
        }
    }
    
    $yaml = ($yaml + "`n  " + $($testServiceName.Name) + "TestFileDefinitions: `"" + $k6ServiceTestFileDefinitions + "`"")
    $FullSuiteTestFileDefinitions += $($testServiceName.Name) + "TestFileDefinitions:" + $k6ServiceTestFileDefinitions + "|"
}

$FullSuiteTestFileDefinitions = $FullSuiteTestFileDefinitions.TrimEnd('|') + "`""

$yaml = $yaml + "`n  fullSuiteTestFileDefinitions: $FullSuiteTestFileDefinitions"

if (!(Test-Path $folderPath -PathType Container)) {
    # Create the folder if it doesn't exist
    New-Item -Path $folderPath -ItemType Directory
    Write-Host "Variables Folder under pipelines created successfully: $folderPath"
} else {
    Write-Host "Variables Folder under pipelines already exists: $folderPath"
}

Set-Content -Path $filePath -Value $yaml

Write-Host ("`tUpdated k6TestFiles.yml with " + $k6TestFileCount + " tests")