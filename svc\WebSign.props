<Project>
  <Target Name="WebSignDLL" Condition="'$(SignOutput)'=='TRUE' and Exists('$(TargetDir)$(TargetName).dll')" AfterTargets="Build">
    <ItemGroup>
      <WebSignExe Include="$(MSBuildThisFileDirectory)..\packages\osisoft.websign\**\tools\WebSign.exe" />
    </ItemGroup>
    <Exec Command="&quot;@(WebSignExe)&quot; /HOST:oakcwss-2.osisoft.int /Retry:3 /ST /R /Diag &quot;$(TargetPath)&quot;" />
    <Exec Command="powershell -Command ($acs = Get-AuthenticodeSignature &apos;$(TargetDir)$(TargetName).dll&apos;);if($acs.Status -ne &apos;Valid&apos;){throw $acs.StatusMessage}" />
  </Target>
  <Target Name="WebSignExecutable" Condition="'$(SignOutput)'=='TRUE' and Exists('$(TargetDir)$(TargetName).exe')" AfterTargets="Build">
    <ItemGroup>
      <WebSignExe Include="$(MSBuildThisFileDirectory)..\packages\osisoft.websign\**\tools\WebSign.exe" />
    </ItemGroup>
    <!-- Sign the executable in the intermediate folder since it gets copied over last during an MSBuild package -->
    <Exec Command="&quot;@(WebSignExe)&quot; /HOST:oakcwss-2.osisoft.int /Retry:3 /ST /R /Diag &quot;$(ProjectDir)$(IntermediateOutputPath)$(TargetName).exe&quot;" />
    <Exec Command="powershell -Command ($acs = Get-AuthenticodeSignature &apos;$(ProjectDir)$(IntermediateOutputPath)$(TargetName).exe&apos;);if($acs.Status -ne &apos;Valid&apos;){throw $acs.StatusMessage}" />
    <!-- Now sign the executable in the output dir -->
    <Exec Command="&quot;@(WebSignExe)&quot; /HOST:oakcwss-2.osisoft.int /Retry:3 /ST /R /Diag &quot;$(TargetDir)$(TargetName).exe&quot;" />
    <Exec Command="powershell -Command ($acs = Get-AuthenticodeSignature &apos;$(TargetDir)$(TargetName).exe&apos;);if($acs.Status -ne &apos;Valid&apos;){throw $acs.StatusMessage}" />
  </Target>
</Project>