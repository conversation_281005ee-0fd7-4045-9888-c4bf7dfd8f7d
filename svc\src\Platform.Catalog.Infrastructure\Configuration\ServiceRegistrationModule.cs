﻿using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Infrastructure.Facades;
using Aveva.Platform.Catalog.Infrastructure.Repository;
using Aveva.Platform.Common.Framework.Abstractions.Configuration;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;

namespace Aveva.Platform.Catalog.Infrastructure.Configuration;

internal sealed class ServiceRegistrationModule : IServiceRegistrationModule
{
    #region Public Constructors

    /// <summary>
    /// Initializes a new instance of the <see cref="ServiceRegistrationModule"/> class.
    /// </summary>
    public ServiceRegistrationModule()
    {
    }

    #endregion Public Constructors

    #region Public Methods

    /// <inheritdoc/>
    public void AddServices(IServiceCollection services)
    {
        services.AddSingleton<IMemoryCache, MemoryCache>();
        services.AddSingleton<IServiceEntryRepository, ServiceEntryRepository>();
        services.AddSingleton<IServiceAvailabilityRepository, ServiceAvailabilityRepository>();
        services.AddSingleton<IKubernetesFacade, KubernetesFacade>();
    }

    #endregion Public Methods
}