﻿using Microsoft.AspNetCore.Mvc;

namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2;

/// <summary>
/// Defines the base request for querying for catalog services.
/// </summary>
public record QuerySpecification
{
    /// <summary>
    /// The category to filter services by. When specified, only returns services that belong to this category. Valid values include `Data` (services focused on data storage, processing, and management) and `Ingress` (services for data acquisition and input handling).
    /// </summary>
    [FromQuery]
    public Category? Category { get; init; }
}