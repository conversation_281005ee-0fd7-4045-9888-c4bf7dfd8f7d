﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2
{
    /// <summary>
    /// Defines the webhook protocol options for a catalog service.
    /// </summary>
    public class WebhookProtocolOptions : ProtocolOptions
    {
        /// <summary>
        /// The webhook URI for webhook protocol communication.
        /// </summary>
        public Uri? WebhookUri { get; init; }

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is WebhookProtocolOptions item
                && ((item.WebhookUri == null && WebhookUri == null) || (item.WebhookUri != null && item.WebhookUri.Equals(WebhookUri)));
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}