# Test code editorconfig preferences

####################################################################
# Team-specific Rules, Extensions, and Overrides
# Description:
#   Rules from the common OSIsoft rules may be overridden here.
#   Extensions to enable additional rules is always acceptable.
#   Overrides do disable a rule are by exception only and should
#   include one of the following:
#   1)  A PBI number for the eventual removal of the override.
#       For geoinstnew, in a case where compliance will take
#       time to implement.
#   2)  A justification of why the override is required.
####################################################################
# Example to add enforcement of CA1008 and disable SA1000 with PBI:
#   # CA1008: Enums should have zero value
#   dotnet_diagnostic.CA1008.severity = error
#   # SA1000: Keywords should be spaced correctly
#   dotnet_diagnostic.SA1000.severity = none # PBI: <link to="" PBI="">
####################################################################

# CSharp code settings:
[*.cs]
###################################################################################################
# Code Analysis Rule extensions (no justification or PBI reference required)
###################################################################################################
# CS1591: Missing XML comment for publicly visible type or member 'Type_or_Member'
dotnet_diagnostic.CS1591.severity = none


###################################################################################################
# Code Analysis Rule removals + overrides that downgrade code analysis (include justification or PBI #)
###################################################################################################
# CA1707: Identifiers should not contain underscores - Justification: Test method names use an underscore separate convention for readability.
dotnet_diagnostic.CA1707.severity = none
# CA1305: Specify IFormatProvider - Justification: Tests are not localized and are not expected to ever need to be.
dotnet_diagnostic.CA1305.severity = none
# CA1863: Use 'CompositeFormat' - Justification: .NET8 feature for performance sensitive code is not applicable to general tests.
dotnet_diagnostic.CA1863.severity = none
#SA1300: Identifiers should not contain underscores
dotnet_diagnostic.SA1300.severity = none #Justification: It can be ignored at this point as bearer_access_token expecting lower-case parameters
#IDE1006: Naming rule violation
dotnet_diagnostic.IDE1006.severity = none #Justification: It can be ignored at this point as bearer_access_token expecting lower-case parameters
#IDE1006: Do not raise reserved exception types
dotnet_diagnostic.CA2201.severity = none #Justification: It can be ignored at for this project for simplification.