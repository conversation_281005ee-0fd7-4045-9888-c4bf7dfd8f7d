﻿using Aveva.Platform.Catalog.Api.Configuration;
using Aveva.Platform.Common.Testing.Mocks;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Shouldly;
using Xunit;

namespace Aveva.Platform.Catalog.Api.Tests.Unit.Configuration;

/// <summary>
/// <see cref="ServiceRegistrationModule"/> unit test fixture.
/// </summary>
/// <remarks>
/// For help authoring tests, refer to the documentation for the testing framework and libraries we use in our test fixtures:
/// <list type="bullet">
/// <item><see href="https://xunit.net/#documentation">Xunit</see> test execution framework (note that Xunit docs are poor at best - try BingGoogling instead)</item>
/// <item><see href="https://shouldly.readthedocs.io/en/latest/">Shouldly</see> assertion library (new docs - a work in progress)</item>
/// <item><see href="http://docs.shouldly-lib.net/docs/shouldsatisfyallconditions">Shouldly</see> (includes missing content like ShouldThrow)</item>
/// <item><see href="https://github.com/shouldly/shouldly">Shouldly GitHub</see></item>
/// <item><see href="https://github.com/Moq/moq4/wiki/Quickstart">Moq</see> mocking/stubbing library based on lambda expressions from LINQ</item>
/// </list>
/// </remarks>
[Trait("Category", "App")]
[Trait("Category", "Unit")]
[Trait("Category", "App.Unit")]
[Trait("Tag", "Configuration")]
public class ServiceRegistrationModuleTests
{
    #region Test Cases

    [Fact]
    public void ServiceRegistrationModule_AddServices_RegistersExpectedServices()
    {
        // Arrange
        ServiceCollectionMock services = new ServiceCollectionMock();

        // Api versioning service registration will expect this service descriptor, so we add it to the services collection
        ServiceDescriptor serviceDescriptor = new ServiceDescriptor(typeof(LinkGenerator), new Mock<LinkGenerator>().Object);
        services.SetupServiceDescriptor(serviceDescriptor);

        ServiceRegistrationModule serviceRegistrationModule = new ServiceRegistrationModule();

        // Act
        services.InvocationCount.ShouldBe(0);
        serviceRegistrationModule.AddServices(services.Object);

        // Assert
        services.InvocationCount.ShouldBeGreaterThan(0);
    }

    #endregion Test Cases
}