// <auto-generated/>
using System.Runtime.Serialization;
using System;
namespace Aveva.Platform.Catalog.Client.V2.Ops.Models
{
    /// <summary>Defines the authorization scope types available for external identities. Each scope represents a specific permission level or access role.</summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public enum ExternalIdentityScope
    {
        [EnumMember(Value = "apiRole")]
        #pragma warning disable CS1591
        ApiRole,
        #pragma warning restore CS1591
        [EnumMember(Value = "opsRole")]
        #pragma warning disable CS1591
        OpsRole,
        #pragma warning restore CS1591
    }
}
