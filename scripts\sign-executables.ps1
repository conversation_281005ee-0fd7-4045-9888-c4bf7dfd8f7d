param(
    [Parameter(Mandatory=$true)]
    [string]
    $ExecutablesLocation,

    [Parameter(Mandatory=$true)]
    [string]
    $SourcesDirectory
)

# Get Websign Location
$websignInclude = "WebSign.exe" 
$webSign = Get-ChildItem -Path "$SourcesDirectory/packages/OSIsoft.WebSign/" -Include $websignInclude -Recurse

# Get all the .dll files to sign
Get-ChildItem "$ExecutablesLocation\pkg\**\**\Code\OSIsoft*.dll" | ForEach-Object {
    & $websign[0].FullName /HOST:oakcwss-2.osisoft.int /Retry:3 /ST /R $_.FullName
}

# Get all the .exe files to sign
Get-ChildItem "$ExecutablesLocation\pkg\**\**\Code\OSIsoft*.exe" | ForEach-Object {
    & $websign[0].FullName /HOST:oakcwss-2.osisoft.int /Retry:3 /ST /R $_.FullName
}

# Verify all the .dll files are signed.
Get-ChildItem "$ExecutablesLocation\pkg\**\**\Code\OSIsoft*.dll" | ForEach-Object {
    $acs = Get-AuthenticodeSignature $_.FullName
    Write-Output $acs
    if ($acs.Status -ne 'Valid'){throw $acs.StatusMessage}
}

# Verify all the .exe files are signed.
Get-ChildItem "$ExecutablesLocation\pkg\**\**\Code\OSIsoft*.exe" | ForEach-Object {
    $acs = Get-AuthenticodeSignature $_.FullName
    Write-Output $acs
    if ($acs.Status -ne 'Valid'){throw $acs.StatusMessage}
}