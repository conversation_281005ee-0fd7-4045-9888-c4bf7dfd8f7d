﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <Copyright>© 2023-2025 AVEVA Group Limited or its subsidiaries. All rights reserved.</Copyright>
    <Configurations>Debug;Release;DebugNoCheck;ReleasePolaris</Configurations>
  </PropertyGroup>
  <PropertyGroup>
    <EnableSdkContainerSupport>true</EnableSdkContainerSupport>
    <RootNamespace>Aveva.Platform.Catalog.Events</RootNamespace>
    <AssemblyName>Aveva.Platform.Catalog.Events</AssemblyName>
    <UserSecretsId>07c3aac8-8655-4280-b586-397b98fc9797</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <EnableSdkContainerSupport>true</EnableSdkContainerSupport>
    <ContainerRuntimeIdentifier>linux-x64</ContainerRuntimeIdentifier>
    <ContainerImageName>aveva-platform-catalog-events</ContainerImageName>
    <ContainerImageTag>latest</ContainerImageTag>
    <OutputType>EXE</OutputType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Http" />
    <PackageReference Include="Asp.Versioning.Mvc" />
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" />
    <PackageReference Include="Aveva.Platform.Authentication.Sdk.Server" />
    <PackageReference Include="Aveva.Platform.Common.Abstractions" />
    <PackageReference Include="Aveva.Platform.Common.Framework.Abstractions" />
    <PackageReference Include="Aveva.Platform.Common.Framework.AspNetCore" />
    <PackageReference Include="Aveva.Platform.Common.Framework.ExceptionHandling" />
    <PackageReference Include="Aveva.Platform.Common.Messaging.EventBus" />
    <PackageReference Include="Aveva.Platform.Common.Messaging.EventBus.Events" />
    <PackageReference Include="Aveva.Platform.Common.Monitoring.Instrumentation" />
    <PackageReference Include="Aveva.Platform.Common.Monitoring.HealthChecks" />
    <PackageReference Include="Aveva.Platform.Common.Monitoring.HealthChecks.Dapr" />
    <PackageReference Include="Dapr.Client" />
    <PackageReference Include="Dapr.AspNetCore" />
    <PackageReference Include="Dapr.Extensions.Configuration" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
    <PackageReference Include="Polly" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Platform.Catalog.Domain\Platform.Catalog.Domain.csproj" />
    <ProjectReference Include="..\Platform.Catalog.ServiceClient\Platform.Catalog.ServiceClient.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Aveva.Ruleset">
      <IncludeAssets>runtime; compile; build; native; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>  
</Project>
