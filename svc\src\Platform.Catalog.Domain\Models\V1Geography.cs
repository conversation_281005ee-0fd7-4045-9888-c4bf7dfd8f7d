﻿using System.ComponentModel.DataAnnotations;

namespace Aveva.Platform.Catalog.Domain.Models;

/// <summary>
/// Geography.
/// </summary>
public class V1Geography
{
#pragma warning disable CS8618
    /// <summary>
    /// Gets or sets id.
    /// </summary>
    [Required]
    public string Id { get; set; }
#pragma warning restore CS8618

    /// <inheritdoc/>
    public override bool Equals(object? obj)
    {
        if (obj is not V1Geography item)
        {
            return false;
        }

        var result = string.Equals(item.Id, Id, StringComparison.InvariantCultureIgnoreCase);

        return result;
    }

    /// <inheritdoc/>
    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
}