﻿<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
  <!-- Configurations that affect the Test Framework -->
  <!-- https://github.com/MicrosoftDocs/visualstudio-docs/blob/master/docs/test/configure-unit-tests-by-using-a-dot-runsettings-file.md#configure-unit-tests-by-using-a-runsettings-file -->
  <RunConfiguration>
    <!-- Path relative to solution directory -->
    <ResultsDirectory>.\TestResults</ResultsDirectory>

    <!-- [x86] | x64
      - You can also change it from menu Test, Test Settings, Default Processor Architecture -->
    <TargetPlatform>x64</TargetPlatform>

    <!-- Framework35 | [Framework40] | Framework45 | FrameworkCore10 | FrameworkCore20 | FrameworkCore21 | ... -->
    <!--<TargetFrameworkVersion>Framework45</TargetFrameworkVersion>-->
     
  </RunConfiguration>

  <DeploymentEnabled>False</DeploymentEnabled>

  <!-- Configurations for data collectors -->
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="Code Coverage" uri="datacollector://Microsoft/CodeCoverage/2.0" assemblyQualifiedName="Microsoft.VisualStudio.Coverage.DynamicCoverageDataCollector, Microsoft.VisualStudio.TraceCollector, Version=11.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
        <Configuration>
          <CodeCoverage>

            <ModulePaths>
              <Include>
                <!-- Include all project assemblies -->
                <ModulePath>.*\\Aveva\..*\.dll$</ModulePath>
              </Include>
              <Exclude>
                <!-- Exclude client project assemblies -->
                <ModulePath>.*\\Aveva\..*\.Client\.dll$</ModulePath>
                <!-- Exclude all test project assemblies -->
                <ModulePath>.*\.Tests\.Unit\.dll$</ModulePath>
                <ModulePath>.*\.Tests\.Integration\.dll$</ModulePath>
                <ModulePath>.*\.Tests\.Functional\.dll$</ModulePath>
                <ModulePath>.*\.Tests\.dll$</ModulePath>
                <ModulePath>.*\\Xunit\..*\.dll$</ModulePath>
                <ModulePath>.*\\Moq\.*\.dll$</ModulePath>
                <ModulePath>.*\\Shouldly\.*\.dll$</ModulePath>
                <ModulePath>.*\\emptyfiles\.*\.dll$</ModulePath>
              </Exclude>
            </ModulePaths>

            <!-- Match attributes on any code element: -->
            <Attributes>
              <Exclude>
                <!-- Don’t forget "Attribute" at the end of the name -->
                <Attribute>^System.Diagnostics.DebuggerHiddenAttribute$</Attribute>
                <Attribute>^System.Diagnostics.DebuggerNonUserCodeAttribute$</Attribute>
                <Attribute>^System.Runtime.CompilerServices.CompilerGeneratedAttribute$</Attribute>
                <Attribute>^System.CodeDom.Compiler.GeneratedCodeAttribute$</Attribute>
                <Attribute>^System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverageAttribute$</Attribute>
              </Exclude>
            </Attributes>

            <Sources>
              <!-- Exclude service reference generated code: -->
              <Exclude>
                <Source>.*\\Service References\\.*</Source>
              </Exclude>
            </Sources>
          </CodeCoverage>
        </Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>

  <!-- Adapter Specific sections -->

  <!-- MSTest adapter -->
  <MSTest>
  </MSTest>
</RunSettings>