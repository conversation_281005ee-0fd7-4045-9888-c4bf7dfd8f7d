/* tslint:disable */
/* eslint-disable */
// Generated by Microsoft Kiota
// @ts-ignore
import { CatalogRequestBuilderNavigationMetadata, type CatalogRequestBuilder } from './catalog/index.js';
// @ts-ignore
import { type BaseRequestBuilder, type KeysToExcludeForNavigationMetadata, type NavigationMetadata } from '@microsoft/kiota-abstractions';

/**
 * Builds and executes requests for operations under /ops
 */
export interface OpsRequestBuilder extends BaseRequestBuilder<OpsRequestBuilder> {
    /**
     * The catalog property
     */
    get catalog(): CatalogRequestBuilder;
}
/**
 * Uri template for the request builder.
 */
export const OpsRequestBuilderUriTemplate = "{+baseurl}/ops";
/**
 * Metadata for all the navigation properties in the request builder.
 */
export const OpsRequestBuilderNavigationMetadata: Record<Exclude<keyof OpsRequestBuilder, KeysToExcludeForNavigationMetadata>, NavigationMetadata> = {
    catalog: {
        navigationMetadata: CatalogRequestBuilderNavigationMetadata,
    },
};
/* tslint:enable */
/* eslint-enable */
