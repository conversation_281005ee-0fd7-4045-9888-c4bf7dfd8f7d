﻿namespace Aveva.Platform.Catalog.Domain
{
    /// <summary>
    /// Stores all constants used by Platform.Catalog.Domain project.
    /// </summary>
    public static class CatalogConstants
    {
        /// <summary>
        /// Catalog services Connect V2 ServiceId.
        /// </summary>
        public const string ServiceId = "catalog";

        /// <summary>
        /// Kubernetes Group name where custom resource definition lives.
        /// </summary>
        public const string Group = "servicecatalog.aveva.com";

        /// <summary>
        /// Version V1 of CRD.
        /// </summary>
        public const string V1 = "v1";

        /// <summary>
        /// Api version (v1) with the domain name.
        /// </summary>
        public const string ApiV1 = $"{Group}/{V1}";

        /// <summary>
        /// Constants for CataloEntry customresource.
        /// </summary>
#pragma warning disable CA1034
        public static class ServiceEntry
        {
            /// <summary>
            /// Resource Kind in Kubernetes.
            /// </summary>
            public const string Kind = "ServiceEntry";

            /// <summary>
            /// PluralName of the custom resource in Kubernetes.
            /// </summary>
            public const string Plural = "serviceentries";

            /// <summary>
            /// k8s Namespace for catalog.
            /// </summary>
            public const string Namespace = "platform-catalog";
        }
#pragma warning restore CA1034
    }
}