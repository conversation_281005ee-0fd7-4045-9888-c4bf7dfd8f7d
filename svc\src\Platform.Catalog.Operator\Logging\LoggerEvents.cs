﻿using Aveva.Platform.Catalog.Domain.Logging;

namespace Aveva.Platform.Catalog.Operator.Logging;

/// <summary>
/// Pre-defined event identifiers for this application to use with logging.
/// </summary>
public enum LoggerEvents
{
    /// <summary>
    /// None.
    /// </summary>
    None = 0,

    /// <summary>
    /// Create V1 Catalog Service entry event.
    /// </summary>
    CreateV1ServiceEntry = LoggerEventsConstants.OperatorEventIdRangeStartId + 1,

    /// <summary>
    /// Update V1 Catalog Service entry event.
    /// </summary>
    UpdateV1ServiceEntry = LoggerEventsConstants.OperatorEventIdRangeStartId + 2,

    /// <summary>
    /// Failure in create V1 Catalog Service entry event.
    /// </summary>
    CreateV1ServiceEntryFailed = LoggerEventsConstants.OperatorEventIdRangeStartId + 3,

    /// <summary>
    /// Failure in update V1 Catalog Service entry event.
    /// </summary>
    UpdateV1ServiceEntryFailed = LoggerEventsConstants.OperatorEventIdRangeStartId + 4,

    /// <summary>
    /// Delete V1 Catalog Service entry event.
    /// </summary>
    DeleteV1ServiceEntry = LoggerEventsConstants.OperatorEventIdRangeStartId + 5,

    /// <summary>
    /// Failure in Delete V1 Catalog Service entry event.
    /// </summary>
    DeleteV1ServiceEntryFailed = LoggerEventsConstants.OperatorEventIdRangeStartId + 6,
}