﻿namespace Aveva.Platform.Catalog.Domain.DataTransferObjects.Common.v2
{
    /// <summary>
    /// Defines the mapping for a dependency in the legacy protocol. This mapping connects modern service dependencies to the legacy Solution and Capability Management (SCM) integration system.
    /// </summary>
    public class LegacyProtocolDependencyMapping
    {
        /// <summary>
        /// The integration definition name this corresponds to in the legacy system. This identifier matches the integration definition in the legacy SCM system that this dependency mapping represents.
        /// </summary>
        public required string IntegrationDefinition { get; init; }

        /// <summary>
        /// The identifier of the configuration parameter whose value should be provided as the `sourceContext` in the legacy protocol. This specifies which configuration value from the dependency should be used as the source context in legacy integrations.
        /// </summary>
        public string? SourceContextConfig { get; init; }

        /// <summary>
        /// The identifier of the configuration parameter whose value should be provided as the `targetContext` in the legacy protocol. This specifies which configuration value from the dependency should be used as the target context in legacy integrations.
        /// </summary>
        public string? TargetContextConfig { get; init; }

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is LegacyProtocolDependencyMapping item
                && string.Equals(item.IntegrationDefinition, IntegrationDefinition, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.SourceContextConfig, SourceContextConfig, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.TargetContextConfig, TargetContextConfig, StringComparison.InvariantCultureIgnoreCase);
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}