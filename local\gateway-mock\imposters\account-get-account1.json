{"responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": "{\"id\": \"account1\",\"aliasId\": \"account1\",\"name\": \"account1\",\"geography\": \"eu\",\"status\": \"Active\",\"type\": \"Internal\",\"createdDate\": \"2025-06-02T14:32:23.951Z\",\"modifiedDate\": \"2025-06-02T14:32:23.951Z\",\"accountManagers\": [\"<EMAIL>\"],\"synced\": true,\"allowedGeographies\": [\"eu\"]}"}}], "predicates": [{"equals": {"method": "GET", "path": "/ops/accountMgmt/v1/accounts/account1"}}]}