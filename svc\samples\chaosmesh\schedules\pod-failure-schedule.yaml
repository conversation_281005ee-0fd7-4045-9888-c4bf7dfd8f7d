apiVersion: chaos-mesh.org/v1alpha1
kind: Schedule
metadata:
  name: schedule-pod-failure
  namespace: chaos-mesh
spec:
  schedule: "@every 2m"
  type: "PodChaos"
  historyLimit: 5
  concurrencyPolicy: Forbid
  podChaos:
    action: pod-failure
    mode: all
    selector:
      namespaces:
        - platform-catalog
      labelSelectors:
        operator: aveva-platform-catalog-operator # match the labels in deployment
    duration: "1m"
