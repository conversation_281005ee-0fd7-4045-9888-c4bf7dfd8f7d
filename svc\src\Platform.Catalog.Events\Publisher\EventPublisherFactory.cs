﻿using System.Data;
using Aveva.Platform.Catalog.Domain.Contracts;
using Aveva.Platform.Catalog.Domain.Instrumentation;
using Aveva.Platform.Catalog.ServiceClient.Catalog;
using Aveva.Platform.Common.Framework.Abstractions.Mapping;
using Aveva.Platform.Common.Messaging.EventBus.Abstractions;
using Polly;
using Polly.Retry;

namespace Aveva.Platform.Catalog.Events.Publisher
{
    /// <summary>
    /// Catalog ServiceEntry create event publisher class.
    /// </summary>
    public class EventPublisherFactory : IEventPublisherFactory
    {
        private const string GeoEmptyErrorMessage = "Catalog event service deployed Geography is null or empty";
        private const string RegionEmptyErrorMessage = "Catalog event service deployed region is null or empty";
        private static readonly RetryStrategyOptions _retryStrategyOptions = new RetryStrategyOptions()
        {
            BackoffType = DelayBackoffType.Exponential,
            MaxRetryAttempts = 6,
            Delay = TimeSpan.FromSeconds(5),
        };

        private static readonly ResiliencePipeline _retryPipeline = new ResiliencePipelineBuilder()
            .AddRetry(_retryStrategyOptions)
            .Build();

        private static string _geography = string.Empty;
        private static string _region = string.Empty;
        private readonly ILogger<IEventPublisherFactory> _logger;
        private readonly IEventBus _eventBus;
        private readonly ICatalogClient _catalogClient;
        private readonly ITypeMappingService _typeMappingService;
        private readonly EventMetrics _metrics;

        /// <summary>
        /// Initializes a new instance of the <see cref="EventPublisherFactory"/> class.
        /// </summary>
        /// <param name="eventBus">Event bus to publish event.</param>
        /// <param name="configuration">Catalog event service configuration.</param>
        /// <param name="catalogClient">Catalog Api client.</param>
        /// <param name="logger">Instance of Ilogger(IEventPublisherFacory).</param>
        /// <param name="typeMapper">Instance of ITypeMappingService for domain model mapping.</param>
        /// <param name="metrics">EventMetrics class for instrumentation.</param>
        public EventPublisherFactory(
            IEventBus eventBus,
            IConfiguration configuration,
            ICatalogClient catalogClient,
            ILogger<IEventPublisherFactory> logger,
            ITypeMappingService typeMapper,
            EventMetrics metrics)
        {
            ArgumentNullException.ThrowIfNull(eventBus, nameof(eventBus));
            ArgumentNullException.ThrowIfNull(configuration, nameof(configuration));
            ArgumentNullException.ThrowIfNull(catalogClient, nameof(catalogClient));
            ArgumentNullException.ThrowIfNull(typeMapper, nameof(typeMapper));
            ArgumentNullException.ThrowIfNull(metrics, nameof(metrics));

            _eventBus = eventBus;
            _catalogClient = catalogClient;
            _geography = configuration["geography"] ?? throw new NoNullAllowedException(GeoEmptyErrorMessage);
            _region = configuration["region"] ?? throw new NoNullAllowedException(RegionEmptyErrorMessage);
            _logger = logger;
            _typeMappingService = typeMapper;
            _metrics = metrics;
        }

        /// <inheritdoc/>
        public Task<IEventPublisher?> GetEventPublisherAsync(string eventType)
        {
            ArgumentException.ThrowIfNullOrEmpty(eventType, nameof(eventType));
            switch (eventType)
            {
                case "create":
                    return Task.FromResult<IEventPublisher?>(new CatalogServiceAddV1Publisher(_eventBus, _catalogClient, _geography, _region, _retryPipeline, _metrics, _logger));
                case "delete":
                    return Task.FromResult<IEventPublisher?>(new CatalogServiceDeleteV1Publisher(_eventBus, _catalogClient, _geography, _region, _retryPipeline, _metrics, _logger));
                case "update":
                    return Task.FromResult<IEventPublisher?>(new CatalogServiceUpdateV1Publisher(_eventBus, _catalogClient, _geography, _region, _retryPipeline, _typeMappingService, _metrics, _logger));
                default:
                    return Task.FromResult<IEventPublisher?>(null);
            }
        }
    }
}