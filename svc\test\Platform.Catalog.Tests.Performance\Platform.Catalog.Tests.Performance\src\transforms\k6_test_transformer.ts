import { existsSync, readFileSync, mkdirSync } from 'fs';
import { Options } from 'k6/options';
import path = require('path');
import * as ts from 'typescript'; // todo: why does the IDE not like this?

/*
 * TypeScript Transformer (see https://github.com/madou/typescript-transformer-handbook#transformer-api )
 * Adds exported functions to each test script, providing the hooks needed for K6 and defined by the abstract base class
 *
 * Runs during transpilation (using ttsc or another compiler; tsc does not support this) and modifies the TS source (in-memory, changes are not persisted)
 *
 *
 * Example; given this simple test source file (`src/tests/demo/fake.ts`) and a corresponding `.options.json` file defining a single scenario with `"exec": "test"`:
 * ```
 * import { k6TestBase } from '@utils/k6TestBase';
 * import { get as demo_getCrocodiles } from '@utils/demo/k6io-crocodiles';
 *
 * export class FakeTest extends k6TestBase {
 *     constructor() {
 *         super();
 * 	   }
 *     public defaultScenarioTestIteration(_data: any): void {
 *         this.test();
 *     }
 *     public test() {
 *         demo_getCrocodiles();
 * 	   }
 * }
 * ```
 *
 * Transpiling will produce a matching JS file (`dist/test/demo/fake.js`) with this content appended after the class:
 * ```
 * K6TestBase.testName = "demo/fake";
 * const testInstance_1 = new FakeTest();
 * export function setup() {
 *     return testInstance_1.sharedTestSetup();
 * }
 * export default function defaultScenarioTest(data) {
 *     testInstance_1.defaultScenarioTestIteration(data);
 * }
 * export function teardown(data) {
 *     testInstance_1.sharedTestTeardown(data);
 * }
 * export const options = K6TestBase.loadTestOptions();
 * export function test() {
 *     if ("test" in testInstance_1) {
 *         testInstance_1.test();
 *     }
 *     else {
 *         console.log("Implementation missing for configured scenario, expecting function named " + "test");
 *     }
 * }
 * ```
 */


/*
 * Converts the path to a test script file into a fully-qualified name for the test
 *
 * e.g. `C:/code/repo/src/tests/foo/bar.ts` => `foo/bar` ; `.../dist/tests/bar/foo.js` => `bar/foo`
 */
export function getTestNameFromPath(testScriptPath: string) {
    const extnName = path.extname(testScriptPath)
    const filePath = testScriptPath;
    let testName: string = '';

    if (filePath.includes('tests/')) {
        testName = testScriptPath.substring(testScriptPath.lastIndexOf('tests/') + 6);
    }

    testName = testName?.substring(0, testName?.length - extnName.length);

    return testName;
}

const k6BaseTestClassName = 'K6TestBase';
const baseTestClassName = 'TestBase';

const transformer: ts.TransformerFactory<ts.SourceFile> = (context) => {
  return (sourceFile) => {
    const helperScriptVisitor = (node: ts.Node): ts.VisitResult<ts.Node> => {
      //console.log(`Visiting Helper ${node.kind} of ${sourceFile.fileName} (${ts.SyntaxKind[node.kind]})`);

      const isImportDeclaration = ts.isImportDeclaration(node);
      if (isImportDeclaration) {
        return handleImportDeclaration(node);
      }

      return node;
    };

    const testScriptVisitor = (node: ts.Node): ts.VisitResult<ts.Node> => {
      //console.log(`Visiting Test ${node.kind} of ${sourceFile.fileName} (${ts.SyntaxKind[node.kind]})`);

      const isTestClassNode =
        ts.isClassDeclaration(node) &&
        node.heritageClauses.length === 1 &&
        node.heritageClauses[0].types[0].expression.escapedText.endsWith(baseTestClassName);

      if (isTestClassNode) {
        const testClassName = node.name as ts.Identifier;
        const testName = getTestNameFromPath(sourceFile.fileName);
        const nodes = createTestInvocationNodes(node, testClassName, testName);
        const baseClassName = node.heritageClauses[0].types[0].expression.escapedText;

        // if base class is not the K6TestBase class, we still need to import it for the .js file
        if (baseClassName !== k6BaseTestClassName) {
            // Directly reference the k6_test_base.js file from the node_modules directory
            nodes.unshift(ts.factory.createImportDeclaration(
                undefined,
                ts.factory.createImportClause(
                    false,
                    undefined,
                    ts.factory.createNamedImports([
                        ts.factory.createImportSpecifier(false, undefined, ts.factory.createIdentifier(k6BaseTestClassName))
                    ])
                ),
                ts.factory.createStringLiteral('@platform/performance-libs/utils/common/k6_test_base.js'),
                undefined
            ));
        }

        //console.log(
        //    `Transformer found test class '${testClassName.escapedText}'; adding ${
        //        nodes.length - 1
        //    } new AST node(s) before transpilation.`
        //);
        return nodes;
      } else {
        const isImportDeclaration = ts.isImportDeclaration(node);
        if (isImportDeclaration) {
          return handleImportDeclaration(node);
        }

        return node;
      }
    };

    const sourceFileVisitor = (node: ts.Node): ts.Node => {
      //console.log(`Transformer visiting ${ts.SyntaxKind[node.kind]} \t ${sourceFile.fileName}`);

      const isTypeScript = sourceFile.fileName.endsWith('.ts');
      if (!isTypeScript) {
        return node;
      }

      const isTestScript = sourceFile.fileName.includes('/src/tests/');
      if (isTestScript) {
        if (!existsSync("./test_output"))
          mkdirSync("./test_output");
        return ts.visitEachChild(node, testScriptVisitor, context);
      }

      const isHelper = sourceFile.fileName.includes('/src/utils/');
      if (isHelper) {
        return ts.visitEachChild(node, helperScriptVisitor, context);
      }

      return node;
    };

    // begin traversal
    return ts.visitNode(sourceFile, sourceFileVisitor);
  };
};

function handleImportDeclaration(node: ts.ImportDeclaration): ts.ImportDeclaration {
  const importModuleSpecifier = (node.moduleSpecifier as ts.StringLiteral).text;
  //console.log(`\tImport Module specifier text is '${importModuleSpecifier}'`);  

  const isLocalReference = importModuleSpecifier.startsWith('../') || importModuleSpecifier.startsWith('./');

  // any local script references should end in .js
  if (isLocalReference && !importModuleSpecifier.endsWith('.js')) {
    //console.log(
    //    `Found importDeclaration to local script without extension: '${node.kind}': ${importModuleSpecifier}`
    //);

    const replacementNode = ts.factory.createImportDeclaration(
      undefined,
      node.importClause,
      ts.factory.createStringLiteral(importModuleSpecifier + '.js'),
      undefined
    );

    //console.log(`\tAttempting to change to '${replacementNode.moduleSpecifier.text}'`);
    return replacementNode;
  }

  return node;
}

/*
 * Creates new AST nodes needed to export a test class' abstracted implementation
 *
 * For assistance in authoring new nodes here:
 *	1. Manually author the TypeScript you want
 *	2. Paste the complete TS into https://ts-ast-viewer.com/
 *	3. Select the new nodes in the top left, and copy the generated factory code from the bottom left
 *	4. Push the ts.Node created by the factory onto the return array
 */
function createTestInvocationNodes(node: ts.Node, testClassName: ts.Identifier, testName: string): ts.Node[] {
  const nodes: ts.Node[] = [node];
  const testInstanceIdentifier = ts.factory.createUniqueName('testInstance');

  //set testName static property
  nodes.push(
    ts.factory.createExpressionStatement(
      ts.factory.createBinaryExpression(
        ts.factory.createPropertyAccessExpression(
          ts.factory.createIdentifier(k6BaseTestClassName),
          ts.factory.createIdentifier('testName')
        ),
        ts.factory.createToken(ts.SyntaxKind.EqualsToken),
        ts.factory.createStringLiteral(testName)
      )
    )
  );

  // instantiate class
  nodes.push(
    ts.factory.createVariableStatement(
      undefined,
      ts.factory.createVariableDeclarationList(
        [
          ts.factory.createVariableDeclaration(
            testInstanceIdentifier,
            undefined,
            undefined,
            ts.factory.createNewExpression(testClassName, undefined, [])
          )
        ],
        ts.NodeFlags.Const
      )
    )
  );

  // export `setup()`
  nodes.push(
    ts.factory.createFunctionDeclaration(
      [ts.factory.createModifier(ts.SyntaxKind.ExportKeyword)],
      undefined,
      ts.factory.createIdentifier('setup'),
      undefined,
      [],
      undefined,
      ts.factory.createBlock(
        [
          ts.factory.createReturnStatement(
            ts.factory.createCallExpression(
              ts.factory.createPropertyAccessExpression(
                testInstanceIdentifier,
                ts.factory.createIdentifier('sharedTestSetup')
              ),
              undefined,
              []
            )
          )
        ],
        true
      )
    )
  );

  // export default function for running test without a scenario exec
  nodes.push(
    ts.factory.createFunctionDeclaration(
      [
        ts.factory.createModifier(ts.SyntaxKind.ExportKeyword),
        ts.factory.createModifier(ts.SyntaxKind.DefaultKeyword)
      ],
      undefined,
      ts.factory.createIdentifier('defaultScenarioTest'),
      undefined,
      [
        ts.factory.createParameterDeclaration(
          undefined,
          undefined,
          ts.factory.createIdentifier('data'),
          undefined,
          ts.factory.createUnionTypeNode([
            ts.factory.createKeywordTypeNode(ts.SyntaxKind.AnyKeyword),
            ts.factory.createKeywordTypeNode(ts.SyntaxKind.UndefinedKeyword)
          ]),
          undefined
        )
      ],
      undefined,
      ts.factory.createBlock(
        [
          ts.factory.createExpressionStatement(
            ts.factory.createCallExpression(
              ts.factory.createPropertyAccessExpression(
                testInstanceIdentifier,
                ts.factory.createIdentifier('defaultScenarioTestIteration')
              ),
              undefined,
              [ts.factory.createIdentifier('data')]
            )
          )
        ],
        true
      )
    )
  );

  // export `teardown()`
  nodes.push(
    ts.factory.createFunctionDeclaration(
      [ts.factory.createModifier(ts.SyntaxKind.ExportKeyword)],
      undefined,
      ts.factory.createIdentifier('teardown'),
      undefined,
      [
        ts.factory.createParameterDeclaration(
          undefined,
          undefined,
          ts.factory.createIdentifier('data'),
          undefined,
          ts.factory.createUnionTypeNode([
            ts.factory.createKeywordTypeNode(ts.SyntaxKind.AnyKeyword),
            ts.factory.createKeywordTypeNode(ts.SyntaxKind.UndefinedKeyword)
          ]),
          undefined
        )
      ],
      undefined,
      ts.factory.createBlock(
        [
          ts.factory.createExpressionStatement(
            ts.factory.createCallExpression(
              ts.factory.createPropertyAccessExpression(
                testInstanceIdentifier,
                ts.factory.createIdentifier('sharedTestTeardown')
              ),
              undefined,
              [ts.factory.createIdentifier('data')]
            )
          )
        ],
        true
      )
    )
  );

  // export `handleSummary()`
  nodes.push(
    ts.factory.createFunctionDeclaration(
      [ts.factory.createModifier(ts.SyntaxKind.ExportKeyword)],
      undefined,
      ts.factory.createIdentifier('handleSummary'),
      undefined,
      [
        ts.factory.createParameterDeclaration(
          undefined,
          undefined,
          ts.factory.createIdentifier('data'),
          undefined,
          ts.factory.createUnionTypeNode([
            ts.factory.createKeywordTypeNode(ts.SyntaxKind.AnyKeyword),
            ts.factory.createKeywordTypeNode(ts.SyntaxKind.UndefinedKeyword)
          ]),
          undefined
        )
      ],
      undefined,
      ts.factory.createBlock(
        [
          ts.factory.createReturnStatement(
            ts.factory.createCallExpression(
              ts.factory.createPropertyAccessExpression(
                testInstanceIdentifier,
                ts.factory.createIdentifier('sharedHandleSummary')
              ),
              undefined,
              [ts.factory.createIdentifier('data')]
            )
          )
        ],
        true
      )
    )
  );

  // export `options`
  nodes.push(
    ts.factory.createVariableStatement(
      [ts.factory.createModifier(ts.SyntaxKind.ExportKeyword)],
      ts.factory.createVariableDeclarationList(
        [
          ts.factory.createVariableDeclaration(
            ts.factory.createIdentifier('options'),
            undefined,
            undefined,
            ts.factory.createCallExpression(
              ts.factory.createPropertyAccessExpression(
                ts.factory.createIdentifier(k6BaseTestClassName),
                ts.factory.createIdentifier('loadTestOptions')
              ),
              undefined,
              undefined
            )
          )
        ],
        ts.NodeFlags.Const
      )
    )
  );

  // export each function expected by scenario config
  getConfiguredScenarioExecs(node).forEach((execName) => {
    nodes.push(
      ts.factory.createFunctionDeclaration(
        [ts.factory.createModifier(ts.SyntaxKind.ExportKeyword)],
        undefined,
        ts.factory.createIdentifier(execName),
        undefined,
        [
          ts.factory.createParameterDeclaration(
            undefined,
            undefined,
            ts.factory.createIdentifier('data'),
            undefined,
            ts.factory.createUnionTypeNode([
              ts.factory.createKeywordTypeNode(ts.SyntaxKind.AnyKeyword),
              ts.factory.createKeywordTypeNode(ts.SyntaxKind.UndefinedKeyword)
            ]),
            undefined
          )
        ],
        undefined,
        ts.factory.createBlock(
          [
            ts.factory.createIfStatement(
              ts.factory.createBinaryExpression(
                ts.factory.createStringLiteral(execName),
                ts.factory.createToken(ts.SyntaxKind.InKeyword),
                testInstanceIdentifier
              ),
              ts.factory.createBlock(
                [
                  ts.factory.createExpressionStatement(
                    ts.factory.createCallExpression(
                      ts.factory.createPropertyAccessExpression(
                        testInstanceIdentifier,
                        ts.factory.createIdentifier(execName)
                      ),
                      undefined,
                      [ts.factory.createIdentifier('data')]
                    )
                  )
                ],
                true
              ),
              ts.factory.createBlock(
                [
                  ts.factory.createExpressionStatement(
                    ts.factory.createCallExpression(
                      ts.factory.createPropertyAccessExpression(
                        ts.factory.createIdentifier('console'),
                        ts.factory.createIdentifier('log')
                      ),
                      undefined,
                      [
                        ts.factory.createBinaryExpression(
                          ts.factory.createStringLiteral(
                            'Implementation missing for configured scenario, expecting function named '
                          ),
                          ts.factory.createToken(ts.SyntaxKind.PlusToken),
                          ts.factory.createStringLiteral(execName)
                        )
                      ]
                    )
                  )
                ],
                true
              )
            )
          ],
          true
        )
      )
    );
  });

  return nodes;
}

function getConfiguredScenarioExecs(node: ts.Node): string[] {
  const execs: string[] = [];

  const sourceFile = node.getSourceFile();
  if (!sourceFile || !sourceFile.fileName) {
    console.log(`Transformer error reading sourceFile of '${node}': '${sourceFile}' to read configured scenarios.`);
    return execs;
  }

  const sourceFilePath = sourceFile.fileName;
  const testOptions = loadTestConfigFileThroughNode(sourceFilePath);

  if (testOptions?.scenarios) {
    for (const scenarioName in testOptions.scenarios) {
      if (Object.prototype.hasOwnProperty.call(testOptions.scenarios, scenarioName)) {
        const scenario = testOptions.scenarios[scenarioName];
        if ('exec' in scenario) {
          execs.push(scenario.exec as string);
        }
      }
    }
  }

  return execs;
}

// based on function from k6TestBase.ts, but uses Node not K6 to read file
function loadTestConfigFileThroughNode(testScriptPath: string): Options | undefined {
  const rootDir = process.env.npm_config_local_prefix;
  const pipelineName = process.env.PIPELINENAME;
  let pathToOptionsFile = '';
  const devOptionsFile = `${rootDir}/src/tests/${getTestNameFromPath(testScriptPath)}.dev.options.json`;
  const normalOptionsFile = `${rootDir}/src/tests/${getTestNameFromPath(testScriptPath)}.options.json`;
  if (pipelineName === 'fullTestSuite' && existsSync(devOptionsFile)) {
    pathToOptionsFile = devOptionsFile;
  } else {
    pathToOptionsFile = normalOptionsFile;
  }

  let testSpecificOptions: any = '{}';
  try {
    testSpecificOptions = readFileSync(pathToOptionsFile);
  } catch {
    console.log(`Transformer unable to open test options from '${pathToOptionsFile}'`);
  }

  try {
    return JSON.parse(testSpecificOptions) as Options;
  } catch {
    console.log(
      `Transformer unable to parse "${testSpecificOptions}" from "${pathToOptionsFile}" as test options for "${testScriptPath}"`
    );
    return undefined;
  }
}

function getK6BaseClassImportStatementNode(fileDepth: number): ts.Node {
  let relativePath = '';
  for (let i = 0; i < fileDepth; i++) {
    relativePath += '../';
  }
  return ts.factory.createImportDeclaration(
    undefined,
    ts.factory.createImportClause(
      false,
      undefined,
      ts.factory.createNamedImports([
        ts.factory.createImportSpecifier(false, undefined, ts.factory.createIdentifier(k6BaseTestClassName))
      ])
    ),
    ts.factory.createStringLiteral(relativePath + 'utils/common/k6_test_base.js'),
    undefined
  );
}

export default transformer;
