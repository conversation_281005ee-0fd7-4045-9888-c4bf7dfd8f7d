﻿using System.ComponentModel.DataAnnotations;

namespace Aveva.Platform.Catalog.Domain.Models
{
    /// <summary>
    /// V1LegacyProtocolMappingsApplication.
    /// </summary>
    public class V1LegacyProtocolMappingsApplication
    {
        /// <summary>
        /// Gets or sets the name of the application.
        /// </summary>
        /// <remarks>
        /// This is the name of the application as on the <see cref="V1ServiceEntry"/>.
        /// </remarks>
        [Required]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        public string Name { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.

        /// <summary>
        /// Gets or sets the CapabilityDefinition.
        /// </summary>
        /// <remarks>
        /// This is the name of the CapabilityDefinition in Solution and Capability Management to which this application corresponds.
        /// </remarks>
        [Required]
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        public string CapabilityDefinition { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.

        /// <inheritdoc/>
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            return obj is V1LegacyProtocolMappingsApplication item
                && string.Equals(item.Name, Name, StringComparison.InvariantCultureIgnoreCase)
                && string.Equals(item.CapabilityDefinition, CapabilityDefinition, StringComparison.InvariantCultureIgnoreCase);
        }

        /// <inheritdoc/>
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
    }
}