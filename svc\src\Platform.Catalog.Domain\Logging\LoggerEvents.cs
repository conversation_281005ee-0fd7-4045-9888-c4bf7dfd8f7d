﻿using Aveva.Platform.Catalog.Domain.Models;

namespace Aveva.Platform.Catalog.Domain.Logging;

/// <summary>
/// Pre-defined event identifiers for this application to use with logging.
/// </summary>
public enum LoggerEvents
{
    /// <summary>
    /// None.
    /// </summary>
    None = 0,

    /// <summary>
    /// Failed validation while attempting to create a <see cref="V1ServiceEntry"/>.
    /// </summary>
    CreateCatalogFailedValidation = LoggerEventsConstants.DomainEventIdRangeStartId,

    /// <summary>
    /// Failed validation while attempting to create or update a <see cref="V1ServiceEntry"/>.
    /// </summary>
    CreateOrUpdateCatalogFailedValidation = LoggerEventsConstants.DomainEventIdRangeStartId + 1,

    /// <summary>
    /// Failed to delete an existing <see cref="V1ServiceEntry"/>.
    /// </summary>
    DeleteExistingCatalogFailed = LoggerEventsConstants.DomainEventIdRangeStartId + 2,
}