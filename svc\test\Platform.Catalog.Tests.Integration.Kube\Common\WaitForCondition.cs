﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Logging;

namespace Aveva.Platform.Catalog.Tests.Integration.Kube.Common
{
    internal static class WaitForCondition
    {
        private static readonly ILogger _logger = LoggerFactory.Create(builder =>
        {
            builder.AddConsole();
        }).CreateLogger(typeof(WaitForCondition));

        [SuppressMessage("Performance", "CA1848:Use the LoggerMessage delegates", Justification = "Not applicable", Scope = "module")]
        [SuppressMessage("Usage", "CA2254:Template should be a static expression", Justification = "Not applicable", Scope = "module")]
        public static async Task Until(Func<Task<bool>> condition, int timeout = 5000)
        {
            var deadline = DateTime.UtcNow.AddMilliseconds(timeout);
            while (DateTime.UtcNow < deadline)
            {
                var isConditionMet = await condition().ConfigureAwait(true);
                if (isConditionMet)
                {
                    return;
                }

                _logger.LogWarning($"The condition {condition.Method.Name} was not met retrying!");
                await Task.Delay(1000).ConfigureAwait(true);
            }

            throw new TimeoutException($"Condition '{condition.Method.Name}' not met within timeout");
        }

        [SuppressMessage("Performance", "CA1848:Use the LoggerMessage delegates", Justification = "Not applicable", Scope = "module")]
        [SuppressMessage("Usage", "CA2254:Template should be a static expression", Justification = "Not applicable", Scope = "module")]
        public static async Task<T> Until<T>(Func<Task<Tuple<T, bool>>> condition, int timeout = 5000)
        {
            var deadline = DateTime.UtcNow.AddMilliseconds(timeout);
            while (DateTime.UtcNow < deadline)
            {
                var executionResult = await condition().ConfigureAwait(true);
                if (executionResult.Item2)
                {
                    return executionResult.Item1;
                }

                _logger.LogWarning($"The condition {condition.Method.Name} was not met retrying!");
                await Task.Delay(1000).ConfigureAwait(true);
            }

            throw new TimeoutException($"Condition '{condition.Method.Name}' not met within timeout");
        }
    }
}