﻿using Aveva.Platform.AccountMgmt.Client.Ops;
using Aveva.Platform.Catalog.Tests.PerformanceSetup.Models;
using Aveva.Platform.Catalog.Tests.PerformanceSetup.Utilities;
using Microsoft.Kiota.Abstractions.Authentication;
using Microsoft.Kiota.Http.HttpClientLibrary;

namespace Aveva.Platform.Catalog.Tests.PerformanceSetup;

internal sealed class PerformanceSetupFlow
{
    private static async Task Main(string[] args)
    {
        try
        {
            if (args.Length > 0)
            {
                if (args[0].ToString().Equals(Constants.PERFORMANCESETUP, StringComparison.CurrentCultureIgnoreCase))
                {
                    await PerformanceSetup().ConfigureAwait(false);
                }
                else if (args[0].ToString().Equals(Constants.PERFORMANCETEARDOWN, StringComparison.CurrentCultureIgnoreCase))
                {
                    await PerformanceTeardown().ConfigureAwait(false);
                }
                else if (args[0].ToString().Equals(Constants.ACCOUNTDETAILS, StringComparison.CurrentCultureIgnoreCase))
                {
                    await AccountDetails().ConfigureAwait(false);
                }
                else
                {
                    PerformanceSetupUtilities.Print(Constants.HINT);
                }
            }
            else
            {
                await PerformanceSetup().ConfigureAwait(false);
            }
        }
        catch (Exception ex)
        {
            PerformanceSetupUtilities.GetExceptionMessage(ex);
            throw;
        }
    }

    #region Private methods

    /// <summary>
    /// Method for Account creation.
    /// </summary>
    /// <returns>void.</returns>
    private static async Task PerformanceSetup()
    {
        var environmentDetails = await PerformanceSetupUtilities.GetEnvironmentDetails().ConfigureAwait(false);
        var activeAccountDetails = new List<ActiveAccountDetails>();
        PerformanceSetupUtilities.Print(Constants.ACTIVEACCOUNTS);

        var authProvider = new ApiKeyAuthenticationProvider(apiKey: environmentDetails?.CurrentToken!, parameterName: Constants.AUTHORIZATION, keyLocation: ApiKeyAuthenticationProvider.KeyLocation.Header);

        using var adapter = new HttpClientRequestAdapter(authProvider);
        adapter.BaseUrl = environmentDetails?.BaseUrllDetails;

        var client = new Client(adapter);
        try
        {
            if (environmentDetails != null && environmentDetails.Accounts?.Count > 0)
            {
                foreach (var account in environmentDetails.Accounts)
                {
                    var accountDetail = await PerformanceSetupUtilities.GetAccountDetails(client, PerformanceSetupUtilities.GetQueryParam(account, Constants.GET)).ConfigureAwait(false);

                    if (string.IsNullOrEmpty(accountDetail?.AccountName))
                    {
                        PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTCREATION, account?.Name));

                        #region New Account Creation

                        accountDetail = await PerformanceSetupUtilities.GetResponse(Constants.POST, accountDetail!, client, account!).ConfigureAwait(false);

                        if (!string.IsNullOrEmpty(accountDetail?.AccountName)
                            && string.Equals(accountDetail?.Status, Constants.ACTIVE, StringComparison.OrdinalIgnoreCase))
                        {
                            PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTCREATIONSUCCESS, account?.Name, accountDetail?.AccountGUID));
                        }
                        else
                        {
                            PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTCREATIONFAILED, account?.Name));
                        }
                        #endregion

                    }
                    else
                    {
                        PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTALREADYEXIST, account?.Name, accountDetail?.Status));
                    }

                    if (string.Equals(accountDetail?.Status, Constants.ACTIVE, StringComparison.OrdinalIgnoreCase))
                    {
                        var activeAccountDetail = new ActiveAccountDetails
                        {
                            AccountName = account?.Name,
                            AccountGUID = accountDetail?.AccountGUID,
                        };

                        activeAccountDetails.Add(activeAccountDetail);
                    }
                }

                if (activeAccountDetails.Count > 0)
                {
                    PerformanceSetupUtilities.CreateFile(activeAccountDetails);
                }
                else
                {
                    PerformanceSetupUtilities.Print(Constants.NOACCOUNTACTIVE);
                }
            }
            else
            {
                throw new Exception(Constants.ACCOUNTSTATUSDOESNOTEXIST);
            }
        }
        catch (ArgumentNullException ex)
        {
            PerformanceSetupUtilities.GetExceptionMessage(ex);
            throw;
        }
        catch (Exception ex)
        {
            PerformanceSetupUtilities.GetExceptionMessage(ex);
            throw;
        }
    }

    /// <summary>
    /// Method for Account deletion.
    /// </summary>
    /// <returns>Task.</returns>
    private static async Task PerformanceTeardown()
    {
        var environmentDetails = await PerformanceSetupUtilities.GetEnvironmentDetails().ConfigureAwait(false);
        var authProvider = new ApiKeyAuthenticationProvider(apiKey: environmentDetails.CurrentToken!, parameterName: Constants.AUTHORIZATION, keyLocation: ApiKeyAuthenticationProvider.KeyLocation.Header);

        using var adapter = new HttpClientRequestAdapter(authProvider);
        adapter.BaseUrl = environmentDetails.BaseUrllDetails;

        var client = new Client(adapter);
        try
        {
            if (environmentDetails != null && environmentDetails.Accounts?.Count > 0)
            {
                foreach (var account in environmentDetails.Accounts)
                {
                    PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTDELETING, account?.Name));
                    var accountDetail = await PerformanceSetupUtilities.GetAccountDetails(client, PerformanceSetupUtilities.GetQueryParam(account!, Constants.GET)).ConfigureAwait(false);

                    if (string.Equals(account?.Name, accountDetail?.AccountName, StringComparison.OrdinalIgnoreCase))
                    {
                        accountDetail = await PerformanceSetupUtilities.GetResponse(Constants.DELETE, accountDetail!, client, account!).ConfigureAwait(false);
                    }
                    else
                    {
                        PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTDOESNOTEXIST, account?.Name));
                    }
                }
            }
        }
        catch (ArgumentNullException ex)
        {
            PerformanceSetupUtilities.GetExceptionMessage(ex);
            throw;
        }
        catch (Exception ex)
        {
            PerformanceSetupUtilities.GetExceptionMessage(ex);
            throw;
        }
    }

    /// <summary>
    /// Method for fetching account details.
    /// </summary>
    /// <returns>Task.</returns>
    private static async Task AccountDetails()
    {
        var environmentDetails = await PerformanceSetupUtilities.GetEnvironmentDetails().ConfigureAwait(false);
        var authProvider = new ApiKeyAuthenticationProvider(apiKey: environmentDetails.CurrentToken!, parameterName: Constants.AUTHORIZATION, keyLocation: ApiKeyAuthenticationProvider.KeyLocation.Header);
        using var adapter = new HttpClientRequestAdapter(authProvider);
        adapter.BaseUrl = environmentDetails.BaseUrllDetails;

        var client = new Client(adapter);
        try
        {
            if (environmentDetails != null && environmentDetails.Accounts?.Count > 0)
            {
                foreach (var account in environmentDetails.Accounts)
                {
                    PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTFETCHING, account?.Name));
                    var accountDetail = await PerformanceSetupUtilities.GetAccountDetails(client, PerformanceSetupUtilities.GetQueryParam(account!, Constants.GET)).ConfigureAwait(false);
                    if (accountDetail != null)
                    {
                        PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTSTATUSDETAILS, account?.Name, accountDetail?.Status, accountDetail?.CreatedDate, accountDetail?.ModifiedDate));
                    }
                    else
                    {
                        PerformanceSetupUtilities.Print(string.Format(Constants.ACCOUNTDETAILSDOESNOTEXIST, account?.Name));
                    }
                }
            }
        }
        catch (ArgumentNullException ex)
        {
            PerformanceSetupUtilities.GetExceptionMessage(ex);
            throw;
        }
        catch (Exception ex)
        {
            PerformanceSetupUtilities.GetExceptionMessage(ex);
            throw;
        }
    }

    #endregion
}